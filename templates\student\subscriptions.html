{% extends "base.html" %}

{% block title %}اشتراكاتي - {{ academy_name }}{% endblock %}
{% block page_title %}اشتراكاتي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>اشتراكاتي
                </h5>
                <a href="{{ url_for('student.packages') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>اشتراك جديد
                </a>
            </div>
            <div class="card-body">
                {% if subscriptions %}
                    {% for subscription in subscriptions %}
                    <div class="card mb-3 {{ 'border-success' if subscription.status == 'active' else 'border-warning' if subscription.status == 'pending' else 'border-secondary' }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">{{ subscription.package.name }}</h6>
                            <span class="badge {{ 'bg-success' if subscription.status == 'active' else 'bg-info' if subscription.status == 'paid_pending_approval' else 'bg-warning' if subscription.status == 'pending' else 'bg-danger' if subscription.status == 'expired' else 'bg-secondary' }}">
                                {% if subscription.status == 'active' %}نشط
                                {% elif subscription.status == 'paid_pending_approval' %}قيد المراجعة
                                {% elif subscription.status == 'pending' %}معلق
                                {% elif subscription.status == 'expired' %}منتهي
                                {% elif subscription.status == 'cancelled' %}ملغي
                                {% else %}{{ subscription.status }}{% endif %}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>تفاصيل الباقة</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>السعر:</strong></td>
                                            <td>{{ format_currency(subscription.package.price) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>عدد الحصص:</strong></td>
                                            <td>{{ subscription.package.sessions_count }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>مدة الباقة:</strong></td>
                                            <td>{{ subscription.package.duration_days }} يوم</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>حالة الاشتراك</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>تاريخ الشراء:</strong></td>
                                            <td>{{ subscription.purchase_date.strftime('%Y-%m-%d') }}</td>
                                        </tr>
                                        {% if subscription.status == 'active' %}
                                        <tr>
                                            <td><strong>تاريخ التفعيل:</strong></td>
                                            <td>{{ subscription.approval_date.strftime('%Y-%m-%d') if subscription.approval_date else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الانتهاء:</strong></td>
                                            <td>{{ subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else '-' }}</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                            
                            {% if subscription.status == 'active' %}
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6>استخدام الحصص</h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="h5 text-success">{{ subscription.sessions_used or 0 }}</div>
                                            <div class="small text-muted">مستخدمة</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-primary">{{ subscription.sessions_remaining or 0 }}</div>
                                            <div class="small text-muted">متبقية</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-info">{{ subscription.package.sessions_count }}</div>
                                            <div class="small text-muted">إجمالي</div>
                                        </div>
                                    </div>
                                    
                                    {% set usage_percent = (subscription.sessions_used / subscription.package.sessions_count * 100) if subscription.package.sessions_count > 0 else 0 %}
                                    <div class="progress mt-2">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ usage_percent }}%"
                                             aria-valuenow="{{ usage_percent }}" aria-valuemin="0" aria-valuemax="100">
                                            {{ "%.1f"|format(usage_percent) }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if subscription.status == 'pending' %}
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-clock me-2"></i>
                                اشتراكك قيد المراجعة من قبل الإدارة. سيتم تفعيله خلال 24 ساعة.
                            </div>
                            {% endif %}
                            
                            <div class="mt-3">
                                <a href="{{ url_for('student.sessions') }}?subscription={{ subscription.id }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-calendar-alt me-1"></i>عرض الحصص
                                </a>
                                {% if subscription.payments %}
                                <button type="button" class="btn btn-outline-info btn-sm" 
                                        data-bs-toggle="modal" data-bs-target="#paymentModal{{ subscription.id }}">
                                    <i class="fas fa-receipt me-1"></i>تفاصيل الدفع
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد اشتراكات</h5>
                        <p class="text-muted">لم تقم بشراء أي باقة بعد.</p>
                        <a href="{{ url_for('student.packages') }}" class="btn btn-primary">
                            <i class="fas fa-shopping-cart me-2"></i>تصفح الباقات
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Payment Details Modals -->
{% for subscription in subscriptions %}
{% if subscription.payments %}
<div class="modal fade" id="paymentModal{{ subscription.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الدفع - {{ subscription.package.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in subscription.payments %}
                            <tr>
                                <td>{{ format_currency(payment.amount) }}</td>
                                <td>{{ payment.payment_method or 'مراجعة يدوية' }}</td>
                                <td>
                                    <span class="badge {{ 'bg-success' if payment.status == 'completed' else 'bg-warning' if payment.status == 'pending' else 'bg-danger' }}">
                                        {% if payment.status == 'completed' %}مكتمل
                                        {% elif payment.status == 'pending' %}معلق
                                        {% else %}فاشل{% endif %}
                                    </span>
                                </td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
    // Add animation to subscription cards
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
</script>
{% endblock %}
