{% extends "base.html" %}

{% block title %}إدارة المستخدمين - {{ academy_name }}{% endblock %}
{% block page_title %}إدارة المستخدمين{% endblock %}

{% block content %}
<!-- Filters and Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="اسم أو بريد إلكتروني">
                    </div>
                    <div class="col-md-3">
                        <label for="role" class="form-label">الدور</label>
                        <select class="form-select" id="role" name="role">
                            <option value="">جميع الأدوار</option>
                            <option value="admin" {{ 'selected' if role_filter == 'admin' }}>إدارة</option>
                            <option value="teacher" {{ 'selected' if role_filter == 'teacher' }}>معلم</option>
                            <option value="student" {{ 'selected' if role_filter == 'student' }}>طالب</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {{ 'selected' if status_filter == 'pending' }}>معلق</option>
                            <option value="approved" {{ 'selected' if status_filter == 'approved' }}>مقبول</option>
                            <option value="rejected" {{ 'selected' if status_filter == 'rejected' }}>مرفوض</option>
                            <option value="suspended" {{ 'selected' if status_filter == 'suspended' }}>محظور مؤقتاً</option>
                            <option value="banned" {{ 'selected' if status_filter == 'banned' }}>محظور نهائياً</option>
                            <option value="inactive" {{ 'selected' if status_filter == 'inactive' }}>غير نشط</option>
                            <option value="deleted" {{ 'selected' if status_filter == 'deleted' }}>محذوف</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>قائمة المستخدمين
                </h5>
                <span class="badge bg-primary">{{ users.total }} مستخدم</span>
            </div>
            <div class="card-body">
                {% if users.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                             style="width: 32px; height: 32px; font-size: 14px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ user.full_name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.phone or '-' }}</td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {% if user.role == 'admin' %}إدارة
                                        {% elif user.role == 'teacher' %}معلم
                                        {% elif user.role == 'student' %}طالب
                                        {% else %}{{ user.role }}{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if user.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% elif user.status == 'approved' %}
                                        <span class="badge bg-success">مقبول</span>
                                    {% elif user.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                    {% elif user.status == 'suspended' %}
                                        <span class="badge bg-warning">محظور مؤقتاً</span>
                                    {% elif user.status == 'banned' %}
                                        <span class="badge bg-danger">محظور نهائياً</span>
                                    {% elif user.status == 'inactive' %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% elif user.status == 'deleted' %}
                                        <span class="badge bg-dark">محذوف</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ user.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '-' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if user.status == 'pending' %}
                                            <form method="POST" action="{{ url_for('admin.approve_user', user_id=user.id) }}" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                <button type="submit" class="btn btn-sm btn-success"
                                                        onclick="return confirm('هل أنت متأكد من قبول هذا المستخدم؟')"
                                                        title="قبول">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <form method="POST" action="{{ url_for('admin.reject_user', user_id=user.id) }}" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                <button type="submit" class="btn btn-sm btn-danger"
                                                        onclick="return confirm('هل أنت متأكد من رفض هذا المستخدم؟')"
                                                        title="رفض">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </form>
                                        {% elif user.status == 'approved' %}
                                            <!-- Suspend Button -->
                                            <button type="button" class="btn btn-sm btn-warning"
                                                    data-bs-toggle="modal" data-bs-target="#suspendModal{{ user.id }}"
                                                    title="حظر مؤقت">
                                                <i class="fas fa-clock"></i>
                                            </button>
                                            <!-- Ban Button -->
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    data-bs-toggle="modal" data-bs-target="#banModal{{ user.id }}"
                                                    title="حظر نهائي">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            <!-- Disable Button -->
                                            <button type="button" class="btn btn-sm btn-secondary"
                                                    data-bs-toggle="modal" data-bs-target="#disableModal{{ user.id }}"
                                                    title="تعطيل">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        {% elif user.status in ['suspended', 'banned', 'inactive'] %}
                                            <!-- Activate Button -->
                                            <form method="POST" action="{{ url_for('admin.activate_user_route', user_id=user.id) }}" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                <button type="submit" class="btn btn-sm btn-success"
                                                        onclick="return confirm('هل أنت متأكد من تفعيل هذا المستخدم؟')"
                                                        title="تفعيل">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            </form>
                                        {% elif user.status == 'deleted' %}
                                            <!-- Restore Button -->
                                            {% if user.can_be_restored %}
                                            <form method="POST" action="{{ url_for('admin.restore_user_route', user_id=user.id) }}" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                <button type="submit" class="btn btn-sm btn-info"
                                                        onclick="return confirm('هل أنت متأكد من استرداد هذا المستخدم؟')"
                                                        title="استرداد">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                        {% endif %}

                                        <!-- Delete Button (for non-deleted users) -->
                                        {% if user.status != 'deleted' %}
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}"
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}

                                        <!-- View Details Button -->
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                data-bs-toggle="modal" data-bs-target="#userModal{{ user.id }}"
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if users.pages > 1 %}
                <nav aria-label="صفحات المستخدمين">
                    <ul class="pagination justify-content-center">
                        {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num, role=role_filter, status=status_filter, search=search) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.users', page=page_num, role=role_filter, status=status_filter, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.next_num, role=role_filter, status=status_filter, search=search) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مستخدمين</h5>
                    <p class="text-muted">لم يتم العثور على مستخدمين بالمعايير المحددة.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- User Details Modals -->
{% for user in users.items %}
<div class="modal fade" id="userModal{{ user.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>المعلومات الشخصية</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الاسم الكامل:</strong></td>
                                <td>{{ user.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>{{ user.email }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>{{ user.phone or '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>الدور:</strong></td>
                                <td>{{ user.role }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>{{ user.status }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات النشاط</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>تاريخ التسجيل:</strong></td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر دخول:</strong></td>
                                <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'لم يسجل دخول بعد' }}</td>
                            </tr>
                        </table>
                        
                        {% if user.role == 'student' %}
                        <h6>إحصائيات الطالب</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>عدد الاشتراكات:</strong></td>
                                <td>{{ user.subscriptions|length }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الحصص:</strong></td>
                                <td>{{ user.student_sessions|length }}</td>
                            </tr>
                        </table>
                        {% elif user.role == 'teacher' %}
                        <h6>إحصائيات المعلم</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>عدد الحصص:</strong></td>
                                <td>{{ user.teacher_sessions|length }}</td>
                            </tr>
                        </table>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}

<!-- User Management Modals -->
{% for user in users.items %}

<!-- Suspend User Modal -->
<div class="modal fade" id="suspendModal{{ user.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حظر مؤقت - {{ user.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('admin.suspend_user_route', user_id=user.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="suspendReason{{ user.id }}" class="form-label">سبب الحظر <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="suspendReason{{ user.id }}" name="reason" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="suspensionDays{{ user.id }}" class="form-label">مدة الحظر (بالأيام)</label>
                        <input type="number" class="form-control" id="suspensionDays{{ user.id }}" name="suspension_days" min="1" max="365" placeholder="اتركه فارغاً للحظر إلى أجل غير مسمى">
                        <div class="form-text">إذا تركت هذا الحقل فارغاً، سيكون الحظر إلى أجل غير مسمى</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">حظر مؤقت</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ban User Modal -->
<div class="modal fade" id="banModal{{ user.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حظر نهائي - {{ user.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('admin.ban_user_route', user_id=user.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <strong>تحذير:</strong> الحظر النهائي إجراء خطير ولا يمكن التراجع عنه بسهولة.
                    </div>
                    <div class="mb-3">
                        <label for="banReason{{ user.id }}" class="form-label">سبب الحظر النهائي <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="banReason{{ user.id }}" name="reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">حظر نهائي</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Disable User Modal -->
<div class="modal fade" id="disableModal{{ user.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعطيل الحساب - {{ user.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('admin.disable_user_route', user_id=user.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="disableReason{{ user.id }}" class="form-label">سبب التعطيل (اختياري)</label>
                        <textarea class="form-control" id="disableReason{{ user.id }}" name="reason" rows="3" placeholder="اتركه فارغاً إذا لم يكن هناك سبب محدد"></textarea>
                    </div>
                    <div class="alert alert-info">
                        <strong>ملاحظة:</strong> تعطيل الحساب يمنع المستخدم من تسجيل الدخول ويمكن إعادة تفعيله لاحقاً.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">تعطيل الحساب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حذف الحساب - {{ user.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('admin.delete_user_route', user_id=user.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="deleteReason{{ user.id }}" class="form-label">سبب الحذف (اختياري)</label>
                        <textarea class="form-control" id="deleteReason{{ user.id }}" name="reason" rows="3" placeholder="اتركه فارغاً إذا لم يكن هناك سبب محدد"></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="permanentDelete{{ user.id }}" name="permanent" value="true">
                        <label class="form-check-label" for="permanentDelete{{ user.id }}">
                            حذف نهائي من قاعدة البيانات (لا يمكن الاسترداد)
                        </label>
                    </div>
                    <div class="alert alert-warning">
                        <strong>تحذير:</strong> إذا لم تحدد "حذف نهائي"، سيتم حذف الحساب مع إمكانية الاسترداد لاحقاً.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">حذف الحساب</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endfor %}

{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('#role, #status').forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // Clear search
    function clearSearch() {
        document.getElementById('search').value = '';
        document.getElementById('role').value = '';
        document.getElementById('status').value = '';
        document.querySelector('form').submit();
    }
</script>
{% endblock %}
