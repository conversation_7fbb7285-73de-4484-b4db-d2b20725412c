{% extends "base.html" %}

{% block title %}إعدادات Google Calendar - {{ academy_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h3 mb-1">إعدادات Google Calendar</h2>
                    <p class="text-muted">ربط النظام مع Google Calendar لمزامنة الحصص تلقائياً</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('admin.settings') }}">الإعدادات</a></li>
                        <li class="breadcrumb-item active">Google Calendar</li>
                    </ol>
                </nav>
            </div>

            <form method="POST" action="{{ url_for('admin.calendar_settings') }}" id="calendarSettingsForm">
                {{ form.hidden_tag() }}
                <div class="row">
                    <!-- Main Settings -->
                    <div class="col-lg-8">
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fab fa-google me-2"></i>إعدادات الاتصال
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Enable Calendar -->
                                <div class="form-check form-switch mb-4">
                                    {{ form.calendar_enabled(class="form-check-input", id="calendar_enabled") }}
                                    <label class="form-check-label fw-bold" for="calendar_enabled">
                                        تفعيل ربط Google Calendar
                                    </label>
                                    <div class="form-text">عند التفعيل، سيتم إنشاء events في Google Calendar تلقائياً للحصص الجديدة</div>
                                </div>

                                <div id="calendar_config" style="{{ 'display: none;' if not settings.calendar_enabled }}">
                                    <!-- Calendar ID -->
                                    <div class="mb-3">
                                        <label for="calendar_id" class="form-label fw-bold">Calendar ID</label>
                                        {{ form.calendar_id(class="form-control", placeholder="<EMAIL>") }}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle text-info me-1"></i>
                                            Calendar ID الخاص بك (مثل: <EMAIL>)
                                        </div>
                                    </div>

                                    <!-- Service Account Email -->
                                    <div class="mb-3">
                                        <label for="service_account_email" class="form-label fw-bold">Service Account Email</label>
                                        {{ form.service_account_email(class="form-control", placeholder="<EMAIL>") }}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle text-info me-1"></i>
                                            البريد الإلكتروني للـ Service Account المستخدم للاتصال
                                        </div>
                                    </div>

                                    <!-- Test Connection -->
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-outline-primary" id="test_connection">
                                            <i class="fas fa-plug me-2"></i>اختبار الاتصال
                                        </button>
                                        <div id="connection_result" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Event Settings -->
                        <div class="card shadow-sm mb-4" id="event_settings" style="{{ 'display: none;' if not settings.calendar_enabled }}">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-calendar-plus me-2"></i>إعدادات الأحداث
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.auto_create_events(class="form-check-input", id="auto_create_events") }}
                                            <label class="form-check-label" for="auto_create_events">
                                                إنشاء تلقائي للأحداث
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.auto_update_events(class="form-check-input", id="auto_update_events") }}
                                            <label class="form-check-label" for="auto_update_events">
                                                تحديث تلقائي للأحداث
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.auto_delete_events(class="form-check-input", id="auto_delete_events") }}
                                            <label class="form-check-label" for="auto_delete_events">
                                                حذف تلقائي للأحداث
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Event Templates -->
                        <div class="card shadow-sm mb-4" id="event_templates" style="{{ 'display: none;' if not settings.calendar_enabled }}">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-edit me-2"></i>قوالب الأحداث
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Event Title Template -->
                                <div class="mb-3">
                                    <label for="event_title_template" class="form-label fw-bold">قالب عنوان الحدث</label>
                                    {{ form.event_title_template(class="form-control") }}
                                    <div class="form-text">
                                        المتغيرات المتاحة: {student_name}, {teacher_name}, {session_type}, {duration}
                                    </div>
                                </div>

                                <!-- Event Description Template -->
                                <div class="mb-3">
                                    <label for="event_description_template" class="form-label fw-bold">قالب وصف الحدث</label>
                                    {{ form.event_description_template(class="form-control", rows="4") }}
                                    <div class="form-text">
                                        المتغيرات المتاحة: {student_name}, {teacher_name}, {session_type}, {duration}, {meeting_link}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Status Card -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>حالة الاتصال
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center">
                                    {% if settings.calendar_enabled %}
                                        <i class="fas fa-check-circle text-success fa-3x mb-2"></i>
                                        <h6 class="text-success">مفعل</h6>
                                        <p class="text-muted small">Google Calendar متصل</p>
                                    {% else %}
                                        <i class="fas fa-times-circle text-danger fa-3x mb-2"></i>
                                        <h6 class="text-danger">معطل</h6>
                                        <p class="text-muted small">Google Calendar غير متصل</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Help Card -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-question-circle me-2"></i>كيفية الإعداد
                                </h6>
                            </div>
                            <div class="card-body">
                                <ol class="small">
                                    <li>انسخ Calendar ID من Google Calendar</li>
                                    <li>أضف Service Account للتقويم</li>
                                    <li>امنح صلاحية "Make changes to events"</li>
                                    <li>فعل الربط واختبر الاتصال</li>
                                </ol>
                                <div class="alert alert-info small mt-3">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    تأكد من إضافة Service Account Email للتقويم مع صلاحيات التعديل
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.settings') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Toggle calendar configuration
    document.getElementById('calendar_enabled').addEventListener('change', function() {
        const config = document.getElementById('calendar_config');
        const eventSettings = document.getElementById('event_settings');
        const eventTemplates = document.getElementById('event_templates');
        
        if (this.checked) {
            config.style.display = 'block';
            eventSettings.style.display = 'block';
            eventTemplates.style.display = 'block';
        } else {
            config.style.display = 'none';
            eventSettings.style.display = 'none';
            eventTemplates.style.display = 'none';
        }
    });

    // Test connection
    document.getElementById('test_connection').addEventListener('click', function() {
        const button = this;
        const resultDiv = document.getElementById('connection_result');
        
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
        
        fetch('{{ url_for("admin.test_calendar_connection") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'csrf_token=' + encodeURIComponent('{{ csrf_token() }}')
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>${data.message}
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>خطأ في الاتصال: ${error}
                </div>
            `;
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-plug me-2"></i>اختبار الاتصال';
        });
    });
</script>
{% endblock %}
