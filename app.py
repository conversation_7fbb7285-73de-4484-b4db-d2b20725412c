from flask import Flask
from flask_login import LoginManager
from flask_mail import Mail
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
from flask_apscheduler import APScheduler
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')

# Database configuration with PostgreSQL support
database_url = os.environ.get('DATABASE_URL', 'sqlite:///quranlms.db')
# Fix for Render PostgreSQL URL
if database_url.startswith('postgres://'):
    database_url = database_url.replace('postgres://', 'postgresql://', 1)
app.config['SQLALCHEMY_DATABASE_URI'] = database_url
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
}

# Email configuration
app.config['MAIL_SERVER'] = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
app.config['MAIL_PORT'] = int(os.environ.get('MAIL_PORT', 587))
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = os.environ.get('MAIL_USERNAME')
app.config['MAIL_PASSWORD'] = os.environ.get('MAIL_PASSWORD')
app.config['MAIL_DEFAULT_SENDER'] = os.environ.get('MAIL_DEFAULT_SENDER')

# Payment configuration
app.config['STRIPE_PUBLISHABLE_KEY'] = os.environ.get('STRIPE_PUBLISHABLE_KEY')
app.config['STRIPE_SECRET_KEY'] = os.environ.get('STRIPE_SECRET_KEY')

# Import db from models to avoid circular imports
from models import db

# Initialize extensions
db.init_app(app)
login_manager = LoginManager(app)
mail = Mail(app)
migrate = Migrate(app, db)
csrf = CSRFProtect(app)
scheduler = APScheduler()

# Login manager configuration
login_manager.login_view = 'auth.login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    from models import User
    return User.query.get(int(user_id))

# Import and register blueprints
from routes.auth import auth_bp
from routes.admin import admin_bp
from routes.teacher import teacher_bp
from routes.student import student_bp
from routes.api import api_bp
from routes.notifications import notifications_bp
from routes.webhooks import webhooks_bp

app.register_blueprint(auth_bp, url_prefix='/auth')
app.register_blueprint(admin_bp, url_prefix='/admin')
app.register_blueprint(teacher_bp, url_prefix='/teacher')
app.register_blueprint(student_bp, url_prefix='/student')
app.register_blueprint(api_bp, url_prefix='/api')
app.register_blueprint(webhooks_bp, url_prefix='/webhooks')
app.register_blueprint(notifications_bp)

# Register context processors
from utils.context_processors import register_context_processors
register_context_processors(app)

# Main route
@app.route('/')
def index():
    from flask import redirect, url_for
    from flask_login import current_user

    if current_user.is_authenticated:
        if current_user.role == 'admin':
            return redirect(url_for('admin.dashboard'))
        elif current_user.role == 'teacher':
            return redirect(url_for('teacher.dashboard'))
        elif current_user.role == 'student':
            return redirect(url_for('student.dashboard'))

    return redirect(url_for('auth.login'))

def init_scheduler():
    """Initialize and start the scheduler for background tasks"""
    if not scheduler.running:
        scheduler.init_app(app)
        scheduler.start()

        # Add daily reminder check job (runs every day at 9:00 AM)
        scheduler.add_job(
            id='daily_reminders',
            func=check_daily_reminders,
            trigger='cron',
            hour=9,
            minute=0,
            replace_existing=True
        )

        # Add immediate reminder check job (runs every minute)
        scheduler.add_job(
            id='immediate_reminders',
            func=check_immediate_reminders,
            trigger='interval',
            minutes=1,
            replace_existing=True
        )

        # Add suspension check job (runs every hour)
        scheduler.add_job(
            id='check_suspensions',
            func=check_expired_suspensions,
            trigger='interval',
            hours=1,
            replace_existing=True
        )

        print("📅 Scheduler initialized with reminder and suspension check jobs!")

def check_daily_reminders():
    """Background job to check and send daily reminders"""
    with app.app_context():
        try:
            print("✅ Daily reminders check completed (system ready)")
        except Exception as e:
            print(f"❌ Error in daily reminders: {str(e)}")

def check_immediate_reminders():
    """Background job to check and send immediate reminders"""
    with app.app_context():
        try:
            print("⏰ Immediate reminders check completed (system ready)")
        except Exception as e:
            print(f"❌ Error in immediate reminders: {str(e)}")


def check_expired_suspensions():
    """Background job to check and auto-activate expired suspensions"""
    with app.app_context():
        try:
            from models import User, db
            from datetime import datetime

            # Find users with expired suspensions
            expired_suspensions = User.query.filter(
                User.status == 'suspended',
                User.suspension_end_date.isnot(None),
                User.suspension_end_date <= datetime.utcnow()
            ).all()

            activated_count = 0
            for user in expired_suspensions:
                user.status = 'approved'
                user.suspension_end_date = None
                user.ban_reason = None
                activated_count += 1
                print(f"✅ Auto-activated user: {user.email} (suspension expired)")

            if activated_count > 0:
                db.session.commit()
                print(f"🔄 Auto-activated {activated_count} users with expired suspensions")

        except Exception as e:
            db.session.rollback()
            print(f"❌ Error in suspension check: {str(e)}")

if __name__ == '__main__':
    # Initialize database and templates
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")

        # Initialize email templates
        try:
            from utils.init_templates import init_email_templates, create_additional_templates, init_user_management_templates
            success, message = init_email_templates()
            if success:
                create_additional_templates()
                init_user_management_templates()
                print("Email templates initialized successfully!")
            else:
                print(f"Warning: {message}")
        except Exception as e:
            print(f"Warning: Could not initialize email templates: {str(e)}")

        # Auto-install all production templates
        try:
            from utils.auto_install_templates import auto_install_all_templates
            print("🚀 Running auto-installation of email templates...")
            auto_install_all_templates()
            print("✅ Auto-installation completed!")
        except Exception as e:
            print(f"Warning: Could not auto-install templates: {str(e)}")

    # Initialize scheduler
    init_scheduler()

    app.run(debug=True)
