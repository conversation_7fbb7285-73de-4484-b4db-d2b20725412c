#!/usr/bin/env python3
"""
فحص سريع للتأكد من جاهزية التطبيق للإنتاج
"""

import os
import sys

def check_files():
    """فحص وجود الملفات المطلوبة"""
    required_files = [
        'app.py',
        'requirements.txt',
        'Procfile',
        'runtime.txt',
        '.env.example',
        '.gitignore',
        'production_setup.py',
        'models.py',
        'README.md',
        'LICENSE'
    ]
    
    print("📁 فحص الملفات المطلوبة:")
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    return len(missing_files) == 0

def check_env_example():
    """فحص ملف .env.example"""
    print("\n🔧 فحص متغيرات البيئة:")
    
    required_vars = [
        'SECRET_KEY',
        'DATABASE_URL',
        'MAIL_USERNAME',
        'MAIL_PASSWORD',
        'STRIPE_SECRET_KEY',
        'STRIPE_PUBLISHABLE_KEY'
    ]
    
    try:
        with open('.env.example', 'r') as f:
            content = f.read()
            
        missing_vars = []
        for var in required_vars:
            if var in content:
                print(f"✅ {var}")
            else:
                print(f"❌ {var} - مفقود")
                missing_vars.append(var)
        
        return len(missing_vars) == 0
        
    except FileNotFoundError:
        print("❌ ملف .env.example غير موجود")
        return False

def check_imports():
    """فحص الاستيرادات الأساسية"""
    print("\n📦 فحص الاستيرادات:")
    
    try:
        from app import app
        print("✅ app")
    except Exception as e:
        print(f"❌ app - {e}")
        return False
    
    try:
        from models import db, User, Package
        print("✅ models")
    except Exception as e:
        print(f"❌ models - {e}")
        return False
    
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        print("✅ Flask dependencies")
    except Exception as e:
        print(f"❌ Flask dependencies - {e}")
        return False
    
    return True

def check_database_config():
    """فحص إعدادات قاعدة البيانات"""
    print("\n🗄️ فحص إعدادات قاعدة البيانات:")
    
    try:
        from app import app
        with app.app_context():
            db_uri = app.config.get('SQLALCHEMY_DATABASE_URI', '')
            
            if 'postgresql://' in db_uri or 'sqlite:///' in db_uri:
                print("✅ إعدادات قاعدة البيانات صحيحة")
                return True
            else:
                print("❌ إعدادات قاعدة البيانات غير صحيحة")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def main():
    """الفحص الرئيسي"""
    print("🚀 فحص جاهزية Quran LMS للإنتاج")
    print("=" * 50)
    
    checks = [
        ("الملفات المطلوبة", check_files),
        ("متغيرات البيئة", check_env_example),
        ("الاستيرادات", check_imports),
        ("قاعدة البيانات", check_database_config)
    ]
    
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        try:
            if check_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في فحص {name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتيجة: {passed}/{total} فحوصات نجحت")
    
    if passed == total:
        print("🎉 التطبيق جاهز للإنتاج!")
        print("\n📋 الخطوات التالية:")
        print("1. ارفع الكود إلى GitHub")
        print("2. أنشئ PostgreSQL database على Render")
        print("3. أنشئ Web Service على Render")
        print("4. أضف متغيرات البيئة")
        print("5. انشر التطبيق!")
        return True
    else:
        print("⚠️ يرجى إصلاح المشاكل قبل النشر")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
