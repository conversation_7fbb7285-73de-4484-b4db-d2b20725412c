"""
Script لإعادة إنشاء حساب الإدمن والبيانات الأساسية
"""

from app import app
from models import User, Package, AcademySettings, EmailSettings, SessionSettings, CalendarSettings, UserNotificationSettings, db
from werkzeug.security import generate_password_hash
from datetime import datetime

def create_admin_user():
    """إنشاء حساب الإدمن"""
    with app.app_context():
        try:
            # التأكد من إنشاء الجداول
            db.create_all()
            
            # فحص إذا كان الإدمن موجود
            admin = User.query.filter_by(email='<EMAIL>').first()
            if admin:
                print("حساب الإدمن موجود بالفعل")
                return admin
            
            # إنشاء حساب الإدمن
            admin = User(
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                full_name='مدير النظام',
                phone='0500000000',
                role='admin',
                status='approved',
                created_at=datetime.utcnow()
            )
            
            db.session.add(admin)
            db.session.commit()
            
            print("✅ تم إنشاء حساب الإدمن:")
            print(f"   البريد الإلكتروني: <EMAIL>")
            print(f"   كلمة المرور: admin123")
            
            return admin
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إنشاء حساب الإدمن: {e}")
            return None

def create_sample_users():
    """إنشاء مستخدمين تجريبيين"""
    with app.app_context():
        try:
            # إنشاء معلم تجريبي
            teacher = User.query.filter_by(email='<EMAIL>').first()
            if not teacher:
                teacher = User(
                    email='<EMAIL>',
                    password_hash=generate_password_hash('teacher123'),
                    full_name='أحمد محمد - معلم',
                    phone='0501111111',
                    role='teacher',
                    status='approved',
                    created_at=datetime.utcnow()
                )
                db.session.add(teacher)
                print("✅ تم إنشاء حساب المعلم التجريبي:")
                print(f"   البريد الإلكتروني: <EMAIL>")
                print(f"   كلمة المرور: teacher123")
            
            # إنشاء طالب تجريبي
            student = User.query.filter_by(email='<EMAIL>').first()
            if not student:
                student = User(
                    email='<EMAIL>',
                    password_hash=generate_password_hash('student123'),
                    full_name='فاطمة علي - طالبة',
                    phone='0502222222',
                    role='student',
                    status='approved',
                    created_at=datetime.utcnow()
                )
                db.session.add(student)
                print("✅ تم إنشاء حساب الطالب التجريبي:")
                print(f"   البريد الإلكتروني: <EMAIL>")
                print(f"   كلمة المرور: student123")
            
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إنشاء المستخدمين التجريبيين: {e}")

def create_sample_packages():
    """إنشاء باقات تجريبية"""
    with app.app_context():
        try:
            # فحص إذا كانت الباقات موجودة
            if Package.query.count() > 0:
                print("الباقات موجودة بالفعل")
                return
            
            packages = [
                {
                    'name': 'باقة المبتدئين',
                    'description': 'باقة مناسبة للمبتدئين في تعلم القرآن الكريم',
                    'price': 200.0,
                    'currency': 'SAR',
                    'duration_days': 30,
                    'sessions_count': 8,
                    'session_duration': 30
                },
                {
                    'name': 'باقة المتوسطين',
                    'description': 'باقة للطلاب المتوسطين في مستوى التحفيظ',
                    'price': 350.0,
                    'currency': 'SAR',
                    'duration_days': 30,
                    'sessions_count': 12,
                    'session_duration': 45
                },
                {
                    'name': 'باقة المتقدمين',
                    'description': 'باقة للطلاب المتقدمين والمراجعة',
                    'price': 500.0,
                    'currency': 'SAR',
                    'duration_days': 30,
                    'sessions_count': 16,
                    'session_duration': 60
                }
            ]
            
            for pkg_data in packages:
                package = Package(**pkg_data)
                db.session.add(package)
            
            db.session.commit()
            print("✅ تم إنشاء الباقات التجريبية")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إنشاء الباقات: {e}")

def create_default_settings():
    """إنشاء الإعدادات الافتراضية"""
    with app.app_context():
        try:
            # إعدادات الأكاديمية
            if not AcademySettings.query.first():
                academy_settings = AcademySettings(
                    academy_name='أكاديمية القرآن الكريم',
                    academy_email='<EMAIL>',
                    academy_phone='0500000000',
                    academy_address='الرياض، المملكة العربية السعودية',
                    primary_color='#007bff',
                    secondary_color='#6c757d'
                )
                db.session.add(academy_settings)
                print("✅ تم إنشاء إعدادات الأكاديمية")
            
            # إعدادات البريد الإلكتروني
            if not EmailSettings.query.first():
                email_settings = EmailSettings(
                    smtp_server='smtp.gmail.com',
                    smtp_port=587,
                    smtp_username='',
                    smtp_password='',
                    use_tls=True,
                    sender_name='أكاديمية القرآن الكريم',
                    sender_email='<EMAIL>'
                )
                db.session.add(email_settings)
                print("✅ تم إنشاء إعدادات البريد الإلكتروني")
            
            # إعدادات الحصص
            if not SessionSettings.query.first():
                session_settings = SessionSettings(
                    default_provider='google_meet',
                    auto_generate_links=True,
                    use_google_calendar=False,
                    jitsi_enabled=True,
                    jitsi_domain='meet.jit.si',
                    jitsi_prefix='academy'
                )
                db.session.add(session_settings)
                print("✅ تم إنشاء إعدادات الحصص")
            
            # إعدادات التقويم
            if not CalendarSettings.query.first():
                calendar_settings = CalendarSettings(
                    google_calendar_enabled=False,
                    auto_create_events=False,
                    default_reminder_minutes=15
                )
                db.session.add(calendar_settings)
                print("✅ تم إنشاء إعدادات التقويم")
            
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إنشاء الإعدادات: {e}")

def create_notification_settings():
    """إنشاء إعدادات الإشعارات للمستخدمين"""
    with app.app_context():
        try:
            users = User.query.all()
            for user in users:
                if not UserNotificationSettings.query.filter_by(user_id=user.id).first():
                    settings = UserNotificationSettings(user_id=user.id)
                    db.session.add(settings)
            
            db.session.commit()
            print(f"✅ تم إنشاء إعدادات الإشعارات لـ {len(users)} مستخدم")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إنشاء إعدادات الإشعارات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔄 بدء إعادة إنشاء البيانات الأساسية...")
    
    # إنشاء حساب الإدمن
    admin = create_admin_user()
    if not admin:
        print("❌ فشل في إنشاء حساب الإدمن")
        return
    
    # إنشاء المستخدمين التجريبيين
    create_sample_users()
    
    # إنشاء الباقات التجريبية
    create_sample_packages()
    
    # إنشاء الإعدادات الافتراضية
    create_default_settings()
    
    # إنشاء إعدادات الإشعارات
    create_notification_settings()
    
    print("\n🎉 تم إعادة إنشاء البيانات الأساسية بنجاح!")
    print("\n📋 معلومات تسجيل الدخول:")
    print("=" * 50)
    print("🔑 حساب الإدمن:")
    print("   البريد الإلكتروني: <EMAIL>")
    print("   كلمة المرور: admin123")
    print("\n👨‍🏫 حساب المعلم التجريبي:")
    print("   البريد الإلكتروني: <EMAIL>")
    print("   كلمة المرور: teacher123")
    print("\n🎓 حساب الطالب التجريبي:")
    print("   البريد الإلكتروني: <EMAIL>")
    print("   كلمة المرور: student123")
    print("=" * 50)

if __name__ == '__main__':
    main()
