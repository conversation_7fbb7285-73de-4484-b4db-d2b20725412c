{% extends "base.html" %}

{% block title %}
{% if template %}تعديل القالب: {{ template.display_name }}{% else %}قالب جديد{% endif %} - {{ academy_name }}
{% endblock %}

{% block page_title %}
{% if template %}تعديل القالب{% else %}قالب جديد{% endif %}
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
<style>
.variable-tag {
    cursor: pointer;
    margin: 2px;
}
.variable-tag:hover {
    background-color: #007bff !important;
}
.code-editor {
    font-family: 'Courier New', monospace;
    font-size: 14px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <form method="POST" id="templateForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
        
        <div class="row">
            <div class="col-12">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            {% if template %}تعديل القالب: {{ template.display_name }}{% else %}إنشاء قالب جديد{% endif %}
                        </h4>
                        <p class="text-muted mb-0">
                            {% if template %}{{ template.name }}{% else %}قم بإنشاء قالب بريد إلكتروني جديد{% endif %}
                        </p>
                    </div>
                    <div>
                        <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-right me-1"></i>إلغاء
                        </a>
                        {% if template %}
                        <a href="{{ url_for('admin.preview_email_template', template_id=template.id) }}" 
                           class="btn btn-outline-info me-2" target="_blank">
                            <i class="fas fa-eye me-1"></i>معاينة
                        </a>
                        {% endif %}
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>حفظ
                        </button>
                    </div>
                </div>

                <div class="row">
                    <!-- Form Fields -->
                    <div class="col-lg-8">
                        <!-- Basic Info -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">اسم القالب (بالإنجليزية) *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="{{ template.name if template else '' }}" 
                                               {% if template and template.is_system %}readonly{% endif %}
                                               placeholder="welcome_email" required>
                                        <div class="form-text">اسم فريد للقالب (حروف إنجليزية وأرقام و _ فقط)</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="display_name" class="form-label">الاسم المعروض *</label>
                                        <input type="text" class="form-control" id="display_name" name="display_name" 
                                               value="{{ template.display_name if template else '' }}" 
                                               placeholder="رسالة الترحيب" required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="template_type" class="form-label">نوع القالب *</label>
                                        <select class="form-select" id="template_type" name="template_type" required>
                                            <option value="">اختر النوع</option>
                                            <option value="welcome" {% if template and template.template_type == 'welcome' %}selected{% endif %}>ترحيب</option>
                                            <option value="reminder" {% if template and template.template_type == 'reminder' %}selected{% endif %}>تذكير</option>
                                            <option value="payment" {% if template and template.template_type == 'payment' %}selected{% endif %}>دفع</option>
                                            <option value="notification" {% if template and template.template_type == 'notification' %}selected{% endif %}>إشعار</option>
                                            <option value="report" {% if template and template.template_type == 'report' %}selected{% endif %}>تقرير</option>
                                            <option value="other" {% if template and template.template_type == 'other' %}selected{% endif %}>أخرى</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الحالة</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                   {% if not template or template.is_active %}checked{% endif %}>
                                            <label class="form-check-label" for="is_active">
                                                مفعل
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="variables" class="form-label">المتغيرات المتاحة</label>
                                    <input type="text" class="form-control" id="variables" name="variables" 
                                           value="{% if template %}{{ template.get_variables_list()|join(', ') }}{% endif %}"
                                           placeholder="user_name, user_email, academy_name">
                                    <div class="form-text">أدخل أسماء المتغيرات مفصولة بفواصل</div>
                                </div>
                            </div>
                        </div>

                        <!-- Subject -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-envelope me-2"></i>موضوع الرسالة
                                </h6>
                            </div>
                            <div class="card-body">
                                <textarea class="form-control" id="subject" name="subject" rows="2" 
                                          placeholder="مرحباً بك في {{ academy_name }}" required>{{ template.subject if template else '' }}</textarea>
                                <div class="form-text">يمكنك استخدام المتغيرات مثل {{ user_name }}</div>
                            </div>
                        </div>

                        <!-- HTML Body -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-code me-2"></i>محتوى HTML
                                </h6>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary active" onclick="toggleEditor('visual')">
                                        مرئي
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="toggleEditor('code')">
                                        كود
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="visual-editor">
                                    <textarea id="body_html" name="body_html" class="summernote">{{ template.body_html if template else '' }}</textarea>
                                </div>
                                <div id="code-editor" style="display: none;">
                                    <textarea class="form-control code-editor" id="body_html_code" rows="15">{{ template.body_html if template else '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Text Body -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-align-left me-2"></i>النص العادي (اختياري)
                                </h6>
                            </div>
                            <div class="card-body">
                                <textarea class="form-control" id="body_text" name="body_text" rows="8" 
                                          placeholder="نسخة نصية من الرسالة للعملاء الذين لا يدعمون HTML">{{ template.body_text if template else '' }}</textarea>
                                <div class="form-text">نسخة نصية من الرسالة (للعملاء الذين لا يدعمون HTML)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Variables Helper -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-tags me-2"></i>المتغيرات الشائعة
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="small text-muted mb-3">اضغط على المتغير لإضافته</p>
                                <div class="d-flex flex-wrap gap-1" id="common-variables">
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('user_name')">user_name</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('user_email')">user_email</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('academy_name')">academy_name</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('login_url')">login_url</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('current_date')">current_date</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('session_date')">session_date</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('session_time')">session_time</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('teacher_name')">teacher_name</span>
                                    <span class="badge bg-primary variable-tag" onclick="insertVariable('session_url')">session_url</span>
                                </div>
                            </div>
                        </div>

                        <!-- Template Examples -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>أمثلة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="accordion" id="examplesAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#example1">
                                                رسالة ترحيب
                                            </button>
                                        </h2>
                                        <div id="example1" class="accordion-collapse collapse" data-bs-parent="#examplesAccordion">
                                            <div class="accordion-body">
                                                <small class="text-muted">مثال على رسالة ترحيب:</small>
                                                <code class="d-block mt-2 small">
                                                    مرحباً {{ user_name }}،<br>
                                                    نرحب بك في {{ academy_name }}
                                                </code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if template %}
                        <!-- Template Info -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-info me-2"></i>معلومات القالب
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <small class="text-muted">تاريخ الإنشاء:</small><br>
                                    <small>{{ template.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">آخر تحديث:</small><br>
                                    <small>{{ template.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                                {% if template.is_system %}
                                <div class="alert alert-warning small mb-0">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    هذا قالب نظام ولا يمكن حذفه
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
<script>
let currentEditor = 'visual';

$(document).ready(function() {
    // Initialize Summernote
    $('.summernote').summernote({
        height: 400,
        direction: 'rtl',
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'underline', 'clear']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link', 'picture']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ]
    });
});

function toggleEditor(type) {
    // Update buttons
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    if (type === 'visual') {
        if (currentEditor === 'code') {
            // Copy from code editor to visual
            $('.summernote').summernote('code', document.getElementById('body_html_code').value);
        }
        document.getElementById('visual-editor').style.display = 'block';
        document.getElementById('code-editor').style.display = 'none';
        currentEditor = 'visual';
    } else {
        // Copy from visual to code editor
        document.getElementById('body_html_code').value = $('.summernote').summernote('code');
        document.getElementById('visual-editor').style.display = 'none';
        document.getElementById('code-editor').style.display = 'block';
        currentEditor = 'code';
    }
}

function insertVariable(varName) {
    const variable = '{{ ' + varName + ' }}';

    if (currentEditor === 'visual') {
        $('.summernote').summernote('insertText', variable);
    } else {
        const textarea = document.getElementById('body_html_code');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;
        textarea.value = text.substring(0, start) + variable + text.substring(end);
        textarea.selectionStart = textarea.selectionEnd = start + variable.length;
        textarea.focus();
    }
}

// Form submission handler
document.getElementById('templateForm').addEventListener('submit', function(e) {
    // Sync editors before submit
    if (currentEditor === 'code') {
        $('.summernote').summernote('code', document.getElementById('body_html_code').value);
    }
});
</script>
{% endblock %}
