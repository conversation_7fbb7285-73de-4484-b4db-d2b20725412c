from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import User, Session, Notification, Subscription, Package, Payment, db
from datetime import datetime

api_bp = Blueprint('api', __name__)

@api_bp.route('/notifications')
@login_required
def get_notifications():
    """Get notifications for current user"""
    notifications = Notification.query.filter_by(
        user_id=current_user.id
    ).order_by(Notification.created_at.desc()).limit(10).all()
    
    return jsonify([{
        'id': n.id,
        'title': n.title,
        'message': n.message,
        'type': n.notification_type,
        'is_read': n.is_read,
        'created_at': n.created_at.isoformat()
    } for n in notifications])

@api_bp.route('/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Mark a notification as read"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=current_user.id
    ).first_or_404()
    
    notification.is_read = True
    db.session.commit()
    
    return jsonify({'success': True})

@api_bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read"""
    Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).update({'is_read': True})
    
    db.session.commit()
    
    return jsonify({'success': True})

@api_bp.route('/notifications/unread-count')
@login_required
def get_unread_count():
    """Get count of unread notifications"""
    count = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).count()
    
    return jsonify({'count': count})

@api_bp.route('/sessions/upcoming')
@login_required
def get_upcoming_sessions():
    """Get upcoming sessions for current user"""
    if current_user.role == 'teacher':
        sessions = Session.query.filter(
            Session.teacher_id == current_user.id,
            Session.status == 'scheduled',
            Session.scheduled_datetime >= datetime.now()
        ).order_by(Session.scheduled_datetime).limit(5).all()
    elif current_user.role == 'student':
        sessions = Session.query.filter(
            Session.student_id == current_user.id,
            Session.status == 'scheduled',
            Session.scheduled_datetime >= datetime.now()
        ).order_by(Session.scheduled_datetime).limit(5).all()
    else:
        sessions = []
    
    return jsonify([{
        'id': s.id,
        'teacher_name': s.teacher.full_name,
        'student_name': s.student.full_name,
        'scheduled_datetime': s.scheduled_datetime.isoformat(),
        'duration_minutes': s.duration_minutes,
        'session_type': s.session_type
    } for s in sessions])

@api_bp.route('/dashboard/stats')
@login_required
def get_dashboard_stats():
    """Get dashboard statistics based on user role"""
    if current_user.role == 'admin':
        stats = {
            'total_users': User.query.count(),
            'pending_users': User.query.filter_by(status='pending').count(),
            'total_sessions': Session.query.count(),
            'today_sessions': Session.query.filter(
                db.func.date(Session.scheduled_datetime) == datetime.now().date()
            ).count()
        }
    elif current_user.role == 'teacher':
        stats = {
            'total_sessions': Session.query.filter_by(teacher_id=current_user.id).count(),
            'completed_sessions': Session.query.filter_by(
                teacher_id=current_user.id, 
                status='completed'
            ).count(),
            'today_sessions': Session.query.filter(
                Session.teacher_id == current_user.id,
                db.func.date(Session.scheduled_datetime) == datetime.now().date()
            ).count()
        }
    elif current_user.role == 'student':
        stats = {
            'total_sessions': Session.query.filter_by(student_id=current_user.id).count(),
            'completed_sessions': Session.query.filter_by(
                student_id=current_user.id, 
                status='completed'
            ).count(),
            'upcoming_sessions': Session.query.filter(
                Session.student_id == current_user.id,
                Session.status == 'scheduled',
                Session.scheduled_datetime >= datetime.now()
            ).count()
        }
    else:
        stats = {}
    
    return jsonify(stats)

@api_bp.route('/sessions/<int:session_id>/status', methods=['POST'])
@login_required
def update_session_status(session_id):
    """Update session status"""
    session = Session.query.get_or_404(session_id)
    
    # Check permissions
    if current_user.role not in ['admin', 'teacher'] and session.teacher_id != current_user.id:
        return jsonify({'error': 'غير مصرح لك بتحديث هذه الحصة'}), 403
    
    data = request.get_json()
    new_status = data.get('status')
    
    if new_status in ['scheduled', 'completed', 'cancelled', 'missed']:
        if new_status == 'completed':
            session.mark_completed()  # استخدام الدالة التي ترسل الإشعار
        else:
            session.status = new_status
            if new_status == 'cancelled':
                # Delete Google Calendar event when session is cancelled
                session.delete_calendar_event()

        db.session.commit()
        
        # Create notification for student
        if new_status == 'completed':
            notification = Notification(
                user_id=session.student_id,
                title='تم إكمال الحصة',
                message=f'تم إكمال حصتك مع {session.teacher.full_name}',
                notification_type='session'
            )
            db.session.add(notification)
            db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم تحديث حالة الحصة'})
    
    return jsonify({'error': 'حالة غير صحيحة'}), 400

@api_bp.route('/packages/<int:package_id>/subscribe', methods=['POST'])
@login_required
def subscribe_to_package(package_id):
    """Subscribe to a package"""
    if current_user.role != 'student':
        return jsonify({'error': 'فقط الطلاب يمكنهم الاشتراك في الباقات'}), 403
    
    package = Package.query.get_or_404(package_id)
    
    if not package.is_active:
        return jsonify({'error': 'هذه الباقة غير متاحة حالياً'}), 400
    
    # Check if user already has an active subscription
    existing_subscription = Subscription.query.filter_by(
        user_id=current_user.id,
        status='active'
    ).first()
    
    if existing_subscription:
        return jsonify({'error': 'لديك اشتراك نشط بالفعل'}), 400
    
    # Create new subscription
    subscription = Subscription(
        user_id=current_user.id,
        package_id=package_id,
        status='pending',
        purchase_date=datetime.now()
    )
    
    db.session.add(subscription)
    db.session.commit()
    
    # Create notification for admin
    admin_notification = Notification(
        user_id=User.query.filter_by(role='admin').first().id,
        title='طلب اشتراك جديد',
        message=f'طلب اشتراك جديد من {current_user.full_name} في باقة {package.name}',
        notification_type='subscription'
    )
    db.session.add(admin_notification)
    db.session.commit()
    
    return jsonify({
        'success': True, 
        'message': 'تم إرسال طلب الاشتراك بنجاح. سيتم مراجعته قريباً.',
        'subscription_id': subscription.id
    })

@api_bp.route('/user/profile', methods=['GET', 'PUT'])
@login_required
def user_profile():
    """Get or update user profile"""
    if request.method == 'GET':
        return jsonify({
            'id': current_user.id,
            'first_name': current_user.first_name,
            'last_name': current_user.last_name,
            'email': current_user.email,
            'phone': current_user.phone,
            'role': current_user.role,
            'status': current_user.status,
            'created_at': current_user.created_at.isoformat()
        })
    
    elif request.method == 'PUT':
        data = request.get_json()
        
        # Update allowed fields
        if 'first_name' in data:
            current_user.first_name = data['first_name']
        if 'last_name' in data:
            current_user.last_name = data['last_name']
        if 'phone' in data:
            current_user.phone = data['phone']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث الملف الشخصي بنجاح'
        })

@api_bp.route('/search/users')
@login_required
def search_users():
    """Search users (admin only)"""
    if current_user.role != 'admin':
        return jsonify({'error': 'غير مصرح لك بالبحث عن المستخدمين'}), 403
    
    query = request.args.get('q', '')
    role = request.args.get('role', '')
    
    users_query = User.query
    
    if query:
        users_query = users_query.filter(
            (User.first_name.contains(query)) |
            (User.last_name.contains(query)) |
            (User.email.contains(query))
        )
    
    if role:
        users_query = users_query.filter_by(role=role)
    
    users = users_query.limit(10).all()
    
    return jsonify([{
        'id': u.id,
        'name': u.full_name,
        'email': u.email,
        'role': u.role,
        'status': u.status
    } for u in users])

@api_bp.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })
