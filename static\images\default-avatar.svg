<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="50" cy="50" r="50" fill="url(#bgGradient)"/>
  
  <!-- User Icon -->
  <g fill="white" opacity="0.9">
    <!-- Head -->
    <circle cx="50" cy="35" r="12"/>
    <!-- Body -->
    <path d="M50 50 C35 50, 25 60, 25 75 L75 75 C75 60, 65 50, 50 50 Z"/>
  </g>
</svg>
