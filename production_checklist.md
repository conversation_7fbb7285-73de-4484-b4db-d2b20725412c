# قائمة فحص الإنتاج - نظام أكاديمية القرآن الكريم

## ✅ **1. إعدادات البريد الإلكتروني**

### **قبل الرفع:**
- [ ] تحديث إعدادات SMTP في `/admin/email-settings`
- [ ] إدخال بيانات SMTP الصحيحة للإنتاج
- [ ] اختبار إرسال بريد تجريبي
- [ ] التأكد من تفعيل جميع أنواع الإشعارات

### **بيانات SMTP المطلوبة:**
```
SMTP Server: smtp.gmail.com (أو خادم البريد الخاص بك)
SMTP Port: 587
SMTP Username: <EMAIL>
SMTP Password: your-app-password
Use TLS: True
Default Sender: <EMAIL>
```

## ✅ **2. قاعدة البيانات**

### **قبل الرفع:**
- [ ] تشغيل `python init_db.py` لإنشاء الجداول
- [ ] تشغيل `python recreate_admin.py` لإنشاء حساب الإدمن
- [ ] إضافة العمود الجديد للحصص:
```sql
ALTER TABLE session ADD COLUMN completion_notified BOOLEAN DEFAULT FALSE;
```

### **في الإنتاج:**
- [ ] استخدام قاعدة بيانات PostgreSQL أو MySQL
- [ ] إعداد نسخ احتياطية تلقائية
- [ ] تحسين فهارس قاعدة البيانات

## ✅ **3. نظام التذكيرات التلقائي**

### **إعداد Cron Jobs:**
```bash
# تذكيرات فورية (كل دقيقة)
* * * * * cd /path/to/your/app && python scheduler.py --immediate

# مهام يومية (كل يوم في 9:00 صباحاً)
0 9 * * * cd /path/to/your/app && python scheduler.py --daily

# تنظيف أسبوعي (كل أحد في 2:00 صباحاً)
0 2 * * 0 cd /path/to/your/app && python -c "from utils.session_reminder_scheduler import run_cleanup_job; run_cleanup_job()"
```

### **أو استخدام المجدول المدمج:**
```bash
# تشغيل المجدول في الخلفية
nohup python scheduler.py > scheduler.log 2>&1 &
```

## ✅ **4. متغيرات البيئة**

### **ملف `.env` للإنتاج:**
```env
FLASK_ENV=production
SECRET_KEY=your-super-secret-production-key
DATABASE_URL=postgresql://user:password@localhost/quranlms_production
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

## ✅ **5. إعدادات الأمان**

### **قبل الرفع:**
- [ ] تغيير `SECRET_KEY` إلى قيمة قوية وفريدة
- [ ] تعطيل وضع التطوير (`DEBUG = False`)
- [ ] إعداد HTTPS
- [ ] إعداد جدار حماية
- [ ] تحديث كلمات مرور قواعد البيانات

## ✅ **6. اختبار الإشعارات**

### **اختبارات مطلوبة:**
- [ ] تسجيل حساب جديد → إشعار ترحيب + إشعار إدمن
- [ ] قبول حساب → إشعار تفعيل
- [ ] شراء باقة → إشعار تأكيد دفع
- [ ] إنشاء حصة → إشعار إنشاء حصة
- [ ] إنهاء حصة → إشعار انتهاء حصة
- [ ] تذكيرات الحصص (يومي + فوري)

## ✅ **7. مراقبة النظام**

### **ملفات السجلات:**
```bash
# سجل التطبيق
tail -f app.log

# سجل المجدول
tail -f scheduler.log

# سجل البريد الإلكتروني
tail -f email.log
```

### **مراقبة الأداء:**
- [ ] مراقبة استخدام الذاكرة
- [ ] مراقبة استخدام المعالج
- [ ] مراقبة مساحة القرص
- [ ] مراقبة اتصالات قاعدة البيانات

## ✅ **8. النسخ الاحتياطية**

### **نسخ احتياطية يومية:**
```bash
# نسخة احتياطية لقاعدة البيانات
pg_dump quranlms_production > backup_$(date +%Y%m%d).sql

# نسخة احتياطية للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz uploads/ static/
```

## ✅ **9. إعدادات الخادم**

### **متطلبات الخادم:**
- Python 3.8+
- PostgreSQL 12+ أو MySQL 8+
- Redis (للجلسات والتخزين المؤقت)
- Nginx (كخادم ويب عكسي)
- SSL Certificate

### **إعداد Nginx:**
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## ✅ **10. اختبار نهائي**

### **قبل الإطلاق:**
- [ ] اختبار جميع الصفحات
- [ ] اختبار جميع الإشعارات
- [ ] اختبار عمليات الدفع
- [ ] اختبار إنشاء وإدارة الحصص
- [ ] اختبار لوحات التحكم (إدمن، معلم، طالب)
- [ ] اختبار الأمان والصلاحيات

## 🚨 **مشاكل محتملة وحلولها**

### **مشكلة: الإشعارات لا تصل**
**الحل:**
1. فحص إعدادات SMTP
2. فحص سجلات البريد الإلكتروني
3. التأكد من تفعيل الإشعارات في الإعدادات

### **مشكلة: التذكيرات لا تعمل**
**الحل:**
1. التأكد من تشغيل cron jobs
2. فحص سجل المجدول
3. اختبار المجدول يدوياً

### **مشكلة: بطء في الأداء**
**الحل:**
1. تحسين استعلامات قاعدة البيانات
2. إضافة فهارس
3. استخدام التخزين المؤقت

## 📞 **الدعم الفني**

في حالة مواجهة مشاكل:
1. فحص ملفات السجلات
2. التأكد من إعدادات البيئة
3. اختبار الاتصالات (قاعدة البيانات، SMTP)
4. مراجعة هذه القائمة

---

**✅ النظام جاهز للإنتاج عند اكتمال جميع النقاط أعلاه**
