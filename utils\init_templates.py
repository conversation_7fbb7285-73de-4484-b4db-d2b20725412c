"""
Initialize default email templates for the system
This script creates the basic email templates needed for the application
"""

from models import EmailTemplate, db
from utils.email_service import get_default_templates, get_user_management_templates
import json


def init_email_templates():
    """Initialize default email templates if they don't exist"""
    try:
        print("🔄 تهيئة قوالب البريد الإلكتروني...")
        
        default_templates = get_default_templates()
        created_count = 0
        updated_count = 0
        
        for template_data in default_templates:
            # Check if template already exists
            existing_template = EmailTemplate.query.filter_by(name=template_data['name']).first()
            
            if existing_template:
                # Update existing system template if needed
                if existing_template.is_system:
                    existing_template.display_name = template_data['display_name']
                    existing_template.subject = template_data['subject']
                    existing_template.body_html = template_data['body_html']
                    existing_template.body_text = template_data['body_text']
                    existing_template.template_type = template_data['template_type']
                    existing_template.set_variables_list(template_data['variables'])
                    updated_count += 1
                    print(f"✅ تم تحديث القالب: {template_data['display_name']}")
            else:
                # Create new template
                new_template = EmailTemplate(
                    name=template_data['name'],
                    display_name=template_data['display_name'],
                    subject=template_data['subject'],
                    body_html=template_data['body_html'],
                    body_text=template_data['body_text'],
                    template_type=template_data['template_type'],
                    is_active=True,
                    is_system=template_data['is_system']
                )
                new_template.set_variables_list(template_data['variables'])
                
                db.session.add(new_template)
                created_count += 1
                print(f"✅ تم إنشاء القالب: {template_data['display_name']}")
        
        db.session.commit()
        
        print(f"🎉 تم الانتهاء من تهيئة القوالب:")
        print(f"   📝 تم إنشاء: {created_count} قالب")
        print(f"   🔄 تم تحديث: {updated_count} قالب")
        
        return True, f"تم إنشاء {created_count} قالب وتحديث {updated_count} قالب"
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في تهيئة القوالب: {str(e)}")
        return False, f"خطأ في تهيئة القوالب: {str(e)}"


def create_additional_templates():
    """Create additional useful templates"""
    additional_templates = [
        {
            'name': 'payment_confirmation',
            'display_name': 'تأكيد الدفع',
            'subject': 'تأكيد استلام الدفعة - {{ academy_name }}',
            'template_type': 'payment',
            'is_system': True,
            'variables': ['user_name', 'amount', 'payment_date', 'package_name', 'academy_name'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px;">
                        <h2 style="color: #155724;">تأكيد استلام الدفعة</h2>
                        <p>عزيزي/عزيزتي {{ user_name }}،</p>
                        <p>نؤكد استلام دفعتك بنجاح:</p>
                        <ul>
                            <li><strong>المبلغ:</strong> {{ amount }} ريال</li>
                            <li><strong>التاريخ:</strong> {{ payment_date }}</li>
                            <li><strong>الباقة:</strong> {{ package_name }}</li>
                        </ul>
                        <p>شكراً لك على ثقتك في {{ academy_name }}</p>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،
            
            نؤكد استلام دفعتك بنجاح:
            
            المبلغ: {{ amount }} ريال
            التاريخ: {{ payment_date }}
            الباقة: {{ package_name }}
            
            شكراً لك على ثقتك في {{ academy_name }}
            '''
        },
        {
            'name': 'session_completed',
            'display_name': 'إشعار انتهاء الحصة',
            'subject': 'تم إنهاء حصتك مع {{ teacher_name }}',
            'template_type': 'notification',
            'is_system': True,
            'variables': ['user_name', 'teacher_name', 'session_date', 'session_duration', 'academy_name'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #cce5ff; border: 1px solid #99ccff; padding: 20px; border-radius: 5px;">
                        <h2 style="color: #0066cc;">تم إنهاء الحصة</h2>
                        <p>عزيزي/عزيزتي {{ user_name }}،</p>
                        <p>تم إنهاء حصتك بنجاح:</p>
                        <ul>
                            <li><strong>المعلم:</strong> {{ teacher_name }}</li>
                            <li><strong>التاريخ:</strong> {{ session_date }}</li>
                            <li><strong>المدة:</strong> {{ session_duration }} دقيقة</li>
                        </ul>
                        <p>نتمنى أن تكون الحصة مفيدة ونتطلع للقائك في الحصة القادمة.</p>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،
            
            تم إنهاء حصتك بنجاح:
            
            المعلم: {{ teacher_name }}
            التاريخ: {{ session_date }}
            المدة: {{ session_duration }} دقيقة
            
            نتمنى أن تكون الحصة مفيدة ونتطلع للقائك في الحصة القادمة.
            '''
        },
        {
            'name': 'session_reminder_1_day',
            'display_name': 'تذكير بالحصة قبل يوم',
            'subject': '📅 تذكير: حصتك غداً في {{ session_time }} - {{ academy_name }}',
            'template_type': 'reminder',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #ffc107; margin-bottom: 10px;">⏰ تذكير بحصتك غداً</h1>
                            <p style="color: #6c757d; margin: 0;">{{ academy_slogan }}</p>
                        </div>

                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نذكرك بأن لديك حصة مجدولة غداً. نرجو منك الاستعداد والحضور في الموعد المحدد.
                        </p>

                        <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #ffc107;">
                            <h3 style="color: #856404; margin-top: 0; margin-bottom: 15px;">📅 تفاصيل الحصة:</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">التاريخ:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_date }} (غداً)</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">الوقت:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_time }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">المدة:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_duration }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">المعلم:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ teacher_name }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">نوع الحصة:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_type }}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{ session_url }}" style="background-color: #ffc107; color: #212529; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                                📚 الاستعداد للحصة
                            </a>
                        </div>

                        <div style="background-color: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0; border-right: 4px solid #17a2b8;">
                            <p style="margin: 0; color: #0c5460;">
                                <strong>💡 نصائح للاستعداد:</strong><br>
                                • تأكد من اتصال الإنترنت<br>
                                • جهز المصحف أو التطبيق<br>
                                • اختر مكاناً هادئاً للحصة<br>
                                • ستصلك تذكير آخر قبل 5 دقائق من بداية الحصة
                            </p>
                        </div>

                        <hr style="border: 1px solid #dee2e6; margin: 30px 0;">

                        <div style="text-align: center; color: #6c757d; font-size: 14px;">
                            <p style="margin: 5px 0;"><strong>{{ academy_name }}</strong></p>
                            <p style="margin: 5px 0;">📧 {{ academy_email }} | 📞 {{ academy_phone }}</p>
                            <p style="margin: 5px 0; font-style: italic;">{{ academy_slogan }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            نذكرك بأن لديك حصة مجدولة غداً. نرجو منك الاستعداد والحضور في الموعد المحدد.

            تفاصيل الحصة:
            التاريخ: {{ session_date }} (غداً)
            الوقت: {{ session_time }}
            المدة: {{ session_duration }}
            المعلم: {{ teacher_name }}
            نوع الحصة: {{ session_type }}

            نصائح للاستعداد:
            • تأكد من اتصال الإنترنت
            • جهز المصحف أو التطبيق
            • اختر مكاناً هادئاً للحصة
            • ستصلك تذكير آخر قبل 5 دقائق من بداية الحصة

            يمكنك الاستعداد للحصة من خلال: {{ session_url }}

            مع أطيب التحيات،
            {{ academy_name }}
            {{ academy_email }} | {{ academy_phone }}
            '''
        },
        {
            'name': 'session_reminder_5_minutes',
            'display_name': 'تذكير بالحصة قبل 5 دقائق',
            'subject': '🔔 حصتك ستبدأ خلال 5 دقائق - {{ academy_name }}',
            'template_type': 'reminder',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #dc3545; margin-bottom: 10px;">🚨 حصتك ستبدأ خلال 5 دقائق!</h1>
                            <p style="color: #6c757d; margin: 0;">{{ academy_slogan }}</p>
                        </div>

                        <p style="font-size: 18px; line-height: 1.6; text-align: center; color: #dc3545; font-weight: bold;">
                            عزيزي/عزيزتي {{ user_name }}،<br>
                            حصتك ستبدأ خلال 5 دقائق فقط!
                        </p>

                        <div style="background-color: #f8d7da; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #dc3545;">
                            <h3 style="color: #721c24; margin-top: 0; margin-bottom: 15px;">⏰ تفاصيل الحصة الآن:</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">الوقت:</td>
                                    <td style="padding: 8px 0; color: #212529; font-weight: bold;">{{ session_time }} (خلال 5 دقائق)</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">المدة:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_duration }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">المعلم:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ teacher_name }}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{ session_url }}" style="background-color: #dc3545; color: white; padding: 20px 40px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 18px; animation: pulse 2s infinite;">
                                🚀 ادخل للحصة الآن
                            </a>
                        </div>

                        <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; border-right: 4px solid #28a745;">
                            <p style="margin: 0; color: #155724; text-align: center; font-weight: bold;">
                                ✅ تأكد من أن كل شيء جاهز:<br>
                                الإنترنت • المصحف • المكان الهادئ
                            </p>
                        </div>

                        <hr style="border: 1px solid #dee2e6; margin: 30px 0;">

                        <div style="text-align: center; color: #6c757d; font-size: 14px;">
                            <p style="margin: 5px 0;"><strong>{{ academy_name }}</strong></p>
                            <p style="margin: 5px 0;">📧 {{ academy_email }} | 📞 {{ academy_phone }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            🚨 حصتك ستبدأ خلال 5 دقائق فقط!

            تفاصيل الحصة:
            الوقت: {{ session_time }} (خلال 5 دقائق)
            المدة: {{ session_duration }}
            المعلم: {{ teacher_name }}

            ادخل للحصة الآن من خلال: {{ session_url }}

            تأكد من أن كل شيء جاهز:
            ✅ الإنترنت
            ✅ المصحف
            ✅ المكان الهادئ

            {{ academy_name }}
            {{ academy_email }} | {{ academy_phone }}
            '''
        }
    ]
    
    try:
        created_count = 0
        for template_data in additional_templates:
            existing_template = EmailTemplate.query.filter_by(name=template_data['name']).first()
            if not existing_template:
                new_template = EmailTemplate(
                    name=template_data['name'],
                    display_name=template_data['display_name'],
                    subject=template_data['subject'],
                    body_html=template_data['body_html'],
                    body_text=template_data['body_text'],
                    template_type=template_data['template_type'],
                    is_active=True,
                    is_system=template_data['is_system']
                )
                new_template.set_variables_list(template_data['variables'])
                
                db.session.add(new_template)
                created_count += 1
                print(f"✅ تم إنشاء القالب الإضافي: {template_data['display_name']}")
        
        db.session.commit()
        return True, f"تم إنشاء {created_count} قالب إضافي"
        
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في إنشاء القوالب الإضافية: {str(e)}"


def init_user_management_templates():
    """Initialize user management email templates"""
    try:
        print("🔄 تهيئة قوالب إدارة المستخدمين...")

        user_management_templates = get_user_management_templates()
        created_count = 0
        updated_count = 0

        for template_data in user_management_templates:
            # Check if template already exists
            existing_template = EmailTemplate.query.filter_by(name=template_data['name']).first()

            if existing_template:
                # Update existing system template if needed
                if existing_template.is_system:
                    existing_template.display_name = template_data['display_name']
                    existing_template.subject = template_data['subject']
                    existing_template.body_html = template_data['body_html']
                    existing_template.body_text = template_data['body_text']
                    existing_template.template_type = template_data['template_type']
                    existing_template.set_variables_list(template_data['variables'])
                    updated_count += 1
                    print(f"✅ تم تحديث قالب إدارة المستخدمين: {template_data['display_name']}")
            else:
                # Create new template
                new_template = EmailTemplate(
                    name=template_data['name'],
                    display_name=template_data['display_name'],
                    subject=template_data['subject'],
                    body_html=template_data['body_html'],
                    body_text=template_data['body_text'],
                    template_type=template_data['template_type'],
                    is_active=True,
                    is_system=template_data['is_system']
                )
                new_template.set_variables_list(template_data['variables'])

                db.session.add(new_template)
                created_count += 1
                print(f"✅ تم إنشاء قالب إدارة المستخدمين: {template_data['display_name']}")

        db.session.commit()

        if created_count > 0 or updated_count > 0:
            print(f"✅ تم إنشاء {created_count} قالب جديد وتحديث {updated_count} قالب موجود لإدارة المستخدمين")
        else:
            print("ℹ️ جميع قوالب إدارة المستخدمين موجودة ومحدثة")

        return True, f"تم إنشاء {created_count} قالب جديد وتحديث {updated_count} قالب"

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في تهيئة قوالب إدارة المستخدمين: {str(e)}")
        return False, f"خطأ في تهيئة قوالب إدارة المستخدمين: {str(e)}"


def init_all_templates():
    """Initialize all email templates (default, additional, and user management)"""
    print("🚀 بدء تهيئة جميع قوالب البريد الإلكتروني...")
    print("=" * 60)

    total_created = 0
    total_updated = 0

    # 1. Initialize default templates
    print("\n1️⃣ تهيئة القوالب الأساسية...")
    success1, message1 = init_email_templates()

    # 2. Create additional templates
    print("\n2️⃣ إنشاء القوالب الإضافية...")
    success2, message2 = create_additional_templates()

    # 3. Initialize user management templates
    print("\n3️⃣ تهيئة قوالب إدارة المستخدمين...")
    success3, message3 = init_user_management_templates()

    # Summary
    print("\n" + "=" * 60)
    print("📊 ملخص التهيئة:")
    print(f"   ✅ القوالب الأساسية: {message1}")
    print(f"   ✅ القوالب الإضافية: {message2}")
    print(f"   ✅ قوالب إدارة المستخدمين: {message3}")

    # Show all templates
    print("\n📋 جميع القوالب المتاحة:")
    try:
        all_templates = EmailTemplate.query.order_by(EmailTemplate.template_type, EmailTemplate.name).all()

        current_type = None
        for template in all_templates:
            if template.template_type != current_type:
                current_type = template.template_type
                print(f"\n📁 {current_type.upper()}:")

            status = "🟢 مفعل" if template.is_active else "🔴 معطل"
            system = "🔒 نظام" if template.is_system else "👤 مخصص"
            print(f"   - {template.name}: {template.display_name} {status} {system}")

        print(f"\n📊 المجموع الكلي: {len(all_templates)} قالب")

    except Exception as e:
        print(f"❌ خطأ في عرض القوالب: {str(e)}")

    overall_success = success1 and success2 and success3

    if overall_success:
        print("\n🎉 تم إنشاء وتهيئة جميع القوالب بنجاح!")
        print("✅ النظام جاهز للاستخدام في الإنتاج")
    else:
        print("\n⚠️ تمت التهيئة مع بعض المشاكل")
        if not success1:
            print(f"❌ مشكلة في القوالب الأساسية: {message1}")
        if not success2:
            print(f"❌ مشكلة في القوالب الإضافية: {message2}")
        if not success3:
            print(f"❌ مشكلة في قوالب إدارة المستخدمين: {message3}")

    return overall_success


if __name__ == "__main__":
    # This can be run as a standalone script
    from app import app

    with app.app_context():
        init_all_templates()
