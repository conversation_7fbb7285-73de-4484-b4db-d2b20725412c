{% extends "base.html" %}

{% block title %}لوحة تحكم الطالب - {{ academy_name }}{% endblock %}
{% block page_title %}لوحة تحكم الطالب{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4 class="card-title mb-1">مرحباً، {{ current_user.full_name }}</h4>
                        <p class="card-text mb-0">إليك ملخص نشاطك التعليمي اليوم</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-graduate fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Status -->
{% if active_subscription %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>اشتراكك النشط
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success">{{ active_subscription.package.name }}</div>
                            <div class="small text-muted">الباقة الحالية</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-primary">{{ active_subscription.sessions_remaining }}</div>
                            <div class="small text-muted">حصة متبقية</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info">{{ active_subscription.sessions_used }}</div>
                            <div class="small text-muted">حصة مكتملة</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning">{{ active_subscription.end_date.strftime('%Y-%m-%d') if active_subscription.end_date else 'غير محدد' }}</div>
                            <div class="small text-muted">تاريخ الانتهاء</div>
                        </div>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                {% set progress = (active_subscription.sessions_used / active_subscription.package.sessions_count * 100) if active_subscription.package.sessions_count > 0 else 0 %}
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">تقدم الاشتراك</span>
                        <span class="small">{{ "%.1f"|format(progress) }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ progress }}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>لا يوجد اشتراك نشط
                </h5>
            </div>
            <div class="card-body text-center">
                <p class="card-text">لا يوجد لديك اشتراك نشط حالياً. اشترك في إحدى الباقات للبدء في التعلم.</p>
                <a href="{{ url_for('student.packages') }}" class="btn btn-primary">
                    <i class="fas fa-shopping-cart me-2"></i>تصفح الباقات
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-primary text-uppercase mb-1">إجمالي الحصص</div>
                        <div class="h5 mb-0">{{ stats.total_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">الحصص المكتملة</div>
                        <div class="h5 mb-0">{{ stats.completed_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">الحصص القادمة</div>
                        <div class="h5 mb-0">{{ stats.upcoming_sessions_count }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">معدل الحضور</div>
                        <div class="h5 mb-0">{{ stats.attendance_rate }}%</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Session Statistics -->
<div class="row mb-4">
    {% if active_subscription %}
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">حصص الاشتراك</div>
                        <div class="h5 mb-0">{{ stats.subscription_sessions_count }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-check fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="col-xl-{% if active_subscription %}3{% else %}4{% endif %} col-md-6 mb-4">
        <div class="card border-start border-secondary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-secondary text-uppercase mb-1">الحصص الملغية</div>
                        <div class="h5 mb-0">{{ stats.cancelled_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle fa-2x text-secondary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-{% if active_subscription %}3{% else %}4{% endif %} col-md-6 mb-4">
        <div class="card border-start border-purple border-4" style="border-color: #6f42c1 !important;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-uppercase mb-1" style="color: #6f42c1;">الحصص التجريبية</div>
                        <div class="h5 mb-0">{{ stats.trial_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-flask fa-2x" style="color: #6f42c1;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-{% if active_subscription %}3{% else %}4{% endif %} col-md-6 mb-4">
        <div class="card border-start border-dark border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-dark text-uppercase mb-1">الحصص التعويضية</div>
                        <div class="h5 mb-0">{{ stats.makeup_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-redo fa-2x text-dark"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Sessions Card -->
{% if active_subscription and stats.subscription_sessions_count > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>حصص الاشتراك المجدولة
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="text-center">
                            <div class="h3 text-warning mb-0">{{ stats.subscription_sessions_count }}</div>
                            <div class="small text-muted">حصة مجدولة</div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle text-warning me-2"></i>
                            <div>
                                <div class="fw-bold">حصص من اشتراكك الحالي</div>
                                <div class="small text-muted">
                                    هذه الحصص تم جدولتها من قبل الإدارة من باقة "{{ active_subscription.package.name }}" الخاصة بك
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('student.sessions') }}?type=subscription" class="btn btn-warning">
                            <i class="fas fa-eye me-1"></i>عرض الحصص
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Upcoming Sessions and Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-day me-2"></i>الحصص القادمة
                </h5>
                <a href="{{ url_for('student.sessions') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if upcoming_sessions %}
                    {% for session in upcoming_sessions %}
                    <div class="d-flex align-items-center mb-3 p-3 border rounded">
                        <div class="flex-shrink-0">
                            <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-chalkboard-teacher"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">{{ session.teacher.full_name }}</div>
                            <div class="small text-muted">
                                <i class="fas fa-calendar me-1"></i>{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}
                            </div>
                            <div class="small text-muted">
                                <i class="fas fa-clock me-1"></i>{{ session.scheduled_datetime.strftime('%H:%M') }}
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-info mb-2">{{ session.session_type }}</span>
                            <br>
                            <a href="{{ url_for('student.session_details', session_id=session.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد حصص قادمة</p>
                        {% if active_subscription %}
                        <p class="small text-muted">سيتم جدولة حصصك قريباً</p>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>الحصص المكتملة مؤخراً
                </h5>
                <a href="{{ url_for('student.sessions') }}?status=completed" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_completed %}
                    {% for session in recent_completed %}
                    <div class="d-flex align-items-center mb-3 p-3 border rounded">
                        <div class="flex-shrink-0">
                            <div class="avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">{{ session.teacher.full_name }}</div>
                            <div class="small text-muted">
                                <i class="fas fa-calendar me-1"></i>{{ session.completed_at.strftime('%Y-%m-%d') }}
                            </div>
                            <div class="small text-muted">
                                <i class="fas fa-clock me-1"></i>{{ session.completed_at.strftime('%H:%M') }}
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-success mb-2">مكتملة</span>
                            <br>
                            <a href="{{ url_for('student.session_details', session_id=session.id) }}" 
                               class="btn btn-sm btn-outline-success">
                                <i class="fas fa-star"></i> قيّم
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد حصص مكتملة مؤخراً</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('student.sessions') }}" class="btn btn-primary w-100">
                            <i class="fas fa-calendar-alt me-2"></i>عرض جميع الحصص
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('student.packages') }}" class="btn btn-success w-100">
                            <i class="fas fa-shopping-cart me-2"></i>تصفح الباقات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('student.subscriptions') }}" class="btn btn-info w-100">
                            <i class="fas fa-credit-card me-2"></i>اشتراكاتي
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('student.teachers') }}" class="btn btn-warning w-100">
                            <i class="fas fa-chalkboard-teacher me-2"></i>معلميّ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add hover effects to cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
{% endblock %}
