#!/usr/bin/env python3
"""
ملف تشغيل النظام في الإنتاج
يتم استدعاؤه تلقائياً من Render أو أي منصة استضافة أخرى
"""

import os
import sys
from datetime import datetime

def main():
    """الدالة الرئيسية لتشغيل النظام في الإنتاج"""
    
    print("🚀 بدء تشغيل نظام إدارة تعلم القرآن الكريم")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌍 البيئة: Production")
    print(f"🐍 Python: {sys.version}")
    
    try:
        # استيراد التطبيق
        from app import app, db
        
        # تشغيل الإعداد التلقائي
        print("\n🔧 تشغيل الإعداد التلقائي...")
        try:
            from utils.auto_deploy_setup import auto_setup_on_deploy, check_production_readiness
            
            with app.app_context():
                # تشغيل الإعداد التلقائي
                setup_success = auto_setup_on_deploy()
                
                if setup_success:
                    print("✅ تم الإعداد التلقائي بنجاح")
                    
                    # فحص جاهزية النظام
                    readiness_ok = check_production_readiness()
                    
                    if readiness_ok:
                        print("🎉 النظام جاهز للعمل في الإنتاج")
                    else:
                        print("⚠️ النظام يعمل ولكن هناك بعض التحذيرات")
                else:
                    print("⚠️ تم الإعداد مع بعض التحذيرات")
                    
        except Exception as e:
            print(f"⚠️ تحذير في الإعداد التلقائي: {str(e)}")
            print("🔄 المتابعة مع الإعدادات الحالية...")
        
        # تشغيل التطبيق
        print("\n🌐 بدء تشغيل الخادم...")
        print("=" * 60)
        
        # الحصول على المنفذ من متغيرات البيئة (Render يستخدم PORT)
        port = int(os.environ.get('PORT', 5000))
        host = os.environ.get('HOST', '0.0.0.0')
        
        print(f"🔗 الخادم يعمل على: {host}:{port}")
        print("✅ النظام جاهز لاستقبال الطلبات")
        
        # تشغيل التطبيق في وضع الإنتاج
        app.run(
            host=host,
            port=port,
            debug=False,  # إيقاف وضع التطوير في الإنتاج
            threaded=True  # تمكين المعالجة المتوازية
        )
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
