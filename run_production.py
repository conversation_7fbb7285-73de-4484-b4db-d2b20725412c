#!/usr/bin/env python3
"""
ملف تشغيل النظام في الإنتاج
يتم استدعاؤه تلقائياً من Render أو أي منصة استضافة أخرى
"""

import os
import sys
from datetime import datetime

def main():
    """الدالة الرئيسية لتشغيل النظام في الإنتاج"""
    
    print("🚀 بدء تشغيل نظام إدارة تعلم القرآن الكريم")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌍 البيئة: Production")
    print(f"🐍 Python: {sys.version}")
    
    try:
        # استيراد التطبيق
        from app import app, db
        
        # إعداد قاعدة البيانات فقط - بدون بيانات افتراضية
        print("\n🔧 إعداد قاعدة البيانات للإنتاج...")
        try:
            with app.app_context():
                # إنشاء الجداول فقط
                db.create_all()
                print("✅ تم إنشاء جداول قاعدة البيانات")

                # فحص حالة الإعداد
                from routes.setup import is_setup_completed
                setup_completed = is_setup_completed()

                if setup_completed:
                    print("✅ النظام مُعد مسبقاً وجاهز للعمل")
                else:
                    print("🔧 النظام يحتاج إعداد أولي - سيتم توجيه المستخدمين لصفحة Setup")

        except Exception as e:
            print(f"⚠️ تحذير في إعداد قاعدة البيانات: {str(e)}")
            print("🔄 المتابعة مع الإعدادات الحالية...")
        
        # تشغيل التطبيق
        print("\n🌐 بدء تشغيل الخادم...")
        print("=" * 60)
        
        # الحصول على المنفذ من متغيرات البيئة (Render يستخدم PORT)
        port = int(os.environ.get('PORT', 5000))
        host = os.environ.get('HOST', '0.0.0.0')
        
        print(f"🔗 الخادم يعمل على: {host}:{port}")
        print("✅ النظام جاهز لاستقبال الطلبات")
        
        # تشغيل التطبيق في وضع الإنتاج
        app.run(
            host=host,
            port=port,
            debug=False,  # إيقاف وضع التطوير في الإنتاج
            threaded=True  # تمكين المعالجة المتوازية
        )
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
