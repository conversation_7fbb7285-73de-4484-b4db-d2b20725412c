from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from functools import wraps
from models import User, Package, Subscription, Session, Payment, Notification, AcademySettings, PaymentGateway, EmailSettings, EmailTemplate, EmailLog, SessionRating, SessionSettings, CalendarSettings, UserActionLog, UserNotificationSettings, db
from datetime import datetime, timedelta
from sqlalchemy import func
from utils.user_management import suspend_user, ban_user, disable_user, delete_user, restore_user, activate_user

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def handle_single_session_creation(form_data):
    """Handle creation of a single session"""
    try:
        print("🔍 إنشاء حصة واحدة")

        # Validate required fields
        required_fields = ['teacher_id', 'student_id', 'scheduled_date', 'scheduled_time', 'duration_minutes']
        for field in required_fields:
            if not form_data.get(field, '').strip():
                print(f"❌ الحقل المطلوب '{field}' فارغ")
                flash(f'الحقل {field} مطلوب', 'danger')
                return redirect(url_for('admin.new_session'))

        # Create new session
        scheduled_datetime = datetime.strptime(
            f"{form_data['scheduled_date']} {form_data['scheduled_time']}",
            '%Y-%m-%d %H:%M'
        )

        # Get session settings for provider
        session_settings = SessionSettings.get_settings()

        meeting_provider = form_data.get('meeting_provider', session_settings.default_provider)
        print(f"🔍 إنشاء حصة جديدة:")
        print(f"   Meeting provider: {meeting_provider}")
        print(f"   Default provider: {session_settings.default_provider}")

        # Validate and convert IDs
        try:
            teacher_id_str = form_data.get('teacher_id', '').strip()
            student_id_str = form_data.get('student_id', '').strip()
            subscription_id_str = form_data.get('subscription_id', '').strip()
            duration_str = form_data.get('duration_minutes', '').strip()

            if not teacher_id_str:
                raise ValueError("معرف المعلم فارغ")
            if not student_id_str:
                raise ValueError("معرف الطالب فارغ")
            if not duration_str:
                raise ValueError("مدة الحصة فارغة")

            teacher_id = int(teacher_id_str)
            student_id = int(student_id_str)
            subscription_id = int(subscription_id_str) if subscription_id_str else None
            duration_minutes = int(duration_str)
        except ValueError as e:
            print(f"❌ خطأ في تحويل المعرفات: {str(e)}")
            flash('خطأ في البيانات المرسلة. يرجى التحقق من القيم المدخلة.', 'danger')
            return redirect(url_for('admin.new_session'))

        session = Session(
            teacher_id=teacher_id,
            student_id=student_id,
            subscription_id=subscription_id,
            session_type=form_data.get('session_type', 'scheduled'),
            scheduled_datetime=scheduled_datetime,
            duration_minutes=duration_minutes,
            notes=form_data.get('notes', ''),
            meeting_provider=meeting_provider
        )

        db.session.add(session)
        db.session.flush()  # Get session ID before commit

        # Generate meeting link based on provider
        if session_settings.auto_generate_links:
            print(f"🔍 Generating meeting link for provider: {session.meeting_provider}")
            session.generate_meeting_link()
            print(f"🔗 Generated meeting link: {session.meeting_link}")

        # Commit all changes
        db.session.commit()

        # Send notifications and reminders
        notification_sent = False
        reminder_sent = False

        # Send session notifications for all session types
        try:
            print(f"🔍 فحص إرسال الإشعارات:")
            print(f"   session.session_type: {session.session_type}")
            print(f"   session.subscription_id: {session.subscription_id}")
            print(f"   سيتم إرسال إشعار لجميع الحصص")

            from utils.session_notifications import notify_session_created

            # جلب التعليمات أو السبب من النموذج
            trial_instructions = form_data.get('trial_instructions', '') if session.session_type == 'trial' else None
            makeup_reason = form_data.get('makeup_reason', '') if session.session_type == 'makeup' else None

            print(f"📧 محاولة إرسال إشعار للحصة {session.id}")
            notification_success, notification_message = notify_session_created(
                session,
                trial_instructions=trial_instructions,
                makeup_reason=makeup_reason
            )

            if notification_success:
                notification_sent = True
                session_type_display = 'الاشتراك' if session.subscription_id else session.session_type
                print(f"✅ تم إرسال إشعارات الحصة {session_type_display}: {notification_message}")
            else:
                session_type_display = 'الاشتراك' if session.subscription_id else session.session_type
                print(f"❌ فشل إرسال إشعارات الحصة {session_type_display}: {notification_message}")

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعارات الحصة: {str(e)}")
            import traceback
            traceback.print_exc()

        # Send creation reminders (only if no special notifications were sent)
        try:
            if not notification_sent:
                from utils.session_notifications import notify_session_created
                success = notify_session_created(session)
                if success:
                    reminder_sent = True
                    print(f"✅ تم إرسال إشعار إنشاء الحصة")

        except Exception as e:
            print(f"❌ خطأ في إرسال التذكيرات: {str(e)}")

        # Show appropriate success message
        if notification_sent:
            flash(f'تم جدولة الحصة {session.session_type} بنجاح وإرسال الإشعارات.', 'success')
        elif reminder_sent:
            flash('تم جدولة الحصة بنجاح وإرسال التذكيرات.', 'success')
        else:
            flash('تم جدولة الحصة بنجاح.', 'success')

        return redirect(url_for('admin.sessions'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء جدولة الحصة: {str(e)}', 'danger')
        return redirect(url_for('admin.new_session'))

def handle_bulk_session_creation(form_data):
    """Handle creation of multiple sessions"""
    try:
        print("🚀 بدء معالجة الحصص المتعددة")

        # Get common data with validation
        teacher_id_str = form_data.get('teacher_id', '').strip()
        student_id_str = form_data.get('student_id', '').strip()
        subscription_id_str = form_data.get('subscription_id', '').strip()

        print(f"🔍 القيم الخام:")
        print(f"   teacher_id_str: '{teacher_id_str}'")
        print(f"   student_id_str: '{student_id_str}'")
        print(f"   subscription_id_str: '{subscription_id_str}'")

        # Validate and convert IDs
        if not teacher_id_str:
            print("❌ معرف المعلم فارغ")
            flash('يرجى اختيار المعلم', 'danger')
            return redirect(url_for('admin.new_session'))

        if not student_id_str:
            print("❌ معرف الطالب فارغ")
            flash('يرجى اختيار الطالب', 'danger')
            return redirect(url_for('admin.new_session'))

        try:
            if not teacher_id_str:
                raise ValueError("معرف المعلم فارغ")
            if not student_id_str:
                raise ValueError("معرف الطالب فارغ")

            teacher_id = int(teacher_id_str)
            student_id = int(student_id_str)
            subscription_id = int(subscription_id_str) if subscription_id_str else None
        except ValueError as e:
            print(f"❌ خطأ في تحويل المعرفات: {str(e)}")
            flash('خطأ في البيانات المرسلة. يرجى المحاولة مرة أخرى.', 'danger')
            return redirect(url_for('admin.new_session'))

        session_type = form_data.get('session_type', 'scheduled')
        meeting_provider = form_data.get('meeting_provider', 'google_meet')
        common_notes = form_data.get('notes', '')

        print(f"📊 البيانات الأساسية:")
        print(f"   teacher_id: {teacher_id}")
        print(f"   student_id: {student_id}")
        print(f"   subscription_id: {subscription_id}")
        print(f"   session_type: {session_type}")

        # Get session settings
        session_settings = SessionSettings.get_settings()

        # Get bulk session data
        bulk_dates = form_data.getlist('bulk_dates[]')
        bulk_times = form_data.getlist('bulk_times[]')
        bulk_durations = form_data.getlist('bulk_durations[]')
        bulk_notes = form_data.getlist('bulk_notes[]')

        print(f"📅 بيانات الحصص المتعددة:")
        print(f"   bulk_dates: {bulk_dates}")
        print(f"   bulk_times: {bulk_times}")
        print(f"   bulk_durations: {bulk_durations}")
        print(f"   عدد الحصص: {len(bulk_dates)}")

        if not bulk_dates or len(bulk_dates) == 0:
            print("❌ لا توجد مواعيد للحصص")
            flash('لم يتم العثور على مواعيد للحصص', 'danger')
            return redirect(url_for('admin.new_session'))

        # Validate subscription has enough sessions
        if subscription_id:
            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                print("❌ الاشتراك غير موجود")
                flash('الاشتراك المحدد غير موجود', 'danger')
                return redirect(url_for('admin.new_session'))

            print(f"📊 فحص الاشتراك:")
            print(f"   الحصص المتبقية: {subscription.sessions_remaining}")
            print(f"   الحصص المطلوبة: {len(bulk_dates)}")

            if subscription.sessions_remaining < len(bulk_dates):
                print("❌ عدد الحصص يتجاوز المتبقي")
                flash(f'الاشتراك يحتوي على {subscription.sessions_remaining} حصة متبقية فقط، ولكن تم طلب جدولة {len(bulk_dates)} حصة', 'danger')
                return redirect(url_for('admin.new_session'))

            print("✅ الاشتراك صالح")

        created_sessions = []

        # Check for conflicts before creating sessions
        conflicts = []
        for i in range(len(bulk_dates)):
            try:
                scheduled_datetime = datetime.strptime(
                    f"{bulk_dates[i]} {bulk_times[i]}",
                    '%Y-%m-%d %H:%M'
                )
                duration = int(bulk_durations[i]) if i < len(bulk_durations) else 60

                # Check for teacher conflicts
                teacher_conflict = Session.query.filter(
                    Session.teacher_id == teacher_id,
                    Session.scheduled_datetime == scheduled_datetime,
                    Session.status == 'scheduled'
                ).first()

                # Check for student conflicts
                student_conflict = Session.query.filter(
                    Session.student_id == student_id,
                    Session.scheduled_datetime == scheduled_datetime,
                    Session.status == 'scheduled'
                ).first()

                if teacher_conflict:
                    conflicts.append(f"الحصة {i+1}: المعلم لديه حصة أخرى في نفس الوقت")
                if student_conflict:
                    conflicts.append(f"الحصة {i+1}: الطالب لديه حصة أخرى في نفس الوقت")

            except Exception as e:
                conflicts.append(f"الحصة {i+1}: خطأ في التاريخ أو الوقت")

        if conflicts:
            flash('تم العثور على تعارضات في المواعيد:\n' + '\n'.join(conflicts), 'danger')
            return redirect(url_for('admin.new_session'))

        # Create sessions
        print(f"🔄 بدء إنشاء {len(bulk_dates)} حصة...")

        for i in range(len(bulk_dates)):
            try:
                print(f"📝 إنشاء الحصة {i+1}/{len(bulk_dates)}")

                scheduled_datetime = datetime.strptime(
                    f"{bulk_dates[i]} {bulk_times[i]}",
                    '%Y-%m-%d %H:%M'
                )

                # Handle duration with validation
                duration_str = bulk_durations[i] if i < len(bulk_durations) else '60'
                try:
                    duration = int(duration_str.strip()) if duration_str.strip() else 60
                except (ValueError, AttributeError):
                    print(f"⚠️ قيمة مدة غير صالحة للحصة {i+1}: '{duration_str}', استخدام القيمة الافتراضية 60")
                    duration = 60

                session_notes = bulk_notes[i] if i < len(bulk_notes) else ''

                print(f"   التاريخ: {bulk_dates[i]}")
                print(f"   الوقت: {bulk_times[i]}")
                print(f"   المدة: {duration} دقيقة")

                # Combine common notes with session-specific notes
                final_notes = common_notes
                if session_notes:
                    final_notes = f"{common_notes}\n{session_notes}" if common_notes else session_notes

                session = Session(
                    teacher_id=teacher_id,
                    student_id=student_id,
                    subscription_id=subscription_id,
                    session_type=session_type,
                    scheduled_datetime=scheduled_datetime,
                    duration_minutes=duration,
                    notes=final_notes,
                    meeting_provider=meeting_provider
                )

                db.session.add(session)
                db.session.flush()  # Get session ID before commit

                print(f"   تم إنشاء الحصة بـ ID: {session.id}")

                # Generate meeting link
                if session_settings.auto_generate_links:
                    print(f"   إنشاء رابط الحصة...")
                    session.generate_meeting_link()
                    print(f"   رابط الحصة: {session.meeting_link}")

                created_sessions.append(session)
                print(f"✅ تم إنشاء الحصة {i+1} بنجاح")

            except Exception as e:
                print(f"❌ خطأ في إنشاء الحصة {i+1}: {str(e)}")
                continue

        # Commit all sessions
        print(f"💾 حفظ {len(created_sessions)} حصة في قاعدة البيانات...")
        db.session.commit()
        print("✅ تم حفظ جميع الحصص بنجاح")

        # Send session notifications for all sessions
        print("📧 إرسال إشعارات الحصص...")
        notification_success_count = 0
        for session in created_sessions:
            try:
                print(f"🔍 فحص الحصة {session.id}:")
                print(f"   session.session_type: {session.session_type}")
                print(f"   session.subscription_id: {session.subscription_id}")
                print(f"   سيتم إرسال إشعار لجميع الحصص")

                from utils.session_notifications import notify_session_created
                from utils.session_email_notifications import send_trial_session_email, send_makeup_session_email

                # جلب التعليمات أو السبب من النموذج
                trial_instructions = common_notes if session.session_type == 'trial' else None
                makeup_reason = common_notes if session.session_type == 'makeup' else None

                # إرسال إشعارات البريد الإلكتروني حسب نوع الحصة
                if session.session_type == 'trial':
                    try:
                        success, message = send_trial_session_email(session)
                        if success:
                            print(f"✅ تم إرسال إشعار الحصة التجريبية")
                        else:
                            print(f"❌ فشل إرسال إشعار الحصة التجريبية: {message}")
                    except Exception as e:
                        print(f"❌ خطأ في إرسال إشعار الحصة التجريبية: {str(e)}")

                elif session.session_type == 'makeup':
                    try:
                        success, message = send_makeup_session_email(session)
                        if success:
                            print(f"✅ تم إرسال إشعار الحصة التعويضية")
                        else:
                            print(f"❌ فشل إرسال إشعار الحصة التعويضية: {message}")
                    except Exception as e:
                        print(f"❌ خطأ في إرسال إشعار الحصة التعويضية: {str(e)}")

                print(f"📧 محاولة إرسال إشعار للحصة {session.id}")
                notification_success, notification_message = notify_session_created(
                    session,
                    trial_instructions=trial_instructions,
                    makeup_reason=makeup_reason
                )

                if notification_success:
                    notification_success_count += 1
                    session_type_display = 'الاشتراك' if session.subscription_id else session.session_type
                    print(f"✅ تم إرسال إشعار للحصة {session.id} ({session_type_display})")
                else:
                    session_type_display = 'الاشتراك' if session.subscription_id else session.session_type
                    print(f"❌ فشل إرسال إشعار للحصة {session.id} ({session_type_display}): {notification_message}")

            except Exception as e:
                print(f"❌ خطأ في إرسال إشعار للحصة {session.id}: {str(e)}")
                import traceback
                traceback.print_exc()

        # Send reminders for all created sessions
        print("📧 إرسال التذكيرات...")
        reminder_success_count = 0
        for session in created_sessions:
            try:
                from utils.session_notifications import notify_session_created
                success = notify_session_created(session)
                if success:
                    reminder_success_count += 1
                    print(f"✅ تم إرسال تذكير للحصة {session.id}")
                else:
                    print(f"❌ فشل إرسال تذكير للحصة {session.id}")
            except Exception as e:
                print(f"❌ خطأ في إرسال تذكير للحصة {session.id}: {str(e)}")

        # Show success message
        if len(created_sessions) > 0:
            print(f"🎉 تم إنشاء {len(created_sessions)} حصة بنجاح")

            # تحديد نوع الرسالة حسب ما تم إرساله
            if notification_success_count > 0:
                flash(f'تم جدولة {len(created_sessions)} حصة بنجاح وإرسال {notification_success_count} إشعار خاص.', 'success')
            elif reminder_success_count > 0:
                flash(f'تم جدولة {len(created_sessions)} حصة بنجاح وإرسال {reminder_success_count} تذكير.', 'success')
            else:
                flash(f'تم جدولة {len(created_sessions)} حصة بنجاح.', 'success')
        else:
            print("❌ لم يتم إنشاء أي حصص")
            flash('لم يتم إنشاء أي حصص. يرجى التحقق من البيانات المدخلة.', 'warning')

        return redirect(url_for('admin.sessions'))

    except Exception as e:
        print(f"❌ خطأ عام في معالجة الحصص المتعددة: {str(e)}")
        db.session.rollback()
        flash(f'حدث خطأ أثناء جدولة الحصص: {str(e)}', 'danger')
        return redirect(url_for('admin.new_session'))

@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    # Statistics
    total_users = User.query.count()
    total_teachers = User.query.filter_by(role='teacher').count()
    total_students = User.query.filter_by(role='student').count()
    pending_users = User.query.filter_by(status='pending').count()
    suspended_users = User.query.filter_by(status='suspended').count()
    banned_users = User.query.filter_by(status='banned').count()
    inactive_users = User.query.filter_by(status='inactive').count()
    deleted_users = User.query.filter_by(status='deleted').count()
    
    total_packages = Package.query.count()
    active_packages = Package.query.filter_by(is_active=True).count()
    
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    pending_subscriptions = Subscription.query.filter_by(status='pending').count()
    
    # Calculate total revenue using the new helper method
    total_revenue = Payment.get_total_revenue()
    
    total_sessions = Session.query.count()
    completed_sessions = Session.query.filter_by(status='completed').count()
    scheduled_sessions = Session.query.filter_by(status='scheduled').count()

    # New session type statistics for admin
    trial_sessions = Session.get_admin_sessions_count_by_type('trial')
    makeup_sessions = Session.get_admin_sessions_count_by_type('makeup')
    
    # Recent activities
    recent_users = User.query.filter_by(status='pending').order_by(User.created_at.desc()).limit(5).all()
    recent_subscriptions = Subscription.query.filter_by(status='pending').order_by(Subscription.purchase_date.desc()).limit(5).all()
    upcoming_sessions = Session.query.filter(
        Session.scheduled_datetime >= datetime.now(),
        Session.status == 'scheduled'
    ).order_by(Session.scheduled_datetime).limit(5).all()
    
    # Calculate monthly revenue using the new helper method
    monthly_revenue = Payment.get_monthly_revenue()
    
    stats = {
        'total_users': total_users,
        'total_teachers': total_teachers,
        'total_students': total_students,
        'pending_users': pending_users,
        'suspended_users': suspended_users,
        'banned_users': banned_users,
        'inactive_users': inactive_users,
        'deleted_users': deleted_users,
        'total_packages': total_packages,
        'active_packages': active_packages,
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'pending_subscriptions': pending_subscriptions,
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'total_sessions': total_sessions,
        'completed_sessions': completed_sessions,
        'scheduled_sessions': scheduled_sessions,
        'trial_sessions': trial_sessions,
        'makeup_sessions': makeup_sessions
    }
    
    return render_template('admin/dashboard.html', 
                         stats=stats,
                         recent_users=recent_users,
                         recent_subscriptions=recent_subscriptions,
                         upcoming_sessions=upcoming_sessions)

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    page = request.args.get('page', 1, type=int)
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    search = request.args.get('search', '')
    
    query = User.query
    
    if role_filter:
        query = query.filter_by(role=role_filter)
    if status_filter:
        query = query.filter_by(status=status_filter)
    if search:
        query = query.filter(
            (User.first_name.contains(search)) |
            (User.last_name.contains(search)) |
            (User.email.contains(search))
        )
    
    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/users.html', users=users, 
                         role_filter=role_filter, 
                         status_filter=status_filter, 
                         search=search)

@admin_bp.route('/users/<int:user_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_user(user_id):
    user = User.query.get_or_404(user_id)

    # التحقق من أن المستخدم في حالة pending (تفعيل لأول مرة)
    is_first_time_activation = user.status == 'pending'

    user.status = 'approved'
    db.session.commit()

    # Create notification
    notification = Notification(
        user_id=user.id,
        title='تم قبول حسابك',
        message='تم قبول حسابك بنجاح. يمكنك الآن الوصول إلى جميع الخدمات.',
        notification_type='account'
    )
    db.session.add(notification)
    db.session.commit()

    # إرسال إشعار بريدي للتفعيل الأول
    if is_first_time_activation:
        try:
            from utils.registration_notifications import send_account_activation_notification
            from utils.user_management import get_academy_variables

            # رسالة ترحيبية للتفعيل الأول
            admin_message = request.form.get('admin_message',
                'مرحباً بك في أكاديميتنا! تم مراجعة طلب التسجيل الخاص بك والموافقة عليه. نتطلع لرؤيتك تحقق أهدافك التعليمية معنا.')

            email_success, email_message = send_account_activation_notification(user, admin_message)

            if email_success:
                print(f"✅ تم إرسال إشعار التفعيل الأول إلى {user.email}")
            else:
                print(f"⚠️ تم تفعيل الحساب لكن فشل إرسال البريد: {email_message}")

        except Exception as e:
            print(f"⚠️ تم تفعيل الحساب لكن خطأ في إرسال البريد: {str(e)}")

    flash(f'تم قبول حساب {user.full_name} بنجاح.', 'success')
    return redirect(url_for('admin.users'))

@admin_bp.route('/users/<int:user_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_user(user_id):
    user = User.query.get_or_404(user_id)
    user.status = 'rejected'
    db.session.commit()

    flash(f'تم رفض حساب {user.full_name}.', 'warning')
    return redirect(url_for('admin.users'))


@admin_bp.route('/users/<int:user_id>/suspend', methods=['POST'])
@login_required
@admin_required
def suspend_user_route(user_id):
    """Suspend user temporarily"""
    reason = request.form.get('reason', '')
    suspension_days = request.form.get('suspension_days', type=int)

    if not reason:
        flash('يجب إدخال سبب الحظر.', 'danger')
        return redirect(url_for('admin.users'))

    success, message = suspend_user(user_id, current_user.id, reason, suspension_days)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    return redirect(url_for('admin.users'))


@admin_bp.route('/users/<int:user_id>/ban', methods=['POST'])
@login_required
@admin_required
def ban_user_route(user_id):
    """Ban user permanently"""
    reason = request.form.get('reason', '')

    if not reason:
        flash('يجب إدخال سبب الحظر النهائي.', 'danger')
        return redirect(url_for('admin.users'))

    success, message = ban_user(user_id, current_user.id, reason)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    return redirect(url_for('admin.users'))


@admin_bp.route('/users/<int:user_id>/disable', methods=['POST'])
@login_required
@admin_required
def disable_user_route(user_id):
    """Disable user account"""
    reason = request.form.get('reason', '')

    success, message = disable_user(user_id, current_user.id, reason)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    return redirect(url_for('admin.users'))


@admin_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user_route(user_id):
    """Delete user account"""
    reason = request.form.get('reason', '')
    permanent = request.form.get('permanent') == 'true'

    success, message = delete_user(user_id, current_user.id, reason, permanent)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    return redirect(url_for('admin.users'))


@admin_bp.route('/users/<int:user_id>/restore', methods=['POST'])
@login_required
@admin_required
def restore_user_route(user_id):
    """Restore deleted user account"""
    success, message = restore_user(user_id, current_user.id)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    return redirect(url_for('admin.users'))


@admin_bp.route('/users/<int:user_id>/activate', methods=['POST'])
@login_required
@admin_required
def activate_user_route(user_id):
    """Activate suspended/banned/inactive user"""
    success, message = activate_user(user_id, current_user.id)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    return redirect(url_for('admin.users'))

@admin_bp.route('/packages')
@login_required
@admin_required
def packages():
    packages = Package.query.order_by(Package.created_at.desc()).all()
    return render_template('admin/packages.html', packages=packages)

@admin_bp.route('/packages/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_package():
    if request.method == 'POST':
        try:
            package = Package(
                name=request.form.get('name'),
                description=request.form.get('description'),
                price=float(request.form.get('price', 0)),
                duration_days=int(request.form.get('duration_days', 30)),
                sessions_count=int(request.form.get('sessions_count', 1)),
                session_duration=int(request.form.get('session_duration', 60)),
                features=request.form.get('features', ''),
                is_active=bool(request.form.get('is_active'))
            )

            db.session.add(package)
            db.session.commit()

            flash('تم إنشاء الباقة بنجاح.', 'success')
            return redirect(url_for('admin.packages'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في إنشاء الباقة: {str(e)}', 'danger')
            return redirect(url_for('admin.packages'))

    return render_template('admin/package_form.html', package=None)

@admin_bp.route('/packages/<int:package_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_package(package_id):
    package = Package.query.get_or_404(package_id)

    if request.method == 'POST':
        try:
            package.name = request.form.get('name')
            package.description = request.form.get('description')
            package.price = float(request.form.get('price', 0))
            package.duration_days = int(request.form.get('duration_days', 30))
            package.sessions_count = int(request.form.get('sessions_count', 1))
            package.session_duration = int(request.form.get('session_duration', 60))
            package.features = request.form.get('features', '')
            package.is_active = bool(request.form.get('is_active'))

            db.session.commit()

            flash('تم تحديث الباقة بنجاح.', 'success')
            return redirect(url_for('admin.packages'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في تحديث الباقة: {str(e)}', 'danger')
            return redirect(url_for('admin.packages'))

    return render_template('admin/package_form.html', package=package)

@admin_bp.route('/packages/<int:package_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_package(package_id):
    """Delete a package with safety checks"""
    package = Package.query.get_or_404(package_id)

    try:
        print(f"🗑️ محاولة حذف الباقة: {package.name} (ID: {package_id})")
        print(f"📋 نوع الطلب: {'AJAX' if request.headers.get('X-Requested-With') == 'XMLHttpRequest' else 'Form'}")
        print(f"📋 CSRF token في الطلب: {'موجود' if request.form.get('csrf_token') else 'غير موجود'}")

        # CSRF token is automatically validated by Flask-WTF for POST requests

        # التحقق من وجود اشتراكات مرتبطة
        subscriptions_count = len(package.subscriptions)
        if subscriptions_count > 0:
            print(f"❌ لا يمكن حذف الباقة - توجد {subscriptions_count} اشتراك مرتبط")
            message = f'لا يمكن حذف الباقة "{package.name}" لأنها تحتوي على {subscriptions_count} اشتراك. يجب حذف الاشتراكات أولاً.'

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': message}), 400
            else:
                flash(message, 'danger')
                return redirect(url_for('admin.packages'))

        # التحقق من وجود حصص مرتبطة (عبر الاشتراكات)
        sessions_count = Session.query.join(Subscription).filter(Subscription.package_id == package_id).count()
        if sessions_count > 0:
            print(f"❌ لا يمكن حذف الباقة - توجد {sessions_count} حصة مرتبطة")
            message = f'لا يمكن حذف الباقة "{package.name}" لأنها تحتوي على {sessions_count} حصة مرتبطة. يجب حذف الحصص والاشتراكات أولاً.'

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'message': message}), 400
            else:
                flash(message, 'danger')
                return redirect(url_for('admin.packages'))

        # حفظ اسم الباقة للرسالة
        package_name = package.name

        # حذف الباقة
        db.session.delete(package)
        db.session.commit()

        print(f"✅ تم حذف الباقة '{package_name}' بنجاح")
        success_message = f'تم حذف الباقة "{package_name}" بنجاح.'

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': True, 'message': success_message})
        else:
            flash(success_message, 'success')
            return redirect(url_for('admin.packages'))

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حذف الباقة: {str(e)}")
        error_message = f'حدث خطأ في حذف الباقة: {str(e)}'

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': error_message}), 500
        else:
            flash(error_message, 'danger')
            return redirect(url_for('admin.packages'))

@admin_bp.route('/packages/<int:package_id>/toggle', methods=['POST'])
@login_required
@admin_required
def toggle_package_status(package_id):
    """Toggle package active status"""
    package = Package.query.get_or_404(package_id)

    try:
        print(f"🔄 طلب تبديل حالة الباقة: {package.name} (ID: {package_id})")
        print(f"📊 الحالة الحالية: {package.is_active}")
        print(f"📋 CSRF token في الطلب: {'موجود' if request.form.get('csrf_token') else 'غير موجود'}")

        # CSRF token is automatically validated by Flask-WTF for POST requests
        # No need for manual validation

        # Simply toggle the current status
        new_status = not package.is_active
        print(f"🎯 الحالة الجديدة: {new_status}")

        # Update package status
        package.is_active = new_status
        db.session.commit()

        status_text = 'تم تفعيل' if new_status else 'تم إيقاف'
        print(f"✅ {status_text} الباقة '{package.name}' بنجاح")

        # Return success response
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': f'{status_text} الباقة بنجاح',
                'is_active': new_status,
                'package_name': package.name
            })
        else:
            flash(f'{status_text} الباقة بنجاح', 'success')
            return redirect(url_for('admin.packages'))

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في تبديل حالة الباقة: {str(e)}")

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': False,
                'message': f'حدث خطأ: {str(e)}'
            }), 500
        else:
            flash(f'حدث خطأ: {str(e)}', 'danger')
            return redirect(url_for('admin.packages'))

@admin_bp.route('/subscriptions')
@login_required
@admin_required
def subscriptions():
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    
    query = Subscription.query
    
    if status_filter:
        query = query.filter_by(status=status_filter)
    
    subscriptions = query.order_by(Subscription.purchase_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/subscriptions.html', 
                         subscriptions=subscriptions, 
                         status_filter=status_filter)

@admin_bp.route('/subscriptions/<int:subscription_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_subscription(subscription_id):
    subscription = Subscription.query.get_or_404(subscription_id)

    # Check if subscription can be approved
    if subscription.status not in ['pending', 'paid_pending_approval']:
        flash('لا يمكن الموافقة على هذا الاشتراك.', 'error')
        return redirect(url_for('admin.subscriptions'))

    # Activate subscription
    subscription.activate_subscription()

    # If it was a paid subscription, mark payment as completed
    if subscription.status == 'paid_pending_approval':
        payment = Payment.query.filter_by(subscription_id=subscription.id).first()
        if payment:
            payment.status = 'completed'

    db.session.commit()

    # Get admin message from form
    admin_message = request.form.get('admin_message', '')

    # Create notification
    notification = Notification(
        user_id=subscription.user_id,
        title='تم تفعيل اشتراكك',
        message=f'تم تفعيل اشتراكك في باقة {subscription.package.name} بنجاح.',
        notification_type='subscription'
    )
    db.session.add(notification)
    db.session.commit()

    # Send email notification
    try:
        from utils.subscription_notifications import send_subscription_notification
        success, message = send_subscription_notification(
            subscription,
            'activated',
            admin_message=admin_message
        )
        if success:
            flash(f'تم تفعيل اشتراك {subscription.user.full_name} وإرسال إشعار بالبريد الإلكتروني.', 'success')
        else:
            flash(f'تم تفعيل اشتراك {subscription.user.full_name} لكن فشل إرسال البريد: {message}', 'warning')
    except Exception as e:
        flash(f'تم تفعيل اشتراك {subscription.user.full_name} لكن حدث خطأ في إرسال البريد: {str(e)}', 'warning')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/subscriptions/<int:subscription_id>/cancel', methods=['POST'])
@login_required
@admin_required
def cancel_subscription(subscription_id):
    """إلغاء اشتراك مع إرسال إشعار بالبريد"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # Check if subscription can be cancelled
    if subscription.status in ['cancelled', 'expired']:
        flash('هذا الاشتراك ملغي أو منتهي بالفعل.', 'error')
        return redirect(url_for('admin.subscriptions'))

    try:
        # Get cancellation details from form
        cancellation_reason = request.form.get('cancellation_reason', '')
        admin_message = request.form.get('admin_message', '')
        refund_info = request.form.get('refund_info', '')

        # Update subscription status
        old_status = subscription.status
        subscription.status = 'cancelled'

        # Create notification
        notification = Notification(
            user_id=subscription.user_id,
            title='تم إلغاء اشتراكك',
            message=f'تم إلغاء اشتراكك في باقة {subscription.package.name}.',
            notification_type='subscription'
        )
        db.session.add(notification)
        db.session.commit()

        # Send email notification
        try:
            from utils.subscription_notifications import send_subscription_notification
            success, message = send_subscription_notification(
                subscription,
                'cancelled',
                reason=cancellation_reason,
                admin_message=admin_message,
                refund_info=refund_info
            )
            if success:
                flash(f'تم إلغاء اشتراك {subscription.user.full_name} وإرسال إشعار بالبريد الإلكتروني.', 'success')
            else:
                flash(f'تم إلغاء اشتراك {subscription.user.full_name} لكن فشل إرسال البريد: {message}', 'warning')
        except Exception as e:
            flash(f'تم إلغاء اشتراك {subscription.user.full_name} لكن حدث خطأ في إرسال البريد: {str(e)}', 'warning')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إلغاء الاشتراك: {str(e)}', 'danger')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/subscriptions/<int:subscription_id>/suspend', methods=['POST'])
@login_required
@admin_required
def suspend_subscription(subscription_id):
    """إيقاف اشتراك مؤقتاً مع إرسال إشعار بالبريد"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # Check if subscription can be suspended
    if subscription.status != 'active':
        flash('يمكن إيقاف الاشتراكات النشطة فقط.', 'error')
        return redirect(url_for('admin.subscriptions'))

    try:
        # Get suspension details from form
        suspension_reason = request.form.get('suspension_reason', '')
        admin_message = request.form.get('admin_message', '')
        reactivation_steps = request.form.get('reactivation_steps', '')

        # Update subscription status
        old_status = subscription.status
        subscription.status = 'suspended'

        # Create notification
        notification = Notification(
            user_id=subscription.user_id,
            title='تم إيقاف اشتراكك مؤقتاً',
            message=f'تم إيقاف اشتراكك في باقة {subscription.package.name} مؤقتاً.',
            notification_type='subscription'
        )
        db.session.add(notification)
        db.session.commit()

        # Send email notification
        try:
            from utils.subscription_notifications import send_subscription_notification
            success, message = send_subscription_notification(
                subscription,
                'suspended',
                reason=suspension_reason,
                admin_message=admin_message,
                reactivation_steps=reactivation_steps
            )
            if success:
                flash(f'تم إيقاف اشتراك {subscription.user.full_name} وإرسال إشعار بالبريد الإلكتروني.', 'success')
            else:
                flash(f'تم إيقاف اشتراك {subscription.user.full_name} لكن فشل إرسال البريد: {message}', 'warning')
        except Exception as e:
            flash(f'تم إيقاف اشتراك {subscription.user.full_name} لكن حدث خطأ في إرسال البريد: {str(e)}', 'warning')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إيقاف الاشتراك: {str(e)}', 'danger')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/subscriptions/<int:subscription_id>/reactivate', methods=['POST'])
@login_required
@admin_required
def reactivate_subscription(subscription_id):
    """إعادة تفعيل اشتراك موقوف مؤقتاً مع إرسال إشعار بالبريد"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # Check if subscription can be reactivated
    if subscription.status != 'suspended':
        flash('يمكن إعادة تفعيل الاشتراكات الموقوفة مؤقتاً فقط.', 'error')
        return redirect(url_for('admin.subscriptions'))

    try:
        # Get admin message from form
        admin_message = request.form.get('admin_message', '')

        # Update subscription status
        old_status = subscription.status
        subscription.status = 'active'

        # Create notification
        notification = Notification(
            user_id=subscription.user_id,
            title='تم إعادة تفعيل اشتراكك',
            message=f'تم إعادة تفعيل اشتراكك في باقة {subscription.package.name} بنجاح.',
            notification_type='subscription'
        )
        db.session.add(notification)
        db.session.commit()

        # Send email notification
        try:
            from utils.subscription_notifications import send_subscription_notification
            success, message = send_subscription_notification(
                subscription,
                'reactivated',
                admin_message=admin_message
            )
            if success:
                flash(f'تم إعادة تفعيل اشتراك {subscription.user.full_name} وإرسال إشعار بالبريد الإلكتروني.', 'success')
            else:
                flash(f'تم إعادة تفعيل اشتراك {subscription.user.full_name} لكن فشل إرسال البريد: {message}', 'warning')
        except Exception as e:
            flash(f'تم إعادة تفعيل اشتراك {subscription.user.full_name} لكن حدث خطأ في إرسال البريد: {str(e)}', 'warning')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إعادة تفعيل الاشتراك: {str(e)}', 'danger')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/subscriptions/<int:subscription_id>/approve-payment', methods=['POST'])
@login_required
@admin_required
def approve_payment(subscription_id):
    """Approve a paid subscription"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # Check if subscription can be approved
    if subscription.status != 'paid_pending_approval':
        flash('لا يمكن الموافقة على هذا الاشتراك.', 'error')
        return redirect(url_for('admin.subscriptions'))

    try:
        # Activate subscription
        subscription.activate_subscription()

        # Update payment status to completed
        payment = Payment.query.filter_by(subscription_id=subscription.id).first()
        if payment:
            payment.status = 'completed'

        db.session.commit()

        # Send subscription activation email
        try:
            from utils.subscription_notifications import send_subscription_notification
            from utils.user_management import get_academy_variables

            # رسالة اختيارية من الإدمن
            admin_message = request.form.get('admin_message', 'تم تفعيل اشتراكك بنجاح! نرحب بك في أكاديميتنا ونتمنى لك تجربة تعليمية مثمرة.')

            success, message = send_subscription_notification(
                subscription,
                'activated',
                admin_message=admin_message
            )

            if success:
                print(f"✅ تم إرسال إشعار تفعيل الاشتراك إلى {subscription.user.email}")
            else:
                print(f"❌ فشل إرسال إشعار تفعيل الاشتراك: {message}")

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار تفعيل الاشتراك: {str(e)}")

        # Get custom message from form
        custom_message = request.form.get('message', '')

        # Create notification for student
        if custom_message:
            message = custom_message
        else:
            message = f'تم قبول دفعتك وتفعيل اشتراكك في باقة {subscription.package.name} بنجاح. مرحباً بك في الأكاديمية!'

        notification = Notification(
            user_id=subscription.user_id,
            title='تم الموافقة على دفعتك',
            message=message,
            notification_type='payment'
        )
        db.session.add(notification)
        db.session.commit()

        # Send email notification
        try:
            from utils.subscription_notifications import send_subscription_notification
            success, email_message = send_subscription_notification(
                subscription,
                'activated',
                admin_message=custom_message
            )
            if success:
                flash(f'تم قبول دفعة {subscription.user.full_name} وتفعيل الاشتراك وإرسال إشعار بالبريد الإلكتروني.', 'success')
            else:
                flash(f'تم قبول دفعة {subscription.user.full_name} وتفعيل الاشتراك لكن فشل إرسال البريد: {email_message}', 'warning')
        except Exception as e:
            flash(f'تم قبول دفعة {subscription.user.full_name} وتفعيل الاشتراك لكن حدث خطأ في إرسال البريد: {str(e)}', 'warning')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الموافقة على الدفع: {str(e)}', 'danger')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/subscriptions/<int:subscription_id>/reject-payment', methods=['POST'])
@login_required
@admin_required
def reject_payment(subscription_id):
    """Reject a paid subscription"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # Check if subscription can be rejected
    if subscription.status != 'paid_pending_approval':
        flash('لا يمكن رفض هذا الاشتراك.', 'error')
        return redirect(url_for('admin.subscriptions'))

    try:
        # Get rejection details from form
        reason = request.form.get('reason', '')
        custom_message = request.form.get('message', '')

        # Update subscription status to rejected
        subscription.status = 'rejected'

        # Update payment status to failed
        payment = Payment.query.filter_by(subscription_id=subscription.id).first()
        if payment:
            payment.status = 'rejected'
            payment.gateway_response = f'Rejected by admin. Reason: {reason}'

        db.session.commit()

        # Create notification for student
        notification = Notification(
            user_id=subscription.user_id,
            title='تم رفض دفعتك',
            message=custom_message,
            notification_type='payment'
        )
        db.session.add(notification)
        db.session.commit()

        # Send email notification
        try:
            from utils.email_service import EmailService
            from models import AcademySettings

            email_service = EmailService()
            academy_settings = AcademySettings.query.first()

            # إعداد متغيرات البريد الإلكتروني
            academy_vars = {
                'academy_name': academy_settings.academy_name if academy_settings else 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email if academy_settings else '<EMAIL>',
                'academy_phone': academy_settings.contact_phone if academy_settings else '',
                'academy_logo': academy_settings.get_logo_url() if academy_settings else '',
                'contact_url': f"{request.url_root}contact" if request else '#'
            }

            email_vars = {
                'user_name': subscription.user.full_name,
                'package_name': subscription.package.name,
                'amount': f"{subscription.package.price:.2f}",
                'rejection_reason': reason,
                'admin_message': custom_message,
                'refund_info': 'سيتم رد المبلغ خلال 3-5 أيام عمل إذا كان مدفوعاً مسبقاً',
                **academy_vars
            }

            success, message = email_service.send_template_email(
                subscription.user.email,
                'payment_rejected',
                email_vars
            )

            if success:
                flash(f'تم رفض دفعة {subscription.user.full_name} وإرسال إشعار بالبريد الإلكتروني.', 'warning')
            else:
                flash(f'تم رفض دفعة {subscription.user.full_name} لكن فشل إرسال البريد: {message}', 'warning')
        except Exception as e:
            flash(f'تم رفض دفعة {subscription.user.full_name} لكن حدث خطأ في إرسال البريد: {str(e)}', 'warning')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء رفض الدفع: {str(e)}', 'danger')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/subscriptions/<int:subscription_id>/reject-manual', methods=['POST'])
@login_required
@admin_required
def reject_manual_payment(subscription_id):
    """Reject a manual review subscription"""
    subscription = Subscription.query.get_or_404(subscription_id)

    # Check if subscription can be rejected
    if subscription.status != 'pending':
        flash('لا يمكن رفض هذا الاشتراك.', 'error')
        return redirect(url_for('admin.subscriptions'))

    try:
        # Get rejection details from form
        reason = request.form.get('reason', '')
        custom_message = request.form.get('message', '')

        # Update subscription status to rejected
        subscription.status = 'rejected'

        # Update payment status to failed if exists
        payment = Payment.query.filter_by(subscription_id=subscription.id).first()
        if payment:
            payment.status = 'rejected'
            payment.gateway_response = f'Rejected by admin. Reason: {reason}'

        db.session.commit()

        # Create notification for student
        notification = Notification(
            user_id=subscription.user_id,
            title='تم رفض اشتراكك',
            message=custom_message if custom_message else f'تم رفض اشتراكك في باقة {subscription.package.name}. السبب: {reason}',
            notification_type='subscription'
        )
        db.session.add(notification)
        db.session.commit()

        # Send email notification
        try:
            from utils.email_service import EmailService
            from models import AcademySettings

            email_service = EmailService()
            academy_settings = AcademySettings.query.first()

            # إعداد متغيرات البريد الإلكتروني
            academy_vars = {
                'academy_name': academy_settings.academy_name if academy_settings else 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email if academy_settings else '<EMAIL>',
                'academy_phone': academy_settings.contact_phone if academy_settings else '',
                'academy_logo': academy_settings.get_logo_url() if academy_settings else '',
                'contact_url': f"{request.url_root}contact" if request else '#'
            }

            email_vars = {
                'user_name': subscription.user.full_name,
                'package_name': subscription.package.name,
                'amount': f"{subscription.package.price:.2f}",
                'rejection_reason': reason,
                'admin_message': custom_message,
                'refund_info': 'لا يوجد مبلغ للاسترداد (مراجعة يدوية)',
                **academy_vars
            }

            success, message = email_service.send_template_email(
                subscription.user.email,
                'payment_rejected',
                email_vars
            )

            if success:
                flash(f'تم رفض اشتراك {subscription.user.full_name} وإرسال إشعار بالبريد الإلكتروني.', 'warning')
            else:
                flash(f'تم رفض اشتراك {subscription.user.full_name} لكن فشل إرسال البريد: {message}', 'warning')
        except Exception as e:
            flash(f'تم رفض اشتراك {subscription.user.full_name} لكن حدث خطأ في إرسال البريد: {str(e)}', 'warning')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء رفض الاشتراك: {str(e)}', 'danger')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/subscriptions/<int:subscription_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_subscription(subscription_id):
    """Delete a subscription and all related data"""
    subscription = Subscription.query.get_or_404(subscription_id)

    try:
        # Store info for notification
        student_name = subscription.user.full_name
        package_name = subscription.package.name
        user_id = subscription.user_id

        # Delete related sessions first (due to foreign key constraints)
        sessions = Session.query.filter_by(subscription_id=subscription_id).all()
        for session in sessions:
            # Delete session ratings first
            SessionRating.query.filter_by(session_id=session.id).delete()
            db.session.delete(session)

        # Delete related payments
        payments = Payment.query.filter_by(subscription_id=subscription_id).all()
        for payment in payments:
            db.session.delete(payment)

        # Delete the subscription
        db.session.delete(subscription)

        # Create notification for the student
        notification = Notification(
            user_id=user_id,
            title='تم حذف اشتراكك',
            message=f'تم حذف اشتراكك في باقة {package_name}. للاستفسار يرجى التواصل مع الإدارة.',
            notification_type='subscription'
        )
        db.session.add(notification)

        db.session.commit()

        flash(f'تم حذف اشتراك {student_name} في باقة {package_name} بنجاح.', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الاشتراك: {str(e)}', 'danger')

    return redirect(url_for('admin.subscriptions'))

@admin_bp.route('/sessions')
@login_required
@admin_required
def sessions():
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    type_filter = request.args.get('type', '')
    
    query = Session.query
    if status_filter:
        query = query.filter_by(status=status_filter)
    if type_filter:
        query = query.filter_by(session_type=type_filter)
    
    sessions = query.order_by(Session.scheduled_datetime.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/sessions.html',
                         sessions=sessions,
                         status_filter=status_filter,
                         type_filter=type_filter)

@admin_bp.route('/sessions/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_session():
    print(f"🔍 New session route called - Method: {request.method}")

    if request.method == 'POST':
        # Debug: Print all form data
        print(f"🔍 Form data received:")
        for key, value in request.form.items():
            print(f"   {key}: {value}")

        # Check if bulk scheduling is enabled
        bulk_schedule_value = request.form.get('bulk_schedule')
        bulk_schedule = bulk_schedule_value in ['on', 'true', '1', 'checked']

        print(f"🔍 Bulk schedule check:")
        print(f"   bulk_schedule value: '{bulk_schedule_value}'")
        print(f"   bulk_schedule boolean: {bulk_schedule}")

        # Check for bulk session data
        bulk_dates = request.form.getlist('bulk_dates[]')
        bulk_times = request.form.getlist('bulk_times[]')

        print(f"🔍 Bulk session data:")
        print(f"   bulk_dates: {bulk_dates}")
        print(f"   bulk_times: {bulk_times}")

        # If we have bulk data, treat as bulk regardless of checkbox
        if bulk_dates and len(bulk_dates) > 0:
            print("🔄 Detected bulk session data - using bulk creation")
            return handle_bulk_session_creation(request.form)
        elif bulk_schedule:
            print("🔄 Bulk schedule enabled but no data - redirecting to form")
            flash('لم يتم العثور على بيانات الحصص المتعددة. يرجى إضافة الحصص أولاً.', 'warning')
            return redirect(url_for('admin.new_session'))
        else:
            print("🔄 Using single session creation")
            return handle_single_session_creation(request.form)
    
    # Get teachers, students, and subscriptions for the form
    teachers = User.query.filter_by(role='teacher', status='approved').all()
    students = User.query.filter_by(role='student', status='approved').all()
    subscriptions = Subscription.query.filter_by(status='active').all()
    
    return render_template('admin/session_form.html', 
                         teachers=teachers,
                         students=students,
                         subscriptions=subscriptions)

@admin_bp.route('/payments')
@login_required
@admin_required
def payments():
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    
    query = Payment.query
    if status_filter:
        query = query.filter_by(status=status_filter)
    
    payments = query.order_by(Payment.payment_date.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/payments.html', 
                         payments=payments,
                         status_filter=status_filter)

@admin_bp.route('/reports')
@login_required
@admin_required
def reports():
    # Revenue stats
    current_month = datetime.now().replace(day=1)

    # Calculate total revenue from completed payments
    total_revenue_from_payments = db.session.query(func.sum(Payment.amount)).filter_by(status='completed').scalar() or 0

    # Fallback: Calculate revenue from active subscriptions
    total_revenue_from_subscriptions = db.session.query(
        func.sum(Package.price)
    ).join(Subscription).filter(
        Subscription.status.in_(['active', 'paid_pending_approval'])
    ).scalar() or 0

    # Use the higher value (payments take priority if they have amounts)
    total_revenue = total_revenue_from_payments if total_revenue_from_payments > 0 else total_revenue_from_subscriptions

    # Calculate monthly revenue from completed payments
    monthly_revenue_from_payments = db.session.query(func.sum(Payment.amount)).filter(
        Payment.status == 'completed',
        Payment.payment_date >= current_month
    ).scalar() or 0

    # Fallback: Calculate monthly revenue from subscriptions activated this month
    monthly_revenue_from_subscriptions = db.session.query(
        func.sum(Package.price)
    ).join(Subscription).filter(
        Subscription.status.in_(['active', 'paid_pending_approval']),
        Subscription.approval_date >= current_month
    ).scalar() or 0

    # Use the higher value (payments take priority if they have amounts)
    monthly_revenue = monthly_revenue_from_payments if monthly_revenue_from_payments > 0 else monthly_revenue_from_subscriptions

    # User stats
    total_users = User.query.count()
    total_teachers = User.query.filter_by(role='teacher').count()
    total_students = User.query.filter_by(role='student').count()
    pending_users = User.query.filter_by(status='pending').count()
    active_users = User.query.filter_by(status='approved').count()
    active_teachers = User.query.filter_by(role='teacher', status='approved').count()
    active_students = User.query.filter_by(role='student', status='approved').count()

    # Session stats
    total_sessions = Session.query.count()
    completed_sessions = Session.query.filter_by(status='completed').count()
    scheduled_sessions = Session.query.filter_by(status='scheduled').count()
    cancelled_sessions = Session.query.filter_by(status='cancelled').count()
    missed_sessions = Session.query.filter_by(status='missed').count()

    # Subscription stats
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    pending_subscriptions = Subscription.query.filter_by(status='pending').count()
    expired_subscriptions = Subscription.query.filter_by(status='expired').count()

    # Performance metrics
    attendance_rate = 0
    completion_rate = 0
    cancellation_rate = 0
    avg_rating = 0
    avg_revenue_per_user = 0
    renewal_rate = 0

    if total_sessions > 0:
        attended_sessions = Session.query.filter_by(student_attended=True).count()
        attendance_rate = (attended_sessions / total_sessions) * 100
        completion_rate = (completed_sessions / total_sessions) * 100
        cancellation_rate = (cancelled_sessions / total_sessions) * 100

    if total_users > 0:
        avg_revenue_per_user = total_revenue / total_users

    if total_subscriptions > 0:
        renewal_rate = (active_subscriptions / total_subscriptions) * 100

    # Monthly data for charts (last 6 months)
    monthly_labels = []
    monthly_revenues = []

    for i in range(5, -1, -1):
        month_start = (datetime.now().replace(day=1) - timedelta(days=32*i)).replace(day=1)
        month_end = (month_start + timedelta(days=32)).replace(day=1)

        # Calculate monthly revenue from completed payments
        month_revenue_from_payments = db.session.query(func.sum(Payment.amount)).filter(
            Payment.status == 'completed',
            Payment.payment_date >= month_start,
            Payment.payment_date < month_end
        ).scalar() or 0

        # Fallback: Calculate monthly revenue from subscriptions activated in this month
        month_revenue_from_subscriptions = db.session.query(
            func.sum(Package.price)
        ).join(Subscription).filter(
            Subscription.status.in_(['active', 'paid_pending_approval']),
            Subscription.approval_date >= month_start,
            Subscription.approval_date < month_end
        ).scalar() or 0

        # Use the higher value (payments take priority if they have amounts)
        month_revenue = month_revenue_from_payments if month_revenue_from_payments > 0 else month_revenue_from_subscriptions

        monthly_labels.append(month_start.strftime('%Y-%m'))
        monthly_revenues.append(float(month_revenue))

    stats = {
        'monthly_revenue': monthly_revenue,
        'total_revenue': total_revenue,
        'total_users': total_users,
        'total_teachers': total_teachers,
        'total_students': total_students,
        'pending_users': pending_users,
        'active_users': active_users,
        'active_teachers': active_teachers,
        'active_students': active_students,
        'total_sessions': total_sessions,
        'completed_sessions': completed_sessions,
        'scheduled_sessions': scheduled_sessions,
        'cancelled_sessions': cancelled_sessions,
        'missed_sessions': missed_sessions,
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'pending_subscriptions': pending_subscriptions,
        'expired_subscriptions': expired_subscriptions,
        'attendance_rate': attendance_rate,
        'completion_rate': completion_rate,
        'cancellation_rate': cancellation_rate,
        'avg_rating': avg_rating,
        'avg_revenue_per_user': avg_revenue_per_user,
        'renewal_rate': renewal_rate
    }

    return render_template('admin/reports.html',
                         stats=stats,
                         monthly_labels=monthly_labels,
                         monthly_revenues=monthly_revenues)

@admin_bp.route('/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def settings():
    if request.method == 'POST':
        try:
            # Update or create settings
            academy_settings = AcademySettings.query.first()
            if not academy_settings:
                academy_settings = AcademySettings()
                db.session.add(academy_settings)

            # Basic Information
            academy_settings.academy_name = request.form.get('academy_name')
            academy_settings.academy_slogan = request.form.get('academy_slogan')
            academy_settings.academy_description = request.form.get('academy_description')
            academy_settings.website_url = request.form.get('website_url')
            academy_settings.language = request.form.get('language')

            # Handle logo upload
            if 'academy_logo' in request.files:
                logo_file = request.files['academy_logo']
                if logo_file and logo_file.filename:
                    # Check file size (2MB limit)
                    if len(logo_file.read()) > 2 * 1024 * 1024:
                        flash('حجم الملف كبير جداً. الحد الأقصى 2MB.', 'danger')
                        return redirect(url_for('admin.settings'))

                    logo_file.seek(0)  # Reset file pointer

                    # Check file extension
                    allowed_extensions = {'png', 'jpg', 'jpeg', 'svg', 'gif'}
                    file_extension = logo_file.filename.rsplit('.', 1)[1].lower()
                    if file_extension not in allowed_extensions:
                        flash('صيغة الملف غير مدعومة. استخدم: PNG, JPG, SVG, GIF', 'danger')
                        return redirect(url_for('admin.settings'))

                    # Generate unique filename
                    import uuid
                    import os
                    filename = f"academy_logo_{uuid.uuid4().hex[:8]}.{file_extension}"

                    # Ensure upload directory exists
                    upload_dir = os.path.join('static', 'images', 'uploads')
                    os.makedirs(upload_dir, exist_ok=True)

                    # Save file
                    file_path = os.path.join(upload_dir, filename)
                    logo_file.save(file_path)

                    # Update database with relative path
                    academy_settings.academy_logo = f"/static/images/uploads/{filename}"

            # Contact Information
            academy_settings.contact_email = request.form.get('contact_email')
            academy_settings.contact_phone = request.form.get('contact_phone')
            academy_settings.contact_whatsapp = request.form.get('contact_whatsapp')
            academy_settings.address = request.form.get('address')
            academy_settings.timezone = request.form.get('timezone')

            # Social Media Links
            academy_settings.facebook_url = request.form.get('facebook_url')
            academy_settings.twitter_url = request.form.get('twitter_url')
            academy_settings.instagram_url = request.form.get('instagram_url')
            academy_settings.youtube_url = request.form.get('youtube_url')

            # Theme Colors
            academy_settings.primary_color = request.form.get('primary_color')
            academy_settings.secondary_color = request.form.get('secondary_color')
            academy_settings.success_color = request.form.get('success_color')
            academy_settings.danger_color = request.form.get('danger_color')
            academy_settings.warning_color = request.form.get('warning_color')
            academy_settings.info_color = request.form.get('info_color')

            # System Settings
            academy_settings.currency = request.form.get('currency')
            academy_settings.date_format = request.form.get('date_format')
            academy_settings.time_format = request.form.get('time_format')

            db.session.commit()

            # Clear any cached settings
            from flask import g
            if hasattr(g, 'academy_settings'):
                delattr(g, 'academy_settings')

            flash('تم تحديث إعدادات الأكاديمية بنجاح! ستظهر التغييرات في جميع أنحاء النظام.', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في تحديث الإعدادات: {str(e)}', 'danger')

        return redirect(url_for('admin.settings'))

    # GET request
    academy_settings = AcademySettings.query.first()
    if not academy_settings:
        academy_settings = AcademySettings()
        db.session.add(academy_settings)
        db.session.commit()

    return render_template('admin/settings.html', settings=academy_settings)

@admin_bp.route('/payment-settings', methods=['GET', 'POST'])
@login_required
@admin_required
def payment_settings():
    if request.method == 'POST':
        try:
            # Update or create Stripe settings
            stripe_gateway = PaymentGateway.query.filter_by(name='stripe').first()
            if not stripe_gateway:
                stripe_gateway = PaymentGateway(name='stripe')
                db.session.add(stripe_gateway)

            stripe_gateway.is_active = bool(request.form.get('stripe_enabled'))
            stripe_gateway.api_key = request.form.get('stripe_publishable_key', '')
            stripe_gateway.secret_key = request.form.get('stripe_secret_key', '')

            # Update or create PayPal settings
            paypal_gateway = PaymentGateway.query.filter_by(name='paypal').first()
            if not paypal_gateway:
                paypal_gateway = PaymentGateway(name='paypal')
                db.session.add(paypal_gateway)

            paypal_gateway.is_active = bool(request.form.get('paypal_enabled'))
            paypal_gateway.api_key = request.form.get('paypal_client_id', '')
            paypal_gateway.secret_key = request.form.get('paypal_client_secret', '')

            # Update academy settings for currency
            academy_settings = AcademySettings.query.first()
            if academy_settings:
                academy_settings.currency = request.form.get('default_currency', 'USD')

            db.session.commit()
            flash('تم تحديث إعدادات الدفع بنجاح!', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في تحديث إعدادات الدفع: {str(e)}', 'danger')

        return redirect(url_for('admin.payment_settings'))

    # GET request
    stripe_gateway = PaymentGateway.query.filter_by(name='stripe').first()
    paypal_gateway = PaymentGateway.query.filter_by(name='paypal').first()
    academy_settings = AcademySettings.query.first()

    return render_template('admin/payment_settings.html',
                         stripe_gateway=stripe_gateway,
                         paypal_gateway=paypal_gateway,
                         academy_settings=academy_settings)

@admin_bp.route('/email-settings', methods=['GET', 'POST'])
@login_required
@admin_required
def email_settings():
    if request.method == 'POST':
        try:
            # Update or create email settings
            email_config = EmailSettings.query.first()
            if not email_config:
                email_config = EmailSettings()
                db.session.add(email_config)

            # SMTP Configuration
            email_config.smtp_server = request.form.get('smtp_server', '')
            email_config.smtp_port = int(request.form.get('smtp_port', 587))
            email_config.smtp_username = request.form.get('smtp_username', '')
            email_config.smtp_password = request.form.get('smtp_password', '')
            email_config.use_tls = bool(request.form.get('use_tls'))
            email_config.default_sender = request.form.get('default_sender', '')

            # Email notifications settings
            email_config.welcome_email_enabled = bool(request.form.get('welcome_email_enabled'))
            email_config.session_reminder_enabled = bool(request.form.get('session_reminder_enabled'))
            email_config.payment_confirmation_enabled = bool(request.form.get('payment_confirmation_enabled'))
            email_config.subscription_approval_enabled = bool(request.form.get('subscription_approval_enabled'))
            email_config.user_management_notifications_enabled = bool(request.form.get('user_management_notifications_enabled'))
            email_config.subscription_purchase_enabled = bool(request.form.get('subscription_purchase_enabled'))
            email_config.subscription_expiry_enabled = bool(request.form.get('subscription_expiry_enabled'))
            email_config.password_reset_enabled = bool(request.form.get('password_reset_enabled'))

            # Session Reminder Settings
            if hasattr(email_config, 'reminder_enabled'):
                email_config.reminder_enabled = bool(request.form.get('reminder_enabled'))
            if hasattr(email_config, 'reminder_hours_before'):
                email_config.reminder_hours_before = int(request.form.get('reminder_hours_before', 24))
            if hasattr(email_config, 'reminder_minutes_before'):
                email_config.reminder_minutes_before = int(request.form.get('reminder_minutes_before', 30))
            if hasattr(email_config, 'reminder_auto_send'):
                email_config.reminder_auto_send = bool(request.form.get('reminder_auto_send'))
            if hasattr(email_config, 'reminder_include_teacher_info'):
                email_config.reminder_include_teacher_info = bool(request.form.get('reminder_include_teacher_info'))
            if hasattr(email_config, 'reminder_include_session_notes'):
                email_config.reminder_include_session_notes = bool(request.form.get('reminder_include_session_notes'))
            if hasattr(email_config, 'preferred_reminder_time'):
                email_config.preferred_reminder_time = int(request.form.get('preferred_reminder_time', 9))

            # User Management Notification Settings
            if hasattr(email_config, 'new_user_registration_notification'):
                email_config.new_user_registration_notification = bool(request.form.get('new_user_registration_notification'))
            if hasattr(email_config, 'user_profile_update_notification'):
                email_config.user_profile_update_notification = bool(request.form.get('user_profile_update_notification'))
            if hasattr(email_config, 'user_status_change_notification'):
                email_config.user_status_change_notification = bool(request.form.get('user_status_change_notification'))
            if hasattr(email_config, 'subscription_status_change_notification'):
                email_config.subscription_status_change_notification = bool(request.form.get('subscription_status_change_notification'))
            if hasattr(email_config, 'payment_received_notification'):
                email_config.payment_received_notification = bool(request.form.get('payment_received_notification'))
            if hasattr(email_config, 'session_completion_notification'):
                email_config.session_completion_notification = bool(request.form.get('session_completion_notification'))
            if hasattr(email_config, 'admin_daily_summary'):
                email_config.admin_daily_summary = bool(request.form.get('admin_daily_summary'))
            if hasattr(email_config, 'admin_weekly_report'):
                email_config.admin_weekly_report = bool(request.form.get('admin_weekly_report'))

            # Admin Notification Settings
            if hasattr(email_config, 'admin_payment_notification_enabled'):
                email_config.admin_payment_notification_enabled = bool(request.form.get('admin_payment_notification_enabled'))
            if hasattr(email_config, 'admin_new_user_notification_enabled'):
                email_config.admin_new_user_notification_enabled = bool(request.form.get('admin_new_user_notification_enabled'))
            if hasattr(email_config, 'admin_session_booking_notification'):
                email_config.admin_session_booking_notification = bool(request.form.get('admin_session_booking_notification'))
            if hasattr(email_config, 'admin_subscription_expiry_notification'):
                email_config.admin_subscription_expiry_notification = bool(request.form.get('admin_subscription_expiry_notification'))

            # Session Notification Settings
            if hasattr(email_config, 'trial_session_notification_enabled'):
                email_config.trial_session_notification_enabled = bool(request.form.get('trial_session_notification_enabled'))
            if hasattr(email_config, 'makeup_session_notification_enabled'):
                email_config.makeup_session_notification_enabled = bool(request.form.get('makeup_session_notification_enabled'))
            if hasattr(email_config, 'session_deletion_notification_enabled'):
                email_config.session_deletion_notification_enabled = bool(request.form.get('session_deletion_notification_enabled'))

            # تم نقل جميع الإعدادات إلى الطريقة الموحدة أعلاه

            db.session.commit()
            flash('تم تحديث جميع إعدادات البريد الإلكتروني بنجاح!', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في تحديث إعدادات البريد: {str(e)}', 'danger')

        return redirect(url_for('admin.email_settings'))

    # GET request
    email_config = EmailSettings.query.first()
    if not email_config:
        email_config = EmailSettings()
        db.session.add(email_config)
        db.session.commit()

    # Get email templates
    templates = EmailTemplate.query.all()

    # Get recent email logs
    recent_logs = EmailLog.query.order_by(EmailLog.created_at.desc()).limit(10).all()

    return render_template('admin/email_settings.html',
                         email_config=email_config,
                         templates=templates,
                         recent_logs=recent_logs)

@admin_bp.route('/test-email', methods=['POST'])
@login_required
@admin_required
def test_email():
    """Test email sending"""
    try:
        from utils.email_service import EmailService

        test_email_address = request.json.get('email')
        if not test_email_address:
            return {'success': False, 'message': 'البريد الإلكتروني مطلوب'}

        # Create email service instance
        email_service = EmailService()

        # Test connection first
        success, message = email_service.test_connection()
        if not success:
            return {'success': False, 'message': f'فشل الاتصال: {message}'}

        # Send test email
        success, message = email_service.send_test_email(test_email_address)
        return {'success': success, 'message': message}

    except Exception as e:
        return {'success': False, 'message': f'خطأ: {str(e)}'}

@admin_bp.route('/test-template-email', methods=['POST'])
@login_required
@admin_required
def test_template_email():
    """Test sending a specific template"""
    try:
        from utils.email_service import EmailService
        from utils.user_management import get_academy_variables

        test_email_address = request.json.get('email')
        template_id = request.json.get('template_id')

        if not test_email_address or not template_id:
            return {'success': False, 'message': 'البريد الإلكتروني ومعرف القالب مطلوبان'}

        template = EmailTemplate.query.get(template_id)
        if not template:
            return {'success': False, 'message': 'القالب غير موجود'}

        # Create EmailService instance
        email_service = EmailService()

        if email_service is None:
            return {'success': False, 'message': 'خطأ في إنشاء خدمة البريد الإلكتروني'}

        # Get academy variables
        academy_vars = get_academy_variables()

        # Sample variables for testing based on template type
        sample_vars = {
            'user_name': 'مستخدم تجريبي',
            'user_email': test_email_address,
            'login_url': url_for('auth.login', _external=True),
            'session_date': '2025-01-15',
            'session_time': '10:00 صباحاً',
            'session_duration': '60 دقيقة',
            'teacher_name': 'الأستاذ محمد',
            'student_name': 'الطالب أحمد',
            'session_type': 'تحفيظ القرآن',
            'session_url': url_for('auth.login', _external=True),
            'current_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'amount': '500',
            'payment_date': datetime.now().strftime('%Y-%m-%d'),
            'package_name': 'باقة تجريبية',
            'test_message': 'هذه رسالة اختبار من النظام',
            # User management specific variables
            'suspension_reason': 'اختبار النظام',
            'suspension_end_date': '2025-08-01 12:00',
            'ban_reason': 'اختبار النظام',
            'disable_reason': 'اختبار النظام',
            'delete_reason': 'اختبار النظام',
            'can_be_restored': True,
            **academy_vars
        }

        # Send template email
        success, message = email_service.send_template_email(test_email_address, template.name, sample_vars)
        return {'success': success, 'message': message}

    except Exception as e:
        return {'success': False, 'message': f'خطأ: {str(e)}'}

@admin_bp.route('/email-templates')
@login_required
@admin_required
def email_templates():
    """Manage email templates"""
    templates = EmailTemplate.query.all()
    return render_template('admin/email_templates.html', templates=templates)

@admin_bp.route('/email-template/<int:template_id>')
@login_required
@admin_required
def view_email_template(template_id):
    """View/Edit email template"""
    template = EmailTemplate.query.get_or_404(template_id)
    return render_template('admin/email_template_edit.html', template=template)

@admin_bp.route('/email-template/<int:template_id>/preview')
@login_required
@admin_required
def preview_email_template(template_id):
    """Preview email template"""
    template = EmailTemplate.query.get_or_404(template_id)

    # Sample variables for preview
    sample_vars = {
        'user_name': 'أحمد محمد',
        'user_email': '<EMAIL>',
        'academy_name': 'أكاديمية القرآن الكريم',
        'login_url': url_for('auth.login', _external=True),
        'session_date': '2024-01-15',
        'session_time': '10:00 صباحاً',
        'teacher_name': 'الأستاذ محمد',
        'session_url': '#',
        'current_date': datetime.now().strftime('%Y-%m-%d')
    }

    try:
        from flask import render_template_string
        rendered_html = render_template_string(template.body_html, **sample_vars)
        rendered_subject = render_template_string(template.subject, **sample_vars)

        return render_template('admin/email_template_preview.html',
                             template=template,
                             rendered_html=rendered_html,
                             rendered_subject=rendered_subject)
    except Exception as e:
        flash(f'خطأ في معاينة القالب: {str(e)}', 'danger')
        return redirect(url_for('admin.email_templates'))

@admin_bp.route('/email-template/<int:template_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_email_template(template_id):
    """Edit email template"""
    template = EmailTemplate.query.get_or_404(template_id)

    if request.method == 'POST':
        try:
            template.display_name = request.form.get('display_name')
            template.subject = request.form.get('subject')
            template.body_html = request.form.get('body_html')
            template.body_text = request.form.get('body_text')
            template.is_active = bool(request.form.get('is_active'))

            # Update variables list
            variables_input = request.form.get('variables', '')
            if variables_input:
                variables_list = [v.strip() for v in variables_input.split(',') if v.strip()]
                template.set_variables_list(variables_list)

            db.session.commit()
            flash('تم تحديث القالب بنجاح!', 'success')
            return redirect(url_for('admin.email_templates'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث القالب: {str(e)}', 'danger')

    return render_template('admin/email_template_edit.html', template=template)

@admin_bp.route('/email-template/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_email_template():
    """Create new email template"""
    if request.method == 'POST':
        try:
            template = EmailTemplate(
                name=request.form.get('name'),
                display_name=request.form.get('display_name'),
                subject=request.form.get('subject'),
                body_html=request.form.get('body_html'),
                body_text=request.form.get('body_text'),
                template_type=request.form.get('template_type'),
                is_active=bool(request.form.get('is_active')),
                is_system=False
            )

            # Set variables list
            variables_input = request.form.get('variables', '')
            if variables_input:
                variables_list = [v.strip() for v in variables_input.split(',') if v.strip()]
                template.set_variables_list(variables_list)

            db.session.add(template)
            db.session.commit()
            flash('تم إنشاء القالب بنجاح!', 'success')
            return redirect(url_for('admin.email_templates'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إنشاء القالب: {str(e)}', 'danger')

    return render_template('admin/email_template_new.html')

@admin_bp.route('/email-template/<int:template_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_email_template(template_id):
    """Delete email template"""
    template = EmailTemplate.query.get_or_404(template_id)

    # Prevent deletion of system templates
    if template.is_system:
        flash('لا يمكن حذف قوالب النظام', 'danger')
        return redirect(url_for('admin.email_templates'))

    try:
        db.session.delete(template)
        db.session.commit()
        flash(f'تم حذف القالب "{template.display_name}" بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف القالب: {str(e)}', 'danger')

    return redirect(url_for('admin.email_templates'))

@admin_bp.route('/update-reminder-settings', methods=['POST'])
@login_required
@admin_required
def update_reminder_settings():
    """Update reminder settings"""
    try:
        email_settings = EmailSettings.query.first()
        if not email_settings:
            email_settings = EmailSettings()
            db.session.add(email_settings)

        # Update reminder settings (using existing session_reminder_enabled field)
        email_settings.session_reminder_enabled = bool(request.form.get('creation_reminders') or
                                                      request.form.get('daily_reminders') or
                                                      request.form.get('immediate_reminders'))

        db.session.commit()
        flash('تم حفظ إعدادات التذكيرات بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حفظ الإعدادات: {str(e)}', 'danger')

    return redirect(url_for('admin.email_settings'))


@admin_bp.route('/update-user-notification-settings', methods=['POST'])
@login_required
@admin_required
def update_user_notification_settings():
    """Update user management notification settings"""
    try:
        email_settings = EmailSettings.query.first()
        if not email_settings:
            email_settings = EmailSettings()
            db.session.add(email_settings)

        # Update user management notification settings
        email_settings.user_management_notifications_enabled = bool(
            request.form.get('suspend_notifications') or
            request.form.get('ban_notifications') or
            request.form.get('disable_notifications') or
            request.form.get('delete_notifications') or
            request.form.get('restore_notifications') or
            request.form.get('activation_notifications')
        )

        db.session.commit()
        flash('تم حفظ إعدادات إشعارات إدارة المستخدمين بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حفظ الإعدادات: {str(e)}', 'danger')

    return redirect(url_for('admin.email_settings'))


@admin_bp.route('/test-user-notification', methods=['POST'])
@login_required
@admin_required
def test_user_notification():
    """Test user management notification email"""
    try:
        data = request.get_json()
        test_email = data.get('email')

        if not test_email:
            return jsonify({'success': False, 'message': 'البريد الإلكتروني مطلوب'})

        from utils.email_service import EmailService
        from utils.user_management import get_academy_variables

        email_service = EmailService()
        academy_vars = get_academy_variables()

        # Test with user_suspended template
        email_vars = {
            'user_name': 'مستخدم تجريبي',
            'suspension_reason': 'هذا إشعار تجريبي لاختبار النظام',
            'suspension_end_date': '2025-08-01 12:00',
            **academy_vars
        }

        success, message = email_service.send_template_email(test_email, 'user_suspended', email_vars)

        if success:
            return jsonify({'success': True, 'message': 'تم إرسال الإشعار التجريبي بنجاح'})
        else:
            return jsonify({'success': False, 'message': f'فشل في إرسال الإشعار: {message}'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

@admin_bp.route('/check-subscriptions', methods=['POST'])
@login_required
@admin_required
def check_subscriptions():
    """Check expiring subscriptions and send notifications"""
    try:
        from utils.subscription_checker import run_subscription_maintenance

        results = run_subscription_maintenance()

        return jsonify({
            'success': True,
            'message': f'تم فحص الاشتراكات بنجاح. تم تحديث {results["expired_updated"]} اشتراك منتهي وإرسال {results["notifications_sent"]} تذكير.',
            'expired_updated': results['expired_updated'],
            'notifications_sent': results['notifications_sent']
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في فحص الاشتراكات: {str(e)}'})


@admin_bp.route('/test-reminder-system', methods=['POST'])
@login_required
@admin_required
def test_reminder_system():
    """Test the reminder system"""
    try:
        from utils.session_reminder_scheduler import run_all_reminder_jobs

        # Test all reminder jobs
        results = run_all_reminder_jobs()

        total_processed = results['daily'] + results['immediate']

        if total_processed > 0:
            message = f"تم معالجة {total_processed} حصة\n"
            message += f"تذكيرات يومية: {results['daily']}\n"
            message += f"تذكيرات فورية: {results['immediate']}"
        else:
            message = "لا توجد حصص تحتاج تذكيرات في الوقت الحالي"

        return {'success': True, 'message': message}

    except Exception as e:
        return {'success': False, 'message': f'خطأ في اختبار النظام: {str(e)}'}





@admin_bp.route('/email-template/<int:template_id>/quick-preview')
@login_required
@admin_required
def quick_preview_email_template(template_id):
    """Quick preview for AJAX requests"""
    template = EmailTemplate.query.get_or_404(template_id)

    # Sample variables for preview
    sample_vars = {
        'user_name': 'أحمد محمد',
        'user_email': '<EMAIL>',
        'academy_name': 'أكاديمية القرآن الكريم',
        'current_date': datetime.now().strftime('%Y-%m-%d')
    }

    try:
        from flask import render_template_string, jsonify
        rendered_subject = render_template_string(template.subject, **sample_vars)

        # Get first 200 characters of HTML content
        rendered_html = render_template_string(template.body_html, **sample_vars)
        preview_text = rendered_html[:200] + "..." if len(rendered_html) > 200 else rendered_html

        return jsonify({
            'success': True,
            'template_name': template.display_name,
            'subject': rendered_subject,
            'preview_text': preview_text,
            'template_type': template.template_type,
            'is_active': template.is_active
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@admin_bp.route('/security-settings', methods=['GET', 'POST'])
@login_required
@admin_required
def security_settings():
    if request.method == 'POST':
        try:
            # Update academy settings with security configurations
            academy_settings = AcademySettings.query.first()
            if not academy_settings:
                academy_settings = AcademySettings()
                db.session.add(academy_settings)

            # Store security settings in JSON format
            import json
            security_config = {
                'min_password_length': int(request.form.get('min_password_length', 8)),
                'require_uppercase': bool(request.form.get('require_uppercase')),
                'require_lowercase': bool(request.form.get('require_lowercase')),
                'require_numbers': bool(request.form.get('require_numbers')),
                'require_special_chars': bool(request.form.get('require_special_chars')),
                'session_timeout': int(request.form.get('session_timeout', 30)),
                'max_login_attempts': int(request.form.get('max_login_attempts', 5)),
                'account_lockout_duration': int(request.form.get('account_lockout_duration', 15)),
                'enable_audit_log': bool(request.form.get('enable_audit_log')),
                'enable_ip_tracking': bool(request.form.get('enable_ip_tracking')),
                'enable_email_notifications': bool(request.form.get('enable_email_notifications')),
                'force_password_change': bool(request.form.get('force_password_change'))
            }

            # Store in academy settings
            academy_settings.security_settings = json.dumps(security_config)

            db.session.commit()
            flash('تم تحديث إعدادات الأمان بنجاح!', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في تحديث إعدادات الأمان: {str(e)}', 'danger')

        return redirect(url_for('admin.security_settings'))

    # GET request - load security settings
    academy_settings = AcademySettings.query.first()
    if academy_settings and hasattr(academy_settings, 'security_settings') and academy_settings.security_settings:
        import json
        try:
            security_config = json.loads(academy_settings.security_settings)
        except:
            # Fallback to defaults if JSON is invalid
            security_config = {
                'min_password_length': 8,
                'require_uppercase': True,
                'require_lowercase': True,
                'require_numbers': True,
                'require_special_chars': False,
                'session_timeout': 30,
                'max_login_attempts': 5,
                'account_lockout_duration': 15,
                'enable_audit_log': True,
                'enable_ip_tracking': True,
                'enable_email_notifications': True,
                'force_password_change': False
            }
    else:
        # Default security settings
        security_config = {
            'min_password_length': 8,
            'require_uppercase': True,
            'require_lowercase': True,
            'require_numbers': True,
            'require_special_chars': False,
            'session_timeout': 30,
            'max_login_attempts': 5,
            'account_lockout_duration': 15,
            'enable_audit_log': True,
            'enable_ip_tracking': True,
            'enable_email_notifications': True,
            'force_password_change': False
        }

    # Get active users count for display
    active_users_count = User.query.filter_by(status='approved').count()

    return render_template('admin/security_settings.html',
                         security_config=security_config,
                         active_users_count=active_users_count)

@admin_bp.route('/export/users')
@login_required
@admin_required
def export_users():
    """Export users data to Excel"""
    flash('ميزة تصدير المستخدمين ستكون متاحة قريباً.', 'info')
    return redirect(url_for('admin.reports'))

@admin_bp.route('/export/sessions')
@login_required
@admin_required
def export_sessions():
    """Export sessions data to CSV"""
    flash('ميزة تصدير الحصص ستكون متاحة قريباً.', 'info')
    return redirect(url_for('admin.reports'))

@admin_bp.route('/export/payments')
@login_required
@admin_required
def export_payments():
    """Export payments data to PDF"""
    flash('ميزة تصدير المدفوعات ستكون متاحة قريباً.', 'info')
    return redirect(url_for('admin.reports'))

@admin_bp.route('/custom-report')
@login_required
@admin_required
def custom_report():
    """Generate custom report"""
    report_type = request.args.get('report_type', 'revenue')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    flash(f'تقرير {report_type} المخصص من {start_date} إلى {end_date} سيكون متاحاً قريباً.', 'info')
    return redirect(url_for('admin.reports'))

@admin_bp.route('/session_settings', methods=['GET', 'POST'])
@login_required
@admin_required
def session_settings():
    """Session settings management"""
    try:
        settings = SessionSettings.get_settings()
    except Exception as e:
        print(f"❌ Error getting settings: {e}")
        import traceback
        traceback.print_exc()
        flash(f'خطأ في تحميل الإعدادات: {str(e)}', 'danger')
        return redirect(url_for('admin.dashboard'))

    if request.method == 'POST':
        try:
            # General Settings
            settings.default_provider = request.form.get('default_provider', 'google_meet')
            settings.auto_generate_links = 'auto_generate_links' in request.form

            # Google Meet Settings (via Google Calendar)
            settings.use_google_calendar = 'use_google_calendar' in request.form

            # Jitsi Meet Settings
            settings.jitsi_enabled = 'jitsi_enabled' in request.form
            settings.jitsi_domain = request.form.get('jitsi_domain', 'meet.jit.si')
            settings.jitsi_prefix = request.form.get('jitsi_prefix', 'academy')
            settings.jitsi_password_required = 'jitsi_password_required' in request.form

            # Link Generation Settings
            settings.include_session_id = 'include_session_id' in request.form
            settings.include_date = 'include_date' in request.form
            settings.include_time = 'include_time' in request.form

            # Security Settings
            settings.require_password = 'require_password' in request.form
            settings.waiting_room = 'waiting_room' in request.form

            db.session.commit()
            flash('تم حفظ إعدادات الحصص بنجاح!', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}', 'danger')

        return redirect(url_for('admin.session_settings'))

    return render_template('admin/session_settings.html', settings=settings)


@admin_bp.route('/calendar-settings', methods=['GET', 'POST'])
@login_required
@admin_required
def calendar_settings():
    """Google Calendar settings management"""
    from models import CalendarSettings
    from utils.calendar_service import calendar_service
    from flask_wtf import FlaskForm
    from wtforms import BooleanField, StringField, TextAreaField, SubmitField
    from wtforms.validators import Optional

    # Create a simple form class for CSRF protection
    class CalendarSettingsForm(FlaskForm):
        calendar_enabled = BooleanField('تفعيل ربط Google Calendar')
        calendar_id = StringField('Calendar ID', validators=[Optional()])
        service_account_email = StringField('Service Account Email', validators=[Optional()])
        auto_create_events = BooleanField('إنشاء تلقائي للأحداث')
        auto_update_events = BooleanField('تحديث تلقائي للأحداث')
        auto_delete_events = BooleanField('حذف تلقائي للأحداث')
        event_title_template = StringField('قالب عنوان الحدث', validators=[Optional()])
        event_description_template = TextAreaField('قالب وصف الحدث', validators=[Optional()])
        submit = SubmitField('حفظ الإعدادات')

    settings = CalendarSettings.get_settings()
    form = CalendarSettingsForm()

    if form.validate_on_submit():
        try:
            # Update calendar settings
            settings.calendar_enabled = form.calendar_enabled.data
            settings.calendar_id = form.calendar_id.data.strip() if form.calendar_id.data else ''
            settings.service_account_email = form.service_account_email.data.strip() if form.service_account_email.data else ''

            # Event settings
            settings.auto_create_events = form.auto_create_events.data
            settings.auto_update_events = form.auto_update_events.data
            settings.auto_delete_events = form.auto_delete_events.data

            # Event templates
            settings.event_title_template = form.event_title_template.data or 'حصة قرآن - {student_name} مع {teacher_name}'
            settings.event_description_template = form.event_description_template.data or 'حصة تحفيظ القرآن الكريم\nالطالب: {student_name}\nالمعلم: {teacher_name}\nنوع الحصة: {session_type}'

            db.session.commit()
            flash('تم حفظ إعدادات Google Calendar بنجاح!', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}', 'danger')

        return redirect(url_for('admin.calendar_settings'))

    # Pre-populate form with current settings
    if request.method == 'GET':
        form.calendar_enabled.data = settings.calendar_enabled
        form.calendar_id.data = settings.calendar_id
        form.service_account_email.data = settings.service_account_email
        form.auto_create_events.data = settings.auto_create_events
        form.auto_update_events.data = settings.auto_update_events
        form.auto_delete_events.data = settings.auto_delete_events
        form.event_title_template.data = settings.event_title_template
        form.event_description_template.data = settings.event_description_template

    return render_template('admin/calendar_settings.html', settings=settings, form=form)


@admin_bp.route('/test-calendar-connection', methods=['POST'])
@login_required
@admin_required
def test_calendar_connection():
    """Test Google Calendar connection"""
    from models import CalendarSettings
    from utils.calendar_service import calendar_service

    try:
        settings = CalendarSettings.get_settings()

        if not settings.calendar_id:
            return jsonify({
                'success': False,
                'message': 'يرجى إدخال Calendar ID أولاً'
            })

        # Set calendar ID and test connection
        calendar_service.set_calendar_id(settings.calendar_id)
        result = calendar_service.test_connection()

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في اختبار الاتصال: {str(e)}'
        })

@admin_bp.route('/sessions/<int:session_id>/meeting-setup')
@login_required
@admin_required
def session_meeting_setup(session_id):
    """Session meeting setup page"""
    session = Session.query.get_or_404(session_id)
    return render_template('admin/session_meeting_setup.html', session=session)

@admin_bp.route('/sessions/<int:session_id>/update-link', methods=['POST'])
@login_required
@admin_required
def update_session_link(session_id):
    """Update session meeting link"""
    session = Session.query.get_or_404(session_id)

    meeting_link = request.form.get('meeting_link', '').strip()

    if not meeting_link:
        flash('يرجى إدخال رابط الحصة.', 'danger')
        return redirect(url_for('admin.session_meeting_setup', session_id=session_id))

    # Validate the link format
    if session.meeting_provider == 'google_meet' and 'meet.google.com' not in meeting_link:
        flash('يرجى إدخال رابط Google Meet صحيح.', 'danger')
        return redirect(url_for('admin.session_meeting_setup', session_id=session_id))

    # Update the session
    session.meeting_link = meeting_link
    session.meeting_id = meeting_link.split('/')[-1]

    db.session.commit()

    # Update Google Calendar event if link changed
    session.update_calendar_event()

    flash('تم تحديث رابط الحصة بنجاح.', 'success')
    return redirect(url_for('admin.session_meeting_setup', session_id=session_id))

@admin_bp.route('/sessions/<int:session_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_session(session_id):
    """Edit session details"""
    session = Session.query.get_or_404(session_id)

    if request.method == 'POST':
        try:
            # Store old values for comparison
            old_datetime = session.scheduled_datetime
            old_teacher_id = session.teacher_id
            old_student_id = session.student_id
            old_duration = session.duration_minutes

            # Update session details
            scheduled_datetime = datetime.strptime(
                f"{request.form['scheduled_date']} {request.form['scheduled_time']}",
                '%Y-%m-%d %H:%M'
            )

            session.teacher_id = int(request.form['teacher_id'])
            session.student_id = int(request.form['student_id'])
            session.subscription_id = int(request.form['subscription_id']) if request.form.get('subscription_id') else None
            session.session_type = request.form['session_type']
            session.scheduled_datetime = scheduled_datetime
            session.duration_minutes = int(request.form['duration_minutes'])
            session.notes = request.form.get('notes', '')
            session.meeting_provider = request.form.get('meeting_provider', session.meeting_provider)

            # Check if significant changes were made that require calendar update
            significant_changes = (
                old_datetime != scheduled_datetime or
                old_teacher_id != session.teacher_id or
                old_student_id != session.student_id or
                old_duration != session.duration_minutes
            )

            db.session.commit()

            # Update Google Calendar event if significant changes were made
            if significant_changes:
                session.update_calendar_event()
                db.session.commit()

            flash('تم تحديث الحصة بنجاح!', 'success')
            return redirect(url_for('admin.sessions'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الحصة: {str(e)}', 'danger')

    # Get data for form
    teachers = User.query.filter_by(role='teacher', status='approved').all()
    students = User.query.filter_by(role='student', status='approved').all()
    subscriptions = Subscription.query.filter_by(status='active').all()

    return render_template('admin/session_edit.html',
                         session=session,
                         teachers=teachers,
                         students=students,
                         subscriptions=subscriptions)

@admin_bp.route('/sessions/<int:session_id>')
@login_required
@admin_required
def session_details(session_id):
    """Session details page for admin"""
    session = Session.query.get_or_404(session_id)
    return render_template('admin/session_meeting_setup.html', session=session)


@admin_bp.route('/sessions/<int:session_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_session(session_id):
    """Delete a session completely from the system"""
    try:
        session = Session.query.get_or_404(session_id)

        # Store session info for logging and notifications
        session_info = {
            'id': session.id,
            'teacher_name': session.teacher.full_name,
            'student_name': session.student.full_name,
            'scheduled_datetime': session.scheduled_datetime.strftime('%Y-%m-%d %H:%M'),
            'session_type': session.session_type,
            'status': session.status,
            'teacher_id': session.teacher_id,
            'student_id': session.student_id,
            'subscription_id': session.subscription_id
        }

        # Delete related data first (due to foreign key constraints)

        # 1. Delete session ratings
        SessionRating.query.filter_by(session_id=session_id).delete()
        print(f"🗑️ تم حذف تقييمات الحصة {session_id}")

        # 2. Delete session reminders
        from models import SessionReminder
        SessionReminder.query.filter_by(session_id=session_id).delete()
        print(f"🗑️ تم حذف تذكيرات الحصة {session_id}")

        # 3. Delete from Google Calendar if exists
        if session.calendar_event_id:
            try:
                from utils.calendar_service import delete_calendar_event
                delete_calendar_event(session.calendar_event_id)
                print(f"📅 تم حذف الحدث من Google Calendar: {session.calendar_event_id}")
            except Exception as e:
                print(f"⚠️ تعذر حذف الحدث من Google Calendar: {str(e)}")

        # 4. Update subscription sessions count if applicable
        if session.subscription_id:
            subscription = session.subscription
            if subscription:
                # Recalculate sessions count after deletion
                remaining_sessions = Session.query.filter_by(subscription_id=subscription.id).count() - 1
                print(f"📊 الحصص المتبقية في الاشتراك: {remaining_sessions}")

        # 5. Log the deletion action
        try:
            log_entry = UserActionLog(
                user_id=session.teacher_id,
                admin_id=current_user.id,
                action='session_deleted',
                old_status=f"Session {session_id}: {session_info['teacher_name']} -> {session_info['student_name']}",
                new_status='deleted',
                reason=f"تم حذف الحصة من قبل الإدمن {current_user.full_name}"
            )
            db.session.add(log_entry)
        except Exception as e:
            print(f"⚠️ تعذر تسجيل عملية الحذف: {str(e)}")

        # 6. Create notifications for teacher and student
        try:
            # Notification for teacher
            teacher_notification = Notification(
                user_id=session.teacher_id,
                title='تم حذف حصة',
                message=f'تم حذف الحصة المجدولة في {session_info["scheduled_datetime"]} مع الطالب {session_info["student_name"]}',
                type='session_deleted',
                is_read=False
            )
            db.session.add(teacher_notification)

            # Notification for student
            student_notification = Notification(
                user_id=session.student_id,
                title='تم حذف حصة',
                message=f'تم حذف الحصة المجدولة في {session_info["scheduled_datetime"]} مع المعلم {session_info["teacher_name"]}',
                type='session_deleted',
                is_read=False
            )
            db.session.add(student_notification)

            print(f"🔔 تم إنشاء إشعارات الحذف للمعلم والطالب")
        except Exception as e:
            print(f"⚠️ تعذر إنشاء الإشعارات: {str(e)}")

        # 7. Finally, delete the session itself
        db.session.delete(session)
        db.session.commit()

        print(f"✅ تم حذف الحصة {session_id} بنجاح")

        # Send email notifications (if enabled)
        try:
            # Check if session deletion notifications are enabled
            email_settings = EmailSettings.query.first()
            if email_settings and getattr(email_settings, 'session_deletion_notification_enabled', True):
                from utils.email_service import EmailService
                email_service = EmailService()

                # Email to teacher
                teacher_subject = f"تم حذف حصة - {session_info['scheduled_datetime']}"
                teacher_message = f"""
                عزيزي {session_info['teacher_name']},

                تم حذف الحصة المجدولة معك في {session_info['scheduled_datetime']} مع الطالب {session_info['student_name']}.

                إذا كان لديك أي استفسار، يرجى التواصل مع الإدارة.

                مع تحيات إدارة الأكاديمية
                """

                email_service.send_email(
                    to_email=session.teacher.email,
                    subject=teacher_subject,
                    body_html=f"<p>{teacher_message.replace(chr(10), '<br>')}</p>",
                    body_text=teacher_message
                )

                # Email to student
                student_subject = f"تم حذف حصة - {session_info['scheduled_datetime']}"
                student_message = f"""
                عزيزي {session_info['student_name']},

                تم حذف الحصة المجدولة لك في {session_info['scheduled_datetime']} مع المعلم {session_info['teacher_name']}.

                إذا كان لديك أي استفسار، يرجى التواصل مع الإدارة.

                مع تحيات إدارة الأكاديمية
                """

                email_service.send_email(
                    to_email=session.student.email,
                    subject=student_subject,
                    body_html=f"<p>{student_message.replace(chr(10), '<br>')}</p>",
                    body_text=student_message
                )

                print(f"📧 تم إرسال إشعارات البريد الإلكتروني")
            else:
                print(f"ℹ️ إشعارات حذف الحصص معطلة - لم يتم إرسال بريد إلكتروني")

        except Exception as e:
            print(f"⚠️ تعذر إرسال إشعارات البريد الإلكتروني: {str(e)}")

        # Return success response
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': f'تم حذف الحصة بنجاح. الحصة كانت مجدولة في {session_info["scheduled_datetime"]} بين {session_info["teacher_name"]} و {session_info["student_name"]}'
            })
        else:
            flash(f'تم حذف الحصة بنجاح. الحصة كانت مجدولة في {session_info["scheduled_datetime"]} بين {session_info["teacher_name"]} و {session_info["student_name"]}', 'success')
            return redirect(url_for('admin.sessions'))

    except Exception as e:
        db.session.rollback()
        error_message = f"خطأ في حذف الحصة: {str(e)}"
        print(f"❌ {error_message}")

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': error_message}), 500
        else:
            flash(error_message, 'danger')
            return redirect(url_for('admin.sessions'))

@admin_bp.route('/profile')
@login_required
@admin_required
def profile():
    """Admin profile page"""
    # Get admin statistics
    total_users = User.query.count()
    total_teachers = User.query.filter_by(role='teacher').count()
    total_students = User.query.filter_by(role='student').count()
    pending_users = User.query.filter_by(status='pending').count()

    total_sessions = Session.query.count()
    completed_sessions = Session.query.filter_by(status='completed').count()

    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()

    # Calculate total revenue
    total_revenue = Payment.get_total_revenue() if hasattr(Payment, 'get_total_revenue') else 0

    # Get recent activities
    recent_users = User.query.filter_by(status='pending').order_by(User.created_at.desc()).limit(5).all()
    recent_sessions = Session.query.order_by(Session.created_at.desc()).limit(5).all()

    # Calculate system health metrics
    system_health = {
        'user_approval_rate': (total_users - pending_users) / total_users * 100 if total_users > 0 else 0,
        'session_completion_rate': completed_sessions / total_sessions * 100 if total_sessions > 0 else 0,
        'subscription_activation_rate': active_subscriptions / total_subscriptions * 100 if total_subscriptions > 0 else 0
    }

    return render_template('admin/profile.html',
                         total_users=total_users,
                         total_teachers=total_teachers,
                         total_students=total_students,
                         pending_users=pending_users,
                         total_sessions=total_sessions,
                         completed_sessions=completed_sessions,
                         total_subscriptions=total_subscriptions,
                         active_subscriptions=active_subscriptions,
                         total_revenue=total_revenue,
                         recent_users=recent_users,
                         recent_sessions=recent_sessions,
                         system_health=system_health)

@admin_bp.route('/profile/update', methods=['POST'])
@login_required
@admin_required
def update_profile():
    """Update admin profile"""
    try:
        # Update basic info
        current_user.first_name = request.form.get('first_name', '').strip()
        current_user.last_name = request.form.get('last_name', '').strip()
        current_user.phone = request.form.get('phone', '').strip()

        # Validate required fields
        if not current_user.first_name or not current_user.last_name:
            flash('الاسم الأول والأخير مطلوبان.', 'error')
            return redirect(url_for('admin.profile'))

        # Update password if provided
        new_password = request.form.get('new_password')
        if new_password:
            current_password = request.form.get('current_password')
            if not current_user.check_password(current_password):
                flash('كلمة المرور الحالية غير صحيحة.', 'error')
                return redirect(url_for('admin.profile'))

            # Validate new password
            if len(new_password) < 6:
                flash('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل.', 'error')
                return redirect(url_for('admin.profile'))

            current_user.set_password(new_password)
            flash('تم تحديث كلمة المرور بنجاح.', 'success')

        db.session.commit()
        flash('تم تحديث الملف الشخصي بنجاح.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الملف الشخصي.', 'error')

    return redirect(url_for('admin.profile'))

@admin_bp.route('/admin-settings')
@login_required
@admin_required
def admin_settings():
    """Admin personal settings page"""
    # Get or create notification settings
    notification_settings = UserNotificationSettings.get_or_create_for_user(current_user.id)
    return render_template('admin/admin_settings.html', notification_settings=notification_settings)

@admin_bp.route('/admin-settings/notifications', methods=['POST'])
@login_required
@admin_required
def update_admin_notification_settings():
    """Update admin notification settings"""
    try:
        # Get or create notification settings
        notification_settings = UserNotificationSettings.get_or_create_for_user(current_user.id)

        # Update settings from form
        notification_settings.email_notifications = request.form.get('email_notifications') == 'on'
        notification_settings.system_alerts = request.form.get('system_alerts') == 'on'
        notification_settings.user_activity_alerts = request.form.get('user_activity_alerts') == 'on'
        notification_settings.payment_notifications = request.form.get('payment_alerts') == 'on'  # Using payment_notifications field

        db.session.commit()
        flash('تم تحديث إعدادات الإشعارات بنجاح.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الإعدادات.', 'error')

    return redirect(url_for('admin.admin_settings'))
