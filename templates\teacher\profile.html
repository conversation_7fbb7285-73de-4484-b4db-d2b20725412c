{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ super() }}{% endblock %}

{% block page_title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="row">
    <!-- Profile Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="position-relative d-inline-block mb-3">
                    {% if current_user.profile_image %}
                        <img src="{{ current_user.get_profile_image_url() }}" alt="{{ current_user.full_name }}"
                             class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid #28a745;">
                    {% else %}
                        <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center bg-success text-white"
                             style="width: 100px; height: 100px; font-size: 2.5rem;">
                            <i class="{{ current_user.get_role_icon() }}"></i>
                        </div>
                    {% endif %}
                    <button type="button" class="btn btn-sm btn-primary position-absolute bottom-0 end-0 rounded-circle"
                            style="width: 30px; height: 30px; padding: 0;" data-bs-toggle="modal" data-bs-target="#profileImageModal">
                        <i class="fas fa-camera" style="font-size: 0.8rem;"></i>
                    </button>
                </div>
                <h5 class="card-title">{{ current_user.full_name }}</h5>
                <p class="text-muted">{{ current_user.email }}</p>
                <p class="text-muted">
                    <i class="{{ current_user.get_role_icon() }} me-2"></i>{{ current_user.get_role_display() }}
                </p>
                {% if current_user.phone %}
                <p class="text-muted">
                    <i class="fas fa-phone me-2"></i>{{ current_user.phone }}
                </p>
                {% endif %}
                <p class="text-muted">
                    <i class="fas fa-calendar me-2"></i>انضم في {{ current_user.created_at.strftime('%Y/%m/%d') }}
                </p>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائياتي</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ total_sessions }}</h4>
                            <small class="text-muted">إجمالي الحصص</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ completed_sessions }}</h4>
                        <small class="text-muted">حصص مكتملة</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info">{{ unique_students }}</h4>
                            <small class="text-muted">طلاب مختلفين</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ avg_rating }}</h4>
                        <small class="text-muted">متوسط التقييم</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <h5 class="text-info">{{ "%.1f"|format(completion_rate) }}%</h5>
                    <small class="text-muted">معدل الإكمال</small>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('teacher.sessions') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-calendar me-2"></i>عرض حصصي
                    </a>
                    <a href="{{ url_for('teacher.students') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-users me-2"></i>عرض طلابي
                    </a>
                    <a href="{{ url_for('teacher.reports') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-line me-2"></i>التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-edit me-2"></i>تحديث الملف الشخصي</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('teacher.update_profile') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ current_user.first_name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ current_user.last_name }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" value="{{ current_user.email }}" disabled>
                        <div class="form-text">لا يمكن تغيير البريد الإلكتروني</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="{{ current_user.phone or '' }}">
                    </div>

                    <div class="mb-3">
                        <label for="bio" class="form-label">نبذة تعريفية</label>
                        <textarea class="form-control" id="bio" name="bio" rows="3" 
                                  placeholder="اكتب نبذة مختصرة عن خبرتك في تعليم القرآن الكريم..."></textarea>
                        <div class="form-text">ميزة قادمة قريباً</div>
                    </div>

                    <hr>
                    <h6><i class="fas fa-lock me-2"></i>تغيير كلمة المرور</h6>
                    <p class="text-muted small">اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور</p>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <div class="form-text">6 أحرف على الأقل</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Recent Sessions -->
        {% if recent_sessions %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-history me-2"></i>الحصص الأخيرة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الطالب</th>
                                <th>الحالة</th>
                                <th>النوع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in recent_sessions %}
                            <tr>
                                <td>{{ session.scheduled_datetime.strftime('%Y/%m/%d %H:%M') }}</td>
                                <td>{{ session.student.full_name }}</td>
                                <td>
                                    {% if session.status == 'completed' %}
                                        <span class="badge bg-success">مكتملة</span>
                                    {% elif session.status == 'scheduled' %}
                                        <span class="badge bg-primary">مجدولة</span>
                                    {% elif session.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغية</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ session.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if session.session_type == 'trial' %}
                                        <span class="badge bg-info">تجريبية</span>
                                    {% elif session.session_type == 'makeup' %}
                                        <span class="badge bg-warning">تعويضية</span>
                                    {% else %}
                                        <span class="badge bg-primary">عادية</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('teacher.sessions') }}" class="btn btn-outline-primary btn-sm">
                        عرض جميع الحصص
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password validation
    const currentPassword = document.getElementById('current_password');
    const newPassword = document.getElementById('new_password');
    
    newPassword.addEventListener('input', function() {
        if (this.value && !currentPassword.value) {
            currentPassword.required = true;
            currentPassword.classList.add('is-invalid');
        } else {
            currentPassword.required = false;
            currentPassword.classList.remove('is-invalid');
        }
    });
    
    currentPassword.addEventListener('input', function() {
        if (this.value) {
            newPassword.required = true;
        } else {
            newPassword.required = false;
        }
    });
});
</script>

<!-- Profile Image Upload Modal -->
<div class="modal fade" id="profileImageModal" tabindex="-1" aria-labelledby="profileImageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileImageModalLabel">
                    <i class="fas fa-camera me-2"></i>تحديث صورة الملف الشخصي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('teacher.update_profile_image') }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                    <div class="text-center mb-3">
                        <div id="imagePreview" class="mb-3">
                            {% if current_user.profile_image %}
                                <img src="{{ current_user.get_profile_image_url() }}" alt="Preview"
                                     class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">
                            {% else %}
                                <div class="rounded-circle mx-auto d-flex align-items-center justify-content-center bg-secondary text-white"
                                     style="width: 120px; height: 120px; font-size: 3rem;">
                                    <i class="{{ current_user.get_role_icon() }}"></i>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="profile_image" class="form-label">اختر صورة جديدة</label>
                        <input type="file" class="form-control" id="profile_image" name="profile_image"
                               accept="image/*" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            الحد الأقصى: 5MB. الصيغ المدعومة: JPG, PNG, GIF
                        </div>
                    </div>

                    {% if current_user.profile_image %}
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image">
                        <label class="form-check-label text-danger" for="remove_image">
                            <i class="fas fa-trash me-1"></i>حذف الصورة الحالية
                        </label>
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Image preview functionality
document.getElementById('profile_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">`;
        };
        reader.readAsDataURL(file);
    }
});

// Remove image checkbox functionality
const removeImageCheckbox = document.getElementById('remove_image');
const fileInput = document.getElementById('profile_image');

if (removeImageCheckbox) {
    removeImageCheckbox.addEventListener('change', function() {
        if (this.checked) {
            fileInput.required = false;
            fileInput.disabled = true;
        } else {
            fileInput.required = true;
            fileInput.disabled = false;
        }
    });
}
</script>
{% endblock %}
