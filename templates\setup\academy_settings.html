{% extends "setup/base.html" %}

{% block title %}إعدادات الأكاديمية - {{ super() }}{% endblock %}

{% block header_title %}إعدادات الأكاديمية{% endblock %}
{% block header_subtitle %}قم بتخصيص معلومات وإعدادات أكاديميتك{% endblock %}

{% block progress %}
<div class="setup-progress">
    <div class="progress">
        <div class="progress-bar" role="progressbar" style="width: 80%" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
    <div class="step-indicator">
        <div class="step completed">التعريف</div>
        <div class="step completed">حساب المدير</div>
        <div class="step active">إعدادات الأكاديمية</div>
        <div class="step">بوابات الدفع</div>
        <div class="step">الإكمال</div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="text-center mb-4">
    <div class="feature-icon">
        <i class="fas fa-university"></i>
    </div>
    <h3 class="mb-3">إعدادات الأكاديمية</h3>
    <p class="text-muted">
        قم بتخصيص معلومات أكاديميتك وإعداداتها الأساسية
    </p>
</div>

<form method="POST" enctype="multipart/form-data" id="academyForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    
    <!-- معلومات أساسية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="academy_name" class="form-label">
                        <i class="fas fa-graduation-cap me-2"></i>اسم الأكاديمية *
                    </label>
                    <input type="text" class="form-control" id="academy_name" name="academy_name" 
                           value="{{ settings.academy_name if settings else '' }}" required>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="academy_slogan" class="form-label">
                        <i class="fas fa-quote-right me-2"></i>الشعار/السلوجان
                    </label>
                    <input type="text" class="form-control" id="academy_slogan" name="academy_slogan" 
                           value="{{ settings.academy_slogan if settings else '' }}">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="academy_description" class="form-label">
                    <i class="fas fa-align-left me-2"></i>وصف الأكاديمية
                </label>
                <textarea class="form-control" id="academy_description" name="academy_description" rows="3">{{ settings.academy_description if settings else '' }}</textarea>
            </div>
            
            <div class="mb-3">
                <label for="academy_logo" class="form-label">
                    <i class="fas fa-image me-2"></i>شعار الأكاديمية
                </label>
                <input type="file" class="form-control" id="academy_logo" name="academy_logo" accept="image/*">
                <div class="form-text">الصيغ المدعومة: PNG, JPG, JPEG, GIF, SVG</div>
            </div>
        </div>
    </div>
    
    <!-- معلومات التواصل -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-phone me-2"></i>معلومات التواصل</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="contact_email" class="form-label">
                        <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                    </label>
                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                           value="{{ settings.contact_email if settings else '' }}">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="contact_phone" class="form-label">
                        <i class="fas fa-phone me-2"></i>رقم الهاتف
                    </label>
                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                           value="{{ settings.contact_phone if settings else '' }}">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="whatsapp_number" class="form-label">
                        <i class="fab fa-whatsapp me-2"></i>رقم الواتساب
                    </label>
                    <input type="tel" class="form-control" id="whatsapp_number" name="whatsapp_number"
                           value="{{ settings.contact_whatsapp if settings else '' }}">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="contact_address" class="form-label">
                    <i class="fas fa-map-marker-alt me-2"></i>العنوان
                </label>
                <textarea class="form-control" id="contact_address" name="contact_address" rows="2">{{ settings.address if settings else '' }}</textarea>
            </div>
        </div>
    </div>
    
    <!-- وسائل التواصل الاجتماعي -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>وسائل التواصل الاجتماعي</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="facebook_url" class="form-label">
                        <i class="fab fa-facebook me-2"></i>صفحة فيسبوك
                    </label>
                    <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                           value="{{ settings.facebook_url if settings else '' }}" placeholder="https://facebook.com/yourpage">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="twitter_url" class="form-label">
                        <i class="fab fa-twitter me-2"></i>حساب تويتر
                    </label>
                    <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                           value="{{ settings.twitter_url if settings else '' }}" placeholder="https://twitter.com/youraccount">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="instagram_url" class="form-label">
                        <i class="fab fa-instagram me-2"></i>حساب إنستغرام
                    </label>
                    <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                           value="{{ settings.instagram_url if settings else '' }}" placeholder="https://instagram.com/youraccount">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="youtube_url" class="form-label">
                        <i class="fab fa-youtube me-2"></i>قناة يوتيوب
                    </label>
                    <input type="url" class="form-control" id="youtube_url" name="youtube_url" 
                           value="{{ settings.youtube_url if settings else '' }}" placeholder="https://youtube.com/yourchannel">
                </div>
            </div>
        </div>
    </div>
    
    <!-- الألوان والعلامة التجارية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-palette me-2"></i>الألوان والعلامة التجارية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="primary_color" class="form-label">
                        <i class="fas fa-circle me-2" style="color: #007bff;"></i>اللون الأساسي
                    </label>
                    <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" 
                           value="{{ settings.primary_color if settings else '#007bff' }}">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="secondary_color" class="form-label">
                        <i class="fas fa-circle me-2" style="color: #6c757d;"></i>اللون الثانوي
                    </label>
                    <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color" 
                           value="{{ settings.secondary_color if settings else '#6c757d' }}">
                </div>
                

            </div>
        </div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        يمكنك تخطي هذه الخطوة والعودة لتخصيص هذه الإعدادات لاحقاً من لوحة التحكم.
    </div>
    
    <div class="d-flex justify-content-between align-items-center mt-4">
        <a href="{{ url_for('setup.admin_account') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>السابق
        </a>
        
        <div class="text-center">
            <button type="submit" name="skip_remaining" value="1" class="btn btn-outline-secondary me-2">
                <i class="fas fa-forward me-2"></i>تخطي الباقي
            </button>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-arrow-left me-2"></i>التالي
            </button>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('academyForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
        // Show loading state
        if (e.submitter === submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        }
    });
    
    // Color preview
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(input => {
        input.addEventListener('change', function() {
            const icon = this.parentElement.querySelector('.fas.fa-circle');
            if (icon) {
                icon.style.color = this.value;
            }
        });
    });
});
</script>
{% endblock %}
