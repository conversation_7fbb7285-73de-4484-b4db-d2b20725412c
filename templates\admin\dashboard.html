{% extends "base.html" %}

{% block title %}لوحة تحكم الإدارة - {{ academy_name }}{% endblock %}
{% block page_title %}لوحة تحكم الإدارة{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4 class="card-title mb-1">مرحباً، {{ current_user.full_name }}</h4>
                        <p class="card-text mb-0">إليك ملخص إدارة الأكاديمية اليوم</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-primary text-uppercase mb-1">إجمالي المستخدمين</div>
                        <div class="h5 mb-0">{{ stats.total_users }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">الاشتراكات النشطة</div>
                        <div class="h5 mb-0">{{ stats.active_subscriptions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-credit-card fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">الحصص المكتملة</div>
                        <div class="h5 mb-0">{{ stats.completed_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">الحصص المجدولة</div>
                        <div class="h5 mb-0">{{ stats.scheduled_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-plus fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session Type Statistics -->
<div class="row mb-4">
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-purple border-4" style="border-color: #6f42c1 !important;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-uppercase mb-1" style="color: #6f42c1;">الحصص التجريبية</div>
                        <div class="h4 mb-0">{{ stats.trial_sessions }}</div>
                        <div class="small text-muted">إجمالي الحصص التجريبية</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-flask fa-2x" style="color: #6f42c1;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-dark border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-dark text-uppercase mb-1">الحصص التعويضية</div>
                        <div class="h4 mb-0">{{ stats.makeup_sessions }}</div>
                        <div class="small text-muted">إجمالي الحصص التعويضية</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-redo fa-2x text-dark"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Cards -->
<div class="row mb-4">
    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">إجمالي الإيرادات</div>
                        <div class="h4 mb-0">
                            {% if stats.total_revenue > 0 %}
                                {{ format_currency(stats.total_revenue) }}
                            {% else %}
                                <span class="text-muted">{{ format_currency(0) }}</span>
                            {% endif %}
                        </div>
                        <div class="small text-muted">
                            من جميع المدفوعات والاشتراكات
                            {% if stats.total_revenue > 0 %}
                                <i class="fas fa-chart-line text-success ms-1"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-md-6 mb-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">إيرادات هذا الشهر</div>
                        <div class="h4 mb-0">
                            {% if stats.monthly_revenue > 0 %}
                                {{ format_currency(stats.monthly_revenue) }}
                            {% else %}
                                <span class="text-muted">{{ format_currency(0) }}</span>
                            {% endif %}
                        </div>
                        <div class="small text-muted">
                            الشهر الحالي
                            {% if stats.monthly_revenue > 0 %}
                                <i class="fas fa-calendar-check text-warning ms-1"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-dollar fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats Row -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <div class="h4 text-primary">{{ stats.total_students }}</div>
                            <div class="small text-muted">الطلاب</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <div class="h4 text-success">{{ stats.total_teachers }}</div>
                            <div class="small text-muted">المعلمون</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <div class="h4 text-info">{{ stats.scheduled_sessions }}</div>
                            <div class="small text-muted">حصص مجدولة</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="h4 text-warning">{{ stats.pending_users }}</div>
                        <div class="small text-muted">حسابات معلقة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>يتطلب انتباه
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>حسابات معلقة</span>
                    <span class="badge bg-warning">{{ stats.pending_users }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>اشتراكات معلقة</span>
                    <span class="badge bg-info">{{ stats.pending_subscriptions }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>باقات نشطة</span>
                    <span class="badge bg-success">{{ stats.total_packages }}</span>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Recent Activities -->
<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-clock me-2"></i>حسابات جديدة
                </h5>
                <a href="{{ url_for('admin.users') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_users %}
                    {% for user in recent_users %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <img src="https://via.placeholder.com/40" class="rounded-circle" alt="User">
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">{{ user.full_name }}</div>
                            <div class="small text-muted">{{ user.role }} - {{ user.email }}</div>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-warning">معلق</span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد حسابات جديدة</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>اشتراكات جديدة
                </h5>
                <a href="{{ url_for('admin.subscriptions') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_subscriptions %}
                    {% for subscription in recent_subscriptions %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ subscription.user.full_name }}</div>
                            <div class="small text-muted">{{ subscription.package.name }}</div>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="fw-bold text-success">{{ format_currency(subscription.package.price) }}</div>
                            <div class="small text-muted">{{ subscription.purchase_date.strftime('%Y-%m-%d') }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد اشتراكات جديدة</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>حصص قادمة
                </h5>
                <a href="{{ url_for('admin.sessions') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if upcoming_sessions %}
                    {% for session in upcoming_sessions %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ session.student.full_name }}</div>
                            <div class="small text-muted">مع {{ session.teacher.full_name }}</div>
                        </div>
                        <div class="flex-shrink-0 text-end">
                            <div class="small fw-bold">{{ session.scheduled_datetime.strftime('%H:%M') }}</div>
                            <div class="small text-muted">{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد حصص قادمة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Settings Status Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-primary">
            <div class="card-body text-center">
                <div class="display-6 text-primary mb-2">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h6 class="card-title">إعدادات الدفع</h6>
                <p class="card-text small text-muted">إدارة بوابات الدفع والعملات</p>
                <a href="{{ url_for('admin.payment_settings') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-success">
            <div class="card-body text-center">
                <div class="display-6 text-success mb-2">
                    <i class="fas fa-envelope"></i>
                </div>
                <h6 class="card-title">إعدادات البريد</h6>
                <p class="card-text small text-muted">تكوين خادم البريد والإشعارات</p>
                <a href="{{ url_for('admin.email_settings') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-warning">
            <div class="card-body text-center">
                <div class="display-6 text-warning mb-2">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h6 class="card-title">إعدادات الأمان</h6>
                <p class="card-text small text-muted">سياسات الأمان وكلمات المرور</p>
                <a href="{{ url_for('admin.security_settings') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.new_package') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>إضافة باقة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.new_session') }}" class="btn btn-success w-100">
                            <i class="fas fa-calendar-plus me-2"></i>جدولة حصة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.users') }}?status=pending" class="btn btn-warning w-100">
                            <i class="fas fa-user-check me-2"></i>مراجعة الحسابات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.reports') }}" class="btn btn-info w-100">
                            <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                        </a>
                    </div>
                </div>

                <!-- Settings Quick Access Row -->
                <div class="row mt-3">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('admin.payment_settings') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-credit-card me-2"></i>إعدادات الدفع
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('admin.email_settings') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-envelope me-2"></i>إعدادات البريد
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('admin.security_settings') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add some interactivity to cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
{% endblock %}
