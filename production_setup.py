#!/usr/bin/env python3
"""
إعداد شامل للإنتاج - Quran LMS
يقوم بتهيئة قاعدة البيانات وجميع الإعدادات والقوالب تلقائياً
مصمم خصيصاً للعمل على Render وبيئات الإنتاج الأخرى
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🚀 بدء إعداد Quran LMS للإنتاج...")
print("=" * 60)

def setup_database():
    """إعداد قاعدة البيانات"""
    
    print("🗄️ إعداد قاعدة البيانات")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import (User, Package, AcademySettings, PaymentGateway, 
                          EmailSettings, EmailTemplate, SessionSettings)
        
        with app.app_context():
            # إنشاء الجداول
            print("📊 إنشاء جداول قاعدة البيانات...")
            db.create_all()
            print("✅ تم إنشاء الجداول بنجاح")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}")
        return False

def setup_admin_user():
    """إنشاء مستخدم الإدمن الافتراضي"""
    
    print("\n👤 إعداد مستخدم الإدمن")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import User
        
        with app.app_context():
            # فحص وجود الإدمن
            admin_user = User.query.filter_by(email='<EMAIL>').first()
            
            if not admin_user:
                print("👨‍💼 إنشاء مستخدم إدمن افتراضي...")
                admin_user = User(
                    email='<EMAIL>',
                    first_name='مدير',
                    last_name='النظام',
                    role='admin',
                    status='approved'
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء مستخدم الإدمن")
                
                print("\n" + "="*50)
                print("🔑 بيانات الدخول الافتراضية:")
                print("📧 البريد الإلكتروني: <EMAIL>")
                print("🔒 كلمة المرور: admin123")
                print("⚠️ يرجى تغيير كلمة المرور بعد أول تسجيل دخول!")
                print("="*50)
            else:
                print("ℹ️ مستخدم الإدمن موجود بالفعل")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد مستخدم الإدمن: {str(e)}")
        return False

def setup_academy_settings():
    """إعداد إعدادات الأكاديمية"""
    
    print("\n🏫 إعداد إعدادات الأكاديمية")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import AcademySettings
        
        with app.app_context():
            academy_settings = AcademySettings.query.first()
            
            if not academy_settings:
                print("🏛️ إنشاء إعدادات الأكاديمية الافتراضية...")
                academy_settings = AcademySettings(
                    academy_name='أكاديمية القرآن الكريم',
                    academy_description='منصة تعليمية متكاملة لتعلم وتحفيظ القرآن الكريم',
                    contact_email='<EMAIL>',
                    contact_phone='+1234567890',
                    primary_color='#007bff',
                    secondary_color='#6c757d',
                    timezone='UTC',
                    currency='SAR',
                    date_format='%Y-%m-%d',
                    time_format='%H:%M'
                )
                db.session.add(academy_settings)
                db.session.commit()
                print("✅ تم إنشاء إعدادات الأكاديمية")
            else:
                print("ℹ️ إعدادات الأكاديمية موجودة بالفعل")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد إعدادات الأكاديمية: {str(e)}")
        return False

def setup_email_settings():
    """إعداد إعدادات البريد الإلكتروني"""
    
    print("\n📧 إعداد إعدادات البريد الإلكتروني")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import EmailSettings
        
        with app.app_context():
            email_settings = EmailSettings.query.first()
            
            if not email_settings:
                print("📮 إنشاء إعدادات البريد الإلكتروني...")
                email_settings = EmailSettings(
                    enabled=True,
                    smtp_server='smtp.gmail.com',
                    smtp_port=587,
                    use_tls=True,
                    use_ssl=False,
                    default_sender='<EMAIL>',
                    welcome_email_enabled=True,
                    session_reminder_enabled=True,
                    payment_confirmation_enabled=True,
                    subscription_approval_enabled=True,
                    trial_session_notification_enabled=True,
                    makeup_session_notification_enabled=True,
                    session_deletion_notification_enabled=True,
                    reminder_enabled=True,
                    reminder_hours_before=24,
                    reminder_minutes_before=30,
                    reminder_auto_send=True
                )
                db.session.add(email_settings)
                db.session.commit()
                print("✅ تم إنشاء إعدادات البريد الإلكتروني")
                
                print("\n⚠️ تذكير: يجب تحديث إعدادات SMTP في لوحة الإدمن:")
                print("   📧 خادم SMTP")
                print("   👤 اسم المستخدم وكلمة المرور")
                print("   📤 البريد المرسل")
            else:
                # تحديث الإعدادات الموجودة لضمان وجود الحقول الجديدة
                if not hasattr(email_settings, 'enabled') or email_settings.enabled is None:
                    email_settings.enabled = True
                if not hasattr(email_settings, 'use_ssl') or email_settings.use_ssl is None:
                    email_settings.use_ssl = False
                db.session.commit()
                print("ℹ️ إعدادات البريد الإلكتروني موجودة وتم تحديثها")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد البريد الإلكتروني: {str(e)}")
        return False

def install_updated_templates():
    """تثبيت جميع القوالب المحدثة"""

    try:
        from app import app, db
        from models import EmailTemplate

        with app.app_context():
            # القوالب المحدثة بالتصميم الموحد
            updated_templates = [
                {
                    'name': 'subscription_purchased',
                    'display_name': 'إشعار شراء الباقة',
                    'subject': 'تم شراء الباقة بنجاح - {{ academy_name }}',
                    'template_type': 'subscription',
                    'is_system': True,
                    'is_active': True,
                    'variables': 'user_name,package_name,package_price,payment_method,transaction_id,purchase_date,sessions_count,duration_days,academy_name,academy_email,academy_phone,academy_logo,academy_currency',
                    'body_html': '''
<html>
<body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                {% if academy_logo %}
                <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                {% endif %}
                <h2 style="color: #007bff; margin: 0;">🛒 تم شراء الباقة بنجاح</h2>
            </div>
            <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h3 style="color: #0c5460; margin-top: 0;">عزيزي/عزيزتي {{ user_name }}</h3>
                <p style="color: #0c5460; margin-bottom: 0;">شكراً لك على شراء باقة "{{ package_name }}"! تم استلام طلبك بنجاح.</p>
            </div>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h4 style="color: #495057; margin-top: 0;">تفاصيل الشراء:</h4>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">الباقة:</td><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ package_name }}</td></tr>
                    <tr><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">السعر:</td><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #007bff; font-weight: bold;">{{ package_price }} ريال</td></tr>
                    <tr><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">عدد الحصص:</td><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ sessions_count }} حصة</td></tr>
                    <tr><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">مدة الباقة:</td><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ duration_days }} يوم</td></tr>
                    <tr><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">طريقة الدفع:</td><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ payment_method }}</td></tr>
                    <tr><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">رقم المعاملة:</td><td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ transaction_id }}</td></tr>
                    <tr><td style="padding: 8px 0; font-weight: bold; color: #495057;">تاريخ الشراء:</td><td style="padding: 8px 0; color: #6c757d;">{{ purchase_date }}</td></tr>
                </table>
            </div>
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <p style="color: #6c757d; margin: 0;">للتواصل معنا:</p>
                <p style="color: #6c757d; margin: 5px 0;">📧 {{ academy_email }}{% if academy_phone %} | 📞 {{ academy_phone }}{% endif %}</p>
                <p style="color: #6c757d; font-size: 14px; margin-top: 15px;">{{ academy_name }}</p>
            </div>
        </div>
    </div>
</body>
</html>
                    ''',
                    'body_text': 'تم شراء الباقة بنجاح - {{ academy_name }}\n\nعزيزي/عزيزتي {{ user_name }},\n\nشكراً لك على شراء باقة "{{ package_name }}"! تم استلام طلبك بنجاح.\n\nتفاصيل الشراء:\nالباقة: {{ package_name }}\nالسعر: {{ package_price }} ريال\nعدد الحصص: {{ sessions_count }} حصة\nمدة الباقة: {{ duration_days }} يوم\nطريقة الدفع: {{ payment_method }}\nرقم المعاملة: {{ transaction_id }}\nتاريخ الشراء: {{ purchase_date }}\n\nللتواصل معنا:\n📧 {{ academy_email }}\n{% if academy_phone %}📞 {{ academy_phone }}{% endif %}\n\n{{ academy_name }}'
                },
                {
                    'name': 'subscription_activated',
                    'display_name': 'إشعار تفعيل الاشتراك',
                    'subject': 'تم تفعيل اشتراكك بنجاح - {{ academy_name }}',
                    'template_type': 'subscription',
                    'is_system': True,
                    'is_active': True,
                    'variables': 'user_name,package_name,admin_message,sessions_remaining,end_date,academy_name,academy_email,academy_phone,academy_logo,academy_currency',
                    'body_html': '''
<html>
<body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                {% if academy_logo %}
                <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                {% endif %}
                <h2 style="color: #28a745; margin: 0;">🎉 تم تفعيل اشتراكك بنجاح</h2>
            </div>
            <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h3 style="color: #155724; margin-top: 0;">عزيزي/عزيزتي {{ user_name }}</h3>
                <p style="color: #155724; margin-bottom: 0;">مرحباً بك في {{ academy_name }}! تم تفعيل اشتراكك في باقة "{{ package_name }}" بنجاح.</p>
            </div>
            {% if admin_message %}
            <div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h4 style="color: #0056b3; margin-top: 0;">رسالة ترحيب من الإدارة:</h4>
                <p style="color: #0056b3; margin-bottom: 0;">{{ admin_message }}</p>
            </div>
            {% endif %}
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <p style="color: #6c757d; margin: 0;">للتواصل معنا:</p>
                <p style="color: #6c757d; margin: 5px 0;">📧 {{ academy_email }}{% if academy_phone %} | 📞 {{ academy_phone }}{% endif %}</p>
                <p style="color: #6c757d; font-size: 14px; margin-top: 15px;">{{ academy_name }}</p>
            </div>
        </div>
    </div>
</body>
</html>
                    ''',
                    'body_text': 'تم تفعيل اشتراكك بنجاح - {{ academy_name }}\n\nعزيزي/عزيزتي {{ user_name }},\n\nمرحباً بك في {{ academy_name }}! تم تفعيل اشتراكك في باقة "{{ package_name }}" بنجاح.\n\n{% if admin_message %}رسالة ترحيب من الإدارة:\n{{ admin_message }}\n{% endif %}\n\nللتواصل معنا:\n📧 {{ academy_email }}\n{% if academy_phone %}📞 {{ academy_phone }}{% endif %}\n\n{{ academy_name }}'
                }
            ]

            updated_count = 0
            for template_data in updated_templates:
                existing_template = EmailTemplate.query.filter_by(name=template_data['name']).first()

                if existing_template:
                    # تحديث القالب الموجود
                    existing_template.display_name = template_data['display_name']
                    existing_template.subject = template_data['subject']
                    existing_template.template_type = template_data['template_type']
                    existing_template.is_system = template_data['is_system']
                    existing_template.is_active = template_data['is_active']
                    existing_template.variables = template_data['variables']
                    existing_template.body_html = template_data['body_html']
                    existing_template.body_text = template_data['body_text']
                    updated_count += 1
                else:
                    # إنشاء القالب إذا لم يكن موجود
                    new_template = EmailTemplate(
                        name=template_data['name'],
                        display_name=template_data['display_name'],
                        subject=template_data['subject'],
                        template_type=template_data['template_type'],
                        is_system=template_data['is_system'],
                        is_active=template_data['is_active'],
                        variables=template_data['variables'],
                        body_html=template_data['body_html'],
                        body_text=template_data['body_text']
                    )
                    db.session.add(new_template)
                    updated_count += 1

            if updated_count > 0:
                db.session.commit()

            return True

    except Exception as e:
        print(f"❌ خطأ في تثبيت القوالب المحدثة: {str(e)}")
        return False

def setup_email_templates():
    """إعداد قوالب البريد الإلكتروني - جميع القوالب الـ 24"""

    print("\n📝 إعداد قوالب البريد الإلكتروني")
    print("=" * 60)

    try:
        from app import app
        from utils.production_email_templates import auto_install_all_templates, check_templates_installation

        with app.app_context():
            # فحص القوالب الموجودة أولاً
            print("🔍 فحص القوالب الموجودة...")
            templates_ok = check_templates_installation()

            if not templates_ok:
                print("📦 تنصيب القوالب المفقودة...")
                success = auto_install_all_templates()
                if success:
                    print("✅ تم تنصيب جميع قوالب البريد الإلكتروني بنجاح (24 قالب)")
                    return True
                else:
                    print("⚠️ تم التنصيب مع بعض الأخطاء")
                    return False
            else:
                print("✅ جميع قوالب البريد الإلكتروني موجودة (24 قالب)")

                # تحديث القوالب الموجودة للتأكد من أحدث إصدار
                print("🔄 تحديث القوالب للتأكد من أحدث إصدار...")
                success = auto_install_all_templates()
                if success:
                    print("✅ تم تحديث جميع القوالب بنجاح")
                else:
                    print("⚠️ تم التحديث مع بعض الأخطاء")

                return True

    except Exception as e:
        print(f"❌ خطأ في إعداد قوالب البريد الإلكتروني: {str(e)}")
        return False

def setup_session_settings():
    """إعداد إعدادات الحصص"""
    
    print("\n🎓 إعداد إعدادات الحصص")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import SessionSettings
        
        with app.app_context():
            session_settings = SessionSettings.query.first()
            
            if not session_settings:
                print("⚙️ إنشاء إعدادات الحصص الافتراضية...")
                session_settings = SessionSettings(
                    default_provider='jitsi',
                    auto_generate_links=True,
                    default_duration=60,
                    allow_student_reschedule=True,
                    reschedule_hours_limit=24,
                    allow_teacher_reschedule=True,
                    max_reschedules_per_session=2,
                    session_buffer_minutes=15,
                    auto_end_sessions=True,
                    session_timeout_minutes=90,
                    require_session_notes=False,
                    allow_session_recording=False,
                    waiting_room=True
                )
                db.session.add(session_settings)
                db.session.commit()
                print("✅ تم إنشاء إعدادات الحصص")
            else:
                print("ℹ️ إعدادات الحصص موجودة بالفعل")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد إعدادات الحصص: {str(e)}")
        return False

def setup_default_packages():
    """إعداد الباقات الافتراضية"""
    
    print("\n📦 إعداد الباقات الافتراضية")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import Package
        
        with app.app_context():
            if Package.query.count() == 0:
                print("📋 إنشاء الباقات الافتراضية...")
                
                packages = [
                    {
                        'name': 'الباقة الأساسية',
                        'description': 'باقة مناسبة للمبتدئين تشمل 8 حصص شهرياً',
                        'price': 299.99,
                        'duration_days': 30,
                        'sessions_count': 8,
                        'session_duration': 60,
                        'features': '8 حصص شهرياً\nمعلم مؤهل\nمتابعة التقدم\nدعم فني'
                    },
                    {
                        'name': 'الباقة المتوسطة',
                        'description': 'باقة متوسطة تشمل 12 حصة شهرياً مع مميزات إضافية',
                        'price': 449.99,
                        'duration_days': 30,
                        'sessions_count': 12,
                        'session_duration': 60,
                        'features': '12 حصة شهرياً\nمعلم مؤهل\nمتابعة التقدم\nتقارير مفصلة\nدعم فني متقدم'
                    },
                    {
                        'name': 'الباقة المتميزة',
                        'description': 'باقة شاملة تشمل 20 حصة شهرياً مع جميع المميزات',
                        'price': 599.99,
                        'duration_days': 30,
                        'sessions_count': 20,
                        'session_duration': 60,
                        'features': '20 حصة شهرياً\nمعلم متخصص\nمتابعة شخصية\nتقارير مفصلة\nحصص تعويضية\nدعم فني 24/7'
                    }
                ]
                
                for pkg_data in packages:
                    package = Package(
                        name=pkg_data['name'],
                        description=pkg_data['description'],
                        price=pkg_data['price'],
                        duration_days=pkg_data['duration_days'],
                        sessions_count=pkg_data['sessions_count'],
                        session_duration=pkg_data['session_duration'],
                        features=pkg_data['features'],
                        is_active=True
                    )
                    db.session.add(package)
                
                db.session.commit()
                print(f"✅ تم إنشاء {len(packages)} باقة افتراضية")
            else:
                print("ℹ️ الباقات موجودة بالفعل")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد الباقات: {str(e)}")
        return False

def setup_payment_gateways():
    """إعداد بوابات الدفع"""
    
    print("\n💳 إعداد بوابات الدفع")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import PaymentGateway
        
        with app.app_context():
            if PaymentGateway.query.count() == 0:
                print("💰 إنشاء بوابات الدفع الافتراضية...")
                
                gateways = [
                    {
                        'name': 'stripe',
                        'is_active': False,
                        'configuration': '{"currency": "SAR", "country": "SA"}'
                    },
                    {
                        'name': 'paypal',
                        'is_active': False,
                        'configuration': '{"currency": "SAR", "mode": "sandbox"}'
                    }
                ]
                
                for gw_data in gateways:
                    gateway = PaymentGateway(
                        name=gw_data['name'],
                        is_active=gw_data['is_active'],
                        configuration=gw_data['configuration']
                    )
                    db.session.add(gateway)
                
                db.session.commit()
                print(f"✅ تم إنشاء {len(gateways)} بوابة دفع")
                print("⚠️ تذكير: يجب تفعيل وتكوين بوابات الدفع في لوحة الإدمن")
            else:
                print("ℹ️ بوابات الدفع موجودة بالفعل")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد بوابات الدفع: {str(e)}")
        return False

def verify_setup():
    """التحقق من صحة الإعداد"""

    print("\n🔍 التحقق من صحة الإعداد")
    print("=" * 60)

    try:
        from app import app, db
        from models import (User, AcademySettings, EmailSettings, EmailTemplate,
                          SessionSettings, Package, PaymentGateway)

        with app.app_context():
            checks = []

            # فحص المستخدم الإدمن
            admin_count = User.query.filter_by(role='admin').count()
            checks.append(('مستخدمو الإدمن', admin_count > 0, f'{admin_count} مستخدم'))

            # فحص إعدادات الأكاديمية
            academy_exists = AcademySettings.query.first() is not None
            checks.append(('إعدادات الأكاديمية', academy_exists, 'موجودة' if academy_exists else 'مفقودة'))

            # فحص إعدادات البريد
            email_settings = EmailSettings.query.first()
            email_enabled = email_settings and email_settings.enabled
            checks.append(('إعدادات البريد', email_enabled, 'مفعلة' if email_enabled else 'معطلة'))

            # فحص قوالب البريد
            template_count = EmailTemplate.query.count()
            checks.append(('قوالب البريد', template_count > 0, f'{template_count} قالب'))

            # فحص إعدادات الحصص
            session_settings_exists = SessionSettings.query.first() is not None
            checks.append(('إعدادات الحصص', session_settings_exists, 'موجودة' if session_settings_exists else 'مفقودة'))

            # فحص الباقات
            package_count = Package.query.count()
            checks.append(('الباقات', package_count > 0, f'{package_count} باقة'))

            # فحص بوابات الدفع
            gateway_count = PaymentGateway.query.count()
            checks.append(('بوابات الدفع', gateway_count > 0, f'{gateway_count} بوابة'))

            # عرض النتائج
            all_passed = True
            for check_name, passed, details in checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}: {details}")
                if not passed:
                    all_passed = False

            return all_passed

    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False

def show_setup_summary():
    """عرض ملخص الإعداد"""

    print("\n📋 ملخص إعداد الإنتاج")
    print("=" * 60)

    print("🎯 تم إعداد المكونات التالية:")
    print("   🗄️ قاعدة البيانات والجداول")
    print("   👤 مستخدم الإدمن الافتراضي")
    print("   🏫 إعدادات الأكاديمية")
    print("   📧 إعدادات البريد الإلكتروني")
    print("   📝 قوالب البريد الإلكتروني")
    print("   🎓 إعدادات الحصص")
    print("   📦 الباقات الافتراضية")
    print("   💳 بوابات الدفع")

    print("\n⚙️ الإعدادات المطلوبة بعد النشر:")
    print("   📧 تحديث إعدادات SMTP في لوحة الإدمن")
    print("   💳 تفعيل وتكوين بوابات الدفع")
    print("   🔒 تغيير كلمة مرور الإدمن")
    print("   🏫 تحديث معلومات الأكاديمية")
    print("   🌐 تحديث إعدادات النطاق والأمان")

    print("\n🔗 روابط مهمة:")
    print("   🏠 الصفحة الرئيسية: /")
    print("   👨‍💼 لوحة الإدمن: /admin")
    print("   📧 إعدادات البريد: /admin/email-settings")
    print("   💳 بوابات الدفع: /admin/payment-gateways")

    print("\n🚀 النظام جاهز للاستخدام في الإنتاج!")

def main():
    """الدالة الرئيسية للإعداد"""

    print("🚀 إعداد شامل للإنتاج - Quran LMS")
    print("📅 التاريخ: 17 يوليو 2025")
    print("👨‍💻 المطور: Augment Agent")
    print()

    steps = [
        ("إعداد قاعدة البيانات", setup_database),
        ("إعداد مستخدم الإدمن", setup_admin_user),
        ("إعداد إعدادات الأكاديمية", setup_academy_settings),
        ("إعداد إعدادات البريد الإلكتروني", setup_email_settings),
        ("إعداد قوالب البريد الإلكتروني", setup_email_templates),
        ("إعداد إعدادات الحصص", setup_session_settings),
        ("إعداد الباقات الافتراضية", setup_default_packages),
        ("إعداد بوابات الدفع", setup_payment_gateways)
    ]

    success_count = 0
    total_steps = len(steps)

    for step_name, step_function in steps:
        try:
            if step_function():
                success_count += 1
            else:
                print(f"⚠️ فشل في: {step_name}")
        except Exception as e:
            print(f"❌ خطأ في {step_name}: {str(e)}")

    # التحقق النهائي
    print(f"\n📊 تم إكمال {success_count}/{total_steps} خطوات")

    if success_count == total_steps:
        verification_passed = verify_setup()
        if verification_passed:
            print("\n🎉 تم إعداد النظام بنجاح للإنتاج!")
            show_setup_summary()
        else:
            print("\n⚠️ تم الإعداد مع بعض التحذيرات")
    else:
        print("\n❌ فشل في إعداد النظام بالكامل")
        print("📞 يرجى مراجعة الأخطاء وإعادة المحاولة")

if __name__ == '__main__':
    main()
