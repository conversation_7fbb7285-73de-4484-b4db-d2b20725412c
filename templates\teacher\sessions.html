{% extends "base.html" %}

{% block title %}حصصي - {{ academy_name }}{% endblock %}
{% block page_title %}حصصي{% endblock %}

{% block content %}
<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">حالة الحصة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="scheduled" {{ 'selected' if status_filter == 'scheduled' }}>مجدولة</option>
                            <option value="completed" {{ 'selected' if status_filter == 'completed' }}>مكتملة</option>
                            <option value="cancelled" {{ 'selected' if status_filter == 'cancelled' }}>ملغية</option>
                            <option value="missed" {{ 'selected' if status_filter == 'missed' }}>فائتة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ date_filter }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ url_for('teacher.sessions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إزالة الفلاتر
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Sessions List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>قائمة الحصص
                </h5>
                <span class="badge bg-primary">{{ sessions.total }} حصة</span>
            </div>
            <div class="card-body">
                {% if sessions.items %}
                    {% for session in sessions.items %}
                    <div class="card mb-3 session-card {{ 'border-success' if session.status == 'completed' else 'border-primary' if session.status == 'scheduled' else 'border-danger' }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">{{ session.student.full_name }}</h6>
                                <small class="text-muted">
                                    <i class="fas fa-tag me-1"></i>
                                    {% if session.session_type == 'trial' %}حصة تجريبية
                                    {% elif session.session_type == 'makeup' %}حصة تعويضية
                                    {% else %}حصة مجدولة{% endif %}
                                </small>
                            </div>
                            <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                                {% if session.status == 'completed' %}مكتملة
                                {% elif session.status == 'scheduled' %}مجدولة
                                {% elif session.status == 'cancelled' %}ملغية
                                {% else %}فائتة{% endif %}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-calendar text-primary me-2"></i>
                                        <span>{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        <span>{{ session.scheduled_datetime.strftime('%H:%M') }}</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-hourglass-half text-primary me-2"></i>
                                        <span>{{ session.duration_minutes }} دقيقة</span>
                                    </div>
                                    {% if session.subscription %}
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-box text-primary me-2"></i>
                                        <span>{{ session.subscription.package.name }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    {% if session.status == 'completed' %}
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-user-check text-success me-2"></i>
                                        <span>حضور الطالب: {{ 'نعم' if session.student_attended else 'لا' }}</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-chalkboard-teacher text-success me-2"></i>
                                        <span>حضور المعلم: {{ 'نعم' if session.teacher_attended else 'لا' }}</span>
                                    </div>
                                    {% if session.completed_at %}
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>اكتملت في: {{ session.completed_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                    </div>
                                    {% endif %}
                                    {% endif %}
                                    
                                    {% if session.ratings %}
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-star text-warning me-2"></i>
                                        <span>التقييم: {{ session.ratings[0].rating }}/5</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if session.notes %}
                            <div class="mt-3">
                                <h6>ملاحظاتي:</h6>
                                <p class="text-muted">{{ session.notes }}</p>
                            </div>
                            {% endif %}
                            
                            <div class="mt-3 d-flex gap-2 flex-wrap">
                                <a href="{{ url_for('teacher.session_details', session_id=session.id) }}"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>التفاصيل
                                </a>

                                {% if session.status == 'scheduled' %}
                                    {% if session.meeting_link %}
                                        <small class="text-muted d-block mt-1">
                                            <i class="fas fa-info-circle me-1"></i>{{ session.get_meeting_provider_name() }}
                                        </small>
                                    {% endif %}

                                    <button type="button" class="btn btn-warning btn-sm"
                                            data-bs-toggle="modal" data-bs-target="#completeModal{{ session.id }}">
                                        <i class="fas fa-check me-1"></i>إكمال الحصة
                                    </button>
                                {% endif %}
                                
                                {% if session.status == 'completed' and session.ratings %}
                                <button type="button" class="btn btn-outline-info btn-sm" 
                                        data-bs-toggle="modal" data-bs-target="#ratingModal{{ session.id }}">
                                    <i class="fas fa-star me-1"></i>عرض التقييم
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- Pagination -->
                    {% if sessions.pages > 1 %}
                    <nav aria-label="صفحات الحصص">
                        <ul class="pagination justify-content-center">
                            {% if sessions.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('teacher.sessions', page=sessions.prev_num, status=status_filter, date=date_filter) }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in sessions.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != sessions.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('teacher.sessions', page=page_num, status=status_filter, date=date_filter) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if sessions.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('teacher.sessions', page=sessions.next_num, status=status_filter, date=date_filter) }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد حصص</h5>
                        <p class="text-muted">لم يتم جدولة أي حصص لك بعد.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Complete Session Modals -->
{% for session in sessions.items %}
{% if session.status == 'scheduled' %}
<div class="modal fade" id="completeModal{{ session.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إكمال الحصة مع {{ session.student.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('teacher.complete_session', session_id=session.id) }}">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="student_attended{{ session.id }}" 
                                   name="student_attended" checked>
                            <label class="form-check-label" for="student_attended{{ session.id }}">
                                الطالب حضر الحصة
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes{{ session.id }}" class="form-label">ملاحظات الحصة</label>
                        <textarea class="form-control" id="notes{{ session.id }}" name="notes" 
                                  rows="4" placeholder="اكتب ملاحظاتك حول الحصة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إكمال الحصة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}

<!-- Rating View Modals -->
{% for session in sessions.items %}
{% if session.status == 'completed' and session.ratings %}
<div class="modal fade" id="ratingModal{{ session.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم الحصة من {{ session.student.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                {% set rating = session.ratings[0] %}
                <div class="text-center mb-3">
                    <div class="h2 text-warning">{{ rating.rating }}/5</div>
                    <div class="rating-display">
                        {% for i in range(1, 6) %}
                            <i class="fas fa-star {{ 'text-warning' if i <= rating.rating else 'text-muted' }}"></i>
                        {% endfor %}
                    </div>
                </div>
                
                {% if rating.comment %}
                <div class="mt-3">
                    <h6>تعليق الطالب:</h6>
                    <p class="text-muted">{{ rating.comment }}</p>
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <small class="text-muted">تاريخ التقييم: {{ rating.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
    .session-card {
        transition: all 0.3s ease;
    }
    
    .session-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .rating-display {
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.getElementById('status').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('date').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Add animation to session cards
    document.querySelectorAll('.session-card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
</script>
{% endblock %}
