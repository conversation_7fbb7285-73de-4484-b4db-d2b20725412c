# 🚀 دليل النشر على Render

دليل شامل لنشر نظام Quran LMS على منصة Render.

## 📋 المتطلبات المسبقة

1. **حساب GitHub** مع مستودع يحتوي على الكود
2. **حساب Render** (مجاني) - [render.com](https://render.com)
3. **حساب Gmail** لإرسال الإشعارات
4. **حساب Stripe** للمدفوعات (اختياري)

## 🗄️ إعداد قاعدة البيانات

### 1. إنشاء PostgreSQL Database
1. سجل دخول إلى Render
2. اضغط "New" → "PostgreSQL"
3. املأ البيانات:
   - **Name**: `quranlms-db`
   - **Database**: `quranlms`
   - **User**: `quranlms_user`
   - **Region**: اختر الأقرب لك
   - **Plan**: Free
4. اضغط "Create Database"
5. انتظر حتى يكتمل الإعداد
6. احفظ **DATABASE_URL** من صفحة التفاصيل

## 🌐 إعداد Web Service

### 1. إنشاء Web Service
1. في Render، اضغط "New" → "Web Service"
2. اربط حساب GitHub واختر المستودع
3. املأ البيانات:
   - **Name**: `quranlms`
   - **Environment**: `Python 3`
   - **Region**: نفس منطقة قاعدة البيانات
   - **Branch**: `main` أو `master`
   - **Build Command**: 
     ```bash
     pip install --upgrade pip && pip install -r requirements.txt && python production_setup.py
     ```
   - **Start Command**: 
     ```bash
     gunicorn app:app
     ```
   - **Plan**: Free

### 2. إعداد متغيرات البيئة
في قسم "Environment Variables"، أضف:

#### متغيرات أساسية:
```
SECRET_KEY=your-super-secret-production-key-here-change-this
FLASK_ENV=production
DEBUG=False
```

#### قاعدة البيانات:
```
DATABASE_URL=*****************************************************
```
(استخدم الرابط من قاعدة البيانات التي أنشأتها)

#### إعدادات البريد الإلكتروني:
```
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>
```

#### إعدادات الدفع (اختياري):
```
STRIPE_PUBLISHABLE_KEY=pk_live_your_key
STRIPE_SECRET_KEY=sk_live_your_key
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_secret
PAYPAL_MODE=live
```

## 📧 إعداد Gmail للإشعارات

### 1. تفعيل App Passwords
1. اذهب إلى [Google Account Settings](https://myaccount.google.com/)
2. اختر "Security" → "2-Step Verification"
3. فعل المصادقة الثنائية إذا لم تكن مفعلة
4. اذهب إلى "App passwords"
5. اختر "Mail" و "Other"
6. أدخل "Quran LMS" كاسم التطبيق
7. احفظ كلمة المرور المُنشأة

### 2. استخدام App Password
استخدم كلمة المرور المُنشأة في `MAIL_PASSWORD` وليس كلمة مرور Gmail العادية.

## 💳 إعداد Stripe (اختياري)

### 1. إنشاء حساب Stripe
1. اذهب إلى [stripe.com](https://stripe.com)
2. أنشئ حساب جديد
3. أكمل عملية التحقق

### 2. الحصول على API Keys
1. في لوحة تحكم Stripe
2. اذهب إلى "Developers" → "API keys"
3. احفظ:
   - **Publishable key**: يبدأ بـ `pk_live_`
   - **Secret key**: يبدأ بـ `sk_live_`

## 🚀 النشر

### 1. رفع الكود إلى GitHub
```bash
git add .
git commit -m "Ready for production deployment"
git push origin main
```

### 2. تشغيل النشر
1. في Render، اضغط "Create Web Service"
2. انتظر حتى يكتمل البناء (5-10 دقائق)
3. ستحصل على رابط مثل: `https://quranlms.onrender.com`

## ✅ التحقق من النشر

### 1. فحص الموقع
1. افتح الرابط المُنشأ
2. تأكد من ظهور صفحة تسجيل الدخول
3. جرب إنشاء حساب جديد

### 2. فحص قاعدة البيانات
1. سجل دخول كمدير
2. تأكد من وجود البيانات الأساسية
3. جرب إنشاء باقة جديدة

### 3. فحص الإشعارات
1. جرب إنشاء حساب جديد
2. تأكد من وصول بريد التأكيد
3. فحص الإشعارات الداخلية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في قاعدة البيانات
```
Error: relation "user" does not exist
```
**الحل**: تأكد من تشغيل `python production_setup.py` في Build Command

#### 2. خطأ في البريد الإلكتروني
```
SMTPAuthenticationError
```
**الحل**: تأكد من استخدام App Password وليس كلمة مرور Gmail العادية

#### 3. خطأ في المتغيرات
```
KeyError: 'SECRET_KEY'
```
**الحل**: تأكد من إضافة جميع متغيرات البيئة المطلوبة

### عرض السجلات:
1. في Render، اذهب إلى صفحة الخدمة
2. اضغط "Logs" لعرض سجلات الأخطاء
3. ابحث عن رسائل الخطأ

## 🔄 التحديثات

### نشر تحديث جديد:
1. ادفع التغييرات إلى GitHub:
   ```bash
   git add .
   git commit -m "Update description"
   git push origin main
   ```
2. Render سيعيد النشر تلقائياً

### إعادة تشغيل الخدمة:
1. في صفحة الخدمة
2. اضغط "Manual Deploy" → "Deploy latest commit"

## 📞 الدعم

إذا واجهت مشاكل:
1. راجع سجلات Render
2. تأكد من صحة متغيرات البيئة
3. أنشئ issue في GitHub مع تفاصيل الخطأ

---

**نصائح للنجاح:**
- استخدم كلمات مرور قوية
- احتفظ بنسخة احتياطية من متغيرات البيئة
- راقب استخدام الموارد في الخطة المجانية
- فعل HTTPS دائماً في الإنتاج
