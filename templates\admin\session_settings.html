{% extends "base.html" %}

{% block title %}إعدادات الحصص - {{ academy_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h3 mb-1">إعدادات الحصص</h2>
                    <p class="text-muted">إدارة مزودي خدمات الحصص الافتراضية وإعدادات توليد الروابط</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('admin.settings') }}">الإعدادات</a></li>
                        <li class="breadcrumb-item active">إعدادات الحصص</li>
                    </ol>
                </nav>
            </div>

            <form method="POST" action="{{ url_for('admin.session_settings') }}" id="sessionSettingsForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                <!-- General Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>الإعدادات العامة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="default_provider" class="form-label">مزود الخدمة الافتراضي</label>
                                    <select class="form-select" id="default_provider" name="default_provider" required>
                                        <option value="google_meet" {{ 'selected' if settings.default_provider == 'google_meet' else '' }}>
                                            Google Meet
                                        </option>
                                        <option value="jitsi" {{ 'selected' if settings.default_provider == 'jitsi' else '' }}>
                                            Jitsi Meet
                                        </option>
                                    </select>
                                    <div class="form-text">سيتم استخدام هذا المزود افتراضياً عند إنشاء حصص جديدة</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto_generate_links" 
                                               name="auto_generate_links" {{ 'checked' if settings.auto_generate_links else '' }}>
                                        <label class="form-check-label" for="auto_generate_links">
                                            توليد الروابط تلقائياً
                                        </label>
                                    </div>
                                    <div class="form-text">عند التفعيل، سيتم توليد روابط الحصص تلقائياً عند الإنشاء</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Google Meet Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fab fa-google me-2 text-primary"></i>إعدادات Google Meet
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="use_google_calendar"
                                               name="use_google_calendar" {{ 'checked' if settings.use_google_calendar else '' }}>
                                        <label class="form-check-label" for="use_google_calendar">
                                            استخدام Google Calendar لـ Google Meet
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        سيتم إنشاء Google Meet links تلقائياً عبر Google Calendar
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-warning">
                                    <i class="fas fa-cog me-2"></i>
                                    <strong>تتطلب إعداد Google Calendar</strong><br>
                                    <small>يجب تفعيل وإعداد Google Calendar أولاً من صفحة الإعدادات</small>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>الميزة الجديدة:</strong> عند تفعيل هذا الخيار، سيتم إنشاء Google Meet links تلقائياً
                            مع كل حصة جديدة عبر Google Calendar، ولن تحتاج لإنشاء الروابط يدوياً.
                        </div>
                    </div>
                </div>

                <!-- Jitsi Meet Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-video me-2 text-info"></i>إعدادات Jitsi Meet
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="jitsi_enabled" 
                                               name="jitsi_enabled" {{ 'checked' if settings.jitsi_enabled else '' }}>
                                        <label class="form-check-label" for="jitsi_enabled">
                                            تفعيل Jitsi Meet
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="jitsi_domain" class="form-label">النطاق</label>
                                    <input type="text" class="form-control" id="jitsi_domain" 
                                           name="jitsi_domain" value="{{ settings.jitsi_domain or 'meet.jit.si' }}">
                                    <div class="form-text">النطاق المستخدم لروابط Jitsi</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="jitsi_prefix" class="form-label">البادئة</label>
                                    <input type="text" class="form-control" id="jitsi_prefix" 
                                           name="jitsi_prefix" value="{{ settings.jitsi_prefix or 'academy' }}">
                                    <div class="form-text">البادئة المستخدمة في أسماء الغرف</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="jitsi_password_required" 
                                               name="jitsi_password_required" {{ 'checked' if settings.jitsi_password_required else '' }}>
                                        <label class="form-check-label" for="jitsi_password_required">
                                            كلمة مرور مطلوبة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>مثال على الرابط:</strong> https://meet.jit.si/AcademySession123abc
                        </div>
                    </div>
                </div>


                <!-- Link Generation Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-link me-2"></i>إعدادات توليد الروابط
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="include_session_id" 
                                               name="include_session_id" {{ 'checked' if settings.include_session_id else '' }}>
                                        <label class="form-check-label" for="include_session_id">
                                            تضمين معرف الحصة
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="include_date" 
                                               name="include_date" {{ 'checked' if settings.include_date else '' }}>
                                        <label class="form-check-label" for="include_date">
                                            تضمين التاريخ
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="include_time" 
                                               name="include_time" {{ 'checked' if settings.include_time else '' }}>
                                        <label class="form-check-label" for="include_time">
                                            تضمين الوقت
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="require_password" 
                                               name="require_password" {{ 'checked' if settings.require_password else '' }}>
                                        <label class="form-check-label" for="require_password">
                                            كلمة مرور مطلوبة
                                        </label>
                                    </div>
                                    <div class="form-text">يتطلب كلمة مرور للدخول للحصة</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="waiting_room" 
                                               name="waiting_room" {{ 'checked' if settings.waiting_room else '' }}>
                                        <label class="form-check-label" for="waiting_room">
                                            غرفة الانتظار
                                        </label>
                                    </div>
                                    <div class="form-text">المشاركون ينتظرون موافقة المضيف</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
