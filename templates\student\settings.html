{% extends "base.html" %}

{% block title %}الإعدادات - {{ super() }}{% endblock %}

{% block page_title %}الإعدادات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-3">
        <!-- Settings Navigation -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cog me-2"></i>إعدادات الحساب</h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="#notifications" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                </a>
                <a href="#account" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="fas fa-user-cog me-2"></i>معلومات الحساب
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-9">
        <div class="tab-content">
            <!-- Notifications Settings -->
            <div class="tab-pane fade show active" id="notifications">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-bell me-2"></i>إعدادات الإشعارات</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('student.update_notification_settings') }}">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            
                            <div class="mb-4">
                                <h6>إشعارات البريد الإلكتروني</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications"
                                           {% if notification_settings.email_notifications %}checked{% endif %}>
                                    <label class="form-check-label" for="email_notifications">
                                        تلقي إشعارات عبر البريد الإلكتروني
                                    </label>
                                </div>
                                <small class="text-muted">ستتلقى إشعارات حول الحصص والدفعات والتحديثات المهمة</small>
                            </div>

                            <div class="mb-4">
                                <h6>تذكيرات الحصص</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="session_reminders" name="session_reminders"
                                           {% if notification_settings.session_reminders %}checked{% endif %}>
                                    <label class="form-check-label" for="session_reminders">
                                        تذكيرات قبل الحصص
                                    </label>
                                </div>
                                <small class="text-muted">ستتلقى تذكيرات قبل موعد الحصة (حسب إعدادات النظام)</small>
                            </div>

                            <div class="mb-4">
                                <h6>إشعارات الدفع</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="payment_notifications" name="payment_notifications"
                                           {% if notification_settings.payment_notifications %}checked{% endif %}>
                                    <label class="form-check-label" for="payment_notifications">
                                        إشعارات الدفع والفواتير
                                    </label>
                                </div>
                                <small class="text-muted">ستتلقى إشعارات حول حالة الدفعات والفواتير</small>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </form>
                    </div>
                </div>
            </div>



            <!-- Account Settings -->
            <div class="tab-pane fade" id="account">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-cog me-2"></i>إعدادات الحساب</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6>معلومات الحساب</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>البريد الإلكتروني:</strong> {{ current_user.email }}</p>
                                    <p><strong>تاريخ الانضمام:</strong> {{ current_user.created_at.strftime('%Y/%m/%d') }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>نوع الحساب:</strong> طالب</p>
                                    <p><strong>حالة الحساب:</strong> 
                                        {% if current_user.status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ current_user.status }}</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6 class="text-danger">منطقة الخطر</h6>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير</h6>
                                <p class="mb-3">الإجراءات التالية لا يمكن التراجع عنها:</p>

                                <div class="mb-3">
                                    <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                                        <i class="fas fa-trash me-2"></i>حذف الحساب نهائياً
                                    </button>
                                    <small class="d-block mt-2 text-muted">
                                        سيتم حذف جميع بياناتك وحصصك واشتراكاتك نهائياً
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAccountModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الحساب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-warning me-2"></i>تحذير مهم!</h6>
                    <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه. سيتم حذف:</p>
                </div>

                <ul class="list-unstyled">
                    <li><i class="fas fa-times text-danger me-2"></i>جميع بياناتك الشخصية</li>
                    <li><i class="fas fa-times text-danger me-2"></i>سجل الحصص والتقييمات</li>
                    <li><i class="fas fa-times text-danger me-2"></i>الاشتراكات والمدفوعات</li>
                    <li><i class="fas fa-times text-danger me-2"></i>الإعدادات والتفضيلات</li>
                </ul>

                <div class="mb-3">
                    <label for="confirmText" class="form-label">
                        لتأكيد الحذف، اكتب <strong>"حذف حسابي"</strong> في الحقل أدناه:
                    </label>
                    <input type="text" class="form-control" id="confirmText" placeholder="حذف حسابي">
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="confirmUnderstand">
                    <label class="form-check-label" for="confirmUnderstand">
                        أفهم أن هذا الإجراء لا يمكن التراجع عنه
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('student.delete_account') }}" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn" disabled>
                        <i class="fas fa-trash me-2"></i>حذف الحساب نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle tab switching
    const tabLinks = document.querySelectorAll('[data-bs-toggle="pill"]');
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all tabs
            tabLinks.forEach(l => l.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // Add active class to clicked tab
            this.classList.add('active');
            const targetId = this.getAttribute('href').substring(1);
            const targetPane = document.getElementById(targetId);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }
        });
    });

    // Handle delete account confirmation
    const confirmText = document.getElementById('confirmText');
    const confirmCheckbox = document.getElementById('confirmUnderstand');
    const deleteBtn = document.getElementById('confirmDeleteBtn');

    function checkDeleteConfirmation() {
        const textMatch = confirmText.value.trim() === 'حذف حسابي';
        const checkboxChecked = confirmCheckbox.checked;
        deleteBtn.disabled = !(textMatch && checkboxChecked);
    }

    if (confirmText && confirmCheckbox && deleteBtn) {
        confirmText.addEventListener('input', checkDeleteConfirmation);
        confirmCheckbox.addEventListener('change', checkDeleteConfirmation);

        // Reset modal when closed
        document.getElementById('deleteAccountModal').addEventListener('hidden.bs.modal', function() {
            confirmText.value = '';
            confirmCheckbox.checked = false;
            deleteBtn.disabled = true;
        });
    }
});
</script>
{% endblock %}
