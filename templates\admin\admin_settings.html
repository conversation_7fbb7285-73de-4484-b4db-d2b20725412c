{% extends "base.html" %}

{% block title %}الإعدادات الشخصية - {{ super() }}{% endblock %}

{% block page_title %}الإعدادات الشخصية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-3">
        <!-- Settings Navigation -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user-cog me-2"></i>إعدادات الحساب</h6>
            </div>
            <div class="list-group list-group-flush">
                <a href="#notifications" class="list-group-item list-group-item-action active" data-bs-toggle="pill">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                </a>
                <a href="#account" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                    <i class="fas fa-user-shield me-2"></i>معلومات الحساب
                </a>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-external-link-alt me-2"></i>روابط سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.settings') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-cog me-2"></i>الإعدادات العامة
                    </a>
                    <a href="{{ url_for('admin.security_settings') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-shield-alt me-2"></i>أمان النظام
                    </a>
                    <a href="{{ url_for('admin.email_settings') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-envelope me-2"></i>إعدادات البريد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-9">
        <div class="tab-content">
            <!-- Notifications Settings -->
            <div class="tab-pane fade show active" id="notifications">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-bell me-2"></i>إعدادات الإشعارات</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('admin.update_admin_notification_settings') }}">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            
                            <div class="mb-4">
                                <h6>إشعارات البريد الإلكتروني</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications"
                                           {% if notification_settings.email_notifications %}checked{% endif %}>
                                    <label class="form-check-label" for="email_notifications">
                                        تلقي إشعارات عبر البريد الإلكتروني
                                    </label>
                                </div>
                                <small class="text-muted">ستتلقى إشعارات حول الأنشطة المهمة في النظام</small>
                            </div>

                            <div class="mb-4">
                                <h6>تنبيهات النظام</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="system_alerts" name="system_alerts"
                                           {% if notification_settings.system_alerts %}checked{% endif %}>
                                    <label class="form-check-label" for="system_alerts">
                                        تنبيهات أخطاء النظام
                                    </label>
                                </div>
                                <small class="text-muted">ستتلقى تنبيهات فورية عند حدوث أخطاء في النظام</small>
                            </div>

                            <div class="mb-4">
                                <h6>تنبيهات نشاط المستخدمين</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="user_activity_alerts" name="user_activity_alerts"
                                           {% if notification_settings.user_activity_alerts %}checked{% endif %}>
                                    <label class="form-check-label" for="user_activity_alerts">
                                        إشعارات نشاط المستخدمين
                                    </label>
                                </div>
                                <small class="text-muted">ستتلقى إشعارات عند تسجيل مستخدمين جدد أو أنشطة مهمة</small>
                            </div>

                            <div class="mb-4">
                                <h6>تنبيهات المدفوعات</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="payment_alerts" name="payment_alerts"
                                           {% if notification_settings.payment_notifications %}checked{% endif %}>
                                    <label class="form-check-label" for="payment_alerts">
                                        إشعارات المدفوعات والاشتراكات
                                    </label>
                                </div>
                                <small class="text-muted">ستتلقى إشعارات عند حدوث مدفوعات جديدة أو مشاكل في الدفع</small>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </form>
                    </div>
                </div>
            </div>



            <!-- Account Info -->
            <div class="tab-pane fade" id="account">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user-shield me-2"></i>معلومات الحساب</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6>معلومات أساسية</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم:</strong> {{ current_user.full_name }}</p>
                                    <p><strong>البريد الإلكتروني:</strong> {{ current_user.email }}</p>
                                    <p><strong>تاريخ الانضمام:</strong> {{ current_user.created_at.strftime('%Y/%m/%d') }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>نوع الحساب:</strong> مدير النظام</p>
                                    <p><strong>حالة الحساب:</strong> 
                                        <span class="badge bg-success">نشط</span>
                                    </p>
                                    <p><strong>آخر تسجيل دخول:</strong> الآن</p>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6>صلاحيات الحساب</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>إدارة المستخدمين</li>
                                        <li><i class="fas fa-check text-success me-2"></i>إدارة الباقات</li>
                                        <li><i class="fas fa-check text-success me-2"></i>إدارة الحصص</li>
                                        <li><i class="fas fa-check text-success me-2"></i>إدارة المدفوعات</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>عرض التقارير</li>
                                        <li><i class="fas fa-check text-success me-2"></i>إعدادات النظام</li>
                                        <li><i class="fas fa-check text-success me-2"></i>إعدادات الأمان</li>
                                        <li><i class="fas fa-check text-success me-2"></i>إدارة الإشعارات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6 class="text-danger">منطقة الخطر</h6>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-shield-alt me-2"></i>حساب محمي</h6>
                                <p class="mb-3">كونك مدير النظام، حسابك محمي من الحذف أو الإلغاء.</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>حماية من الحذف</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>حماية من الإلغاء</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>صلاحيات كاملة</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>وصول دائم</span>
                                        </div>
                                    </div>
                                </div>

                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    لضمان استمرارية النظام، لا يمكن حذف حساب مدير النظام
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle tab switching
    const tabLinks = document.querySelectorAll('[data-bs-toggle="pill"]');
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs
            tabLinks.forEach(l => l.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });
            
            // Add active class to clicked tab
            this.classList.add('active');
            const targetId = this.getAttribute('href').substring(1);
            const targetPane = document.getElementById(targetId);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }
        });
    });
});
</script>
{% endblock %}
