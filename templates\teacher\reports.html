{% extends "base.html" %}

{% block title %}تقاريري - {{ academy_name }}{% endblock %}
{% block page_title %}تقاريري{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-primary text-uppercase mb-1">إجمالي الحصص</div>
                        <div class="h5 mb-0">{{ stats.total_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">حصص مكتملة</div>
                        <div class="h5 mb-0">{{ stats.completed_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">حصص هذا الشهر</div>
                        <div class="h5 mb-0">{{ stats.monthly_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-week fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">متوسط التقييم</div>
                        <div class="h5 mb-0">
                            {% if stats.avg_rating > 0 %}
                            {{ stats.avg_rating }}/5
                            {% else %}
                            لا يوجد
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-star fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>توزيع حالات الحصص
                </h5>
            </div>
            <div class="card-body">
                <canvas id="sessionsStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>الحصص الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlySessionsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>مؤشرات الأداء
                </h5>
            </div>
            <div class="card-body">
                <!-- Completion Rate -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">معدل إكمال الحصص</span>
                        <span class="text-muted">
                            {% if stats.total_sessions > 0 %}
                            {{ "%.1f"|format((stats.completed_sessions / stats.total_sessions * 100)) }}%
                            {% else %}
                            0%
                            {% endif %}
                        </span>
                    </div>
                    <div class="progress">
                        {% if stats.total_sessions > 0 %}
                        {% set completion_rate = (stats.completed_sessions / stats.total_sessions * 100) %}
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ completion_rate }}%"
                             aria-valuenow="{{ completion_rate }}" aria-valuemin="0" aria-valuemax="100">
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Rating Progress -->
                {% if stats.avg_rating > 0 %}
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">متوسط التقييم</span>
                        <span class="text-muted">{{ stats.avg_rating }}/5</span>
                    </div>
                    <div class="progress">
                        {% set rating_percentage = (stats.avg_rating / 5 * 100) %}
                        <div class="progress-bar bg-warning" role="progressbar" 
                             style="width: {{ rating_percentage }}%"
                             aria-valuenow="{{ rating_percentage }}" aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Monthly Performance -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">نشاط هذا الشهر</span>
                        <span class="text-muted">{{ stats.monthly_sessions }} حصة</span>
                    </div>
                    <div class="progress">
                        {% set monthly_target = 20 %}
                        {% set monthly_percentage = (stats.monthly_sessions / monthly_target * 100) if monthly_target > 0 else 0 %}
                        {% if monthly_percentage > 100 %}{% set monthly_percentage = 100 %}{% endif %}
                        <div class="progress-bar bg-info" role="progressbar" 
                             style="width: {{ monthly_percentage }}%"
                             aria-valuenow="{{ monthly_percentage }}" aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                    <small class="text-muted">الهدف: {{ monthly_target }} حصة شهرياً</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>الإنجازات
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% if stats.completed_sessions >= 10 %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-medal text-warning me-3"></i>
                            <div>
                                <div class="fw-bold">معلم نشط</div>
                                <small class="text-muted">أكمل 10+ حصص</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if stats.avg_rating >= 4.5 %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-star text-warning me-3"></i>
                            <div>
                                <div class="fw-bold">معلم متميز</div>
                                <small class="text-muted">تقييم 4.5+ نجوم</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if stats.completed_sessions >= 50 %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-crown text-warning me-3"></i>
                            <div>
                                <div class="fw-bold">معلم خبير</div>
                                <small class="text-muted">أكمل 50+ حصة</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if stats.monthly_sessions >= 15 %}
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-fire text-danger me-3"></i>
                            <div>
                                <div class="fw-bold">معلم مجتهد</div>
                                <small class="text-muted">15+ حصة هذا الشهر</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if not (stats.completed_sessions >= 10 or stats.avg_rating >= 4.5 or stats.completed_sessions >= 50 or stats.monthly_sessions >= 15) %}
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-trophy fa-2x mb-2"></i>
                        <p>استمر في التدريس لكسب الإنجازات!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('teacher.sessions') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-calendar-alt me-2"></i>عرض جميع الحصص
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('teacher.students') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-graduate me-2"></i>عرض الطلاب
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('teacher.sessions') }}?status=scheduled" class="btn btn-outline-info w-100">
                            <i class="fas fa-clock me-2"></i>الحصص المجدولة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('teacher.dashboard') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Sessions Status Pie Chart
    const statusCtx = document.getElementById('sessionsStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['مكتملة', 'مجدولة', 'ملغية', 'فائتة'],
            datasets: [{
                data: [
                    {{ stats.completed_sessions }},
                    {{ stats.total_sessions - stats.completed_sessions }},
                    0, // Cancelled sessions - would need to be calculated
                    0  // Missed sessions - would need to be calculated
                ],
                backgroundColor: [
                    '#28a745',
                    '#007bff',
                    '#dc3545',
                    '#6c757d'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Monthly Sessions Bar Chart
    const monthlyCtx = document.getElementById('monthlySessionsChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'bar',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'عدد الحصص',
                data: [12, 19, 15, 17, 20, {{ stats.monthly_sessions }}],
                backgroundColor: '#007bff',
                borderColor: '#0056b3',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
</script>
{% endblock %}
