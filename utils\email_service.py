"""
Email service for the Quran LMS system
Handles email sending, template management, and notifications
"""

import smtplib
import json
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from flask import current_app, render_template_string
from models import EmailSettings, EmailTemplate, EmailLog, db


class EmailService:
    def __init__(self):
        self.smtp_server = None
        self.smtp_port = None
        self.smtp_username = None
        self.smtp_password = None
        self.use_tls = True
        self.default_sender = None
        self._load_settings()
    
    def _load_settings(self):
        """Load email settings from database"""
        try:
            from flask import has_app_context

            # التأكد من وجود app context
            if not has_app_context():
                print("تحذير: لا يوجد app context لتحميل إعدادات البريد")
                self._set_default_values()
                return

            settings = EmailSettings.query.first()
            if settings:
                self.smtp_server = settings.smtp_server
                self.smtp_port = settings.smtp_port or 587
                self.smtp_username = settings.smtp_username
                self.smtp_password = settings.smtp_password
                self.use_tls = settings.use_tls if settings.use_tls is not None else True
                self.default_sender = settings.default_sender or settings.smtp_username

                # طباعة حالة الإعدادات للتشخيص
                print(f"📧 تم تحميل إعدادات البريد:")
                print(f"   SMTP Server: {self.smtp_server}")
                print(f"   SMTP Username: {self.smtp_username}")
                print(f"   Default Sender: {self.default_sender}")
            else:
                print("⚠️ لا توجد إعدادات بريد في قاعدة البيانات")
                self._set_default_values()

        except Exception as e:
            print(f"❌ خطأ في تحميل إعدادات البريد: {str(e)}")
            self._set_default_values()

    def _set_default_values(self):
        """Set default email settings"""
        self.smtp_server = 'smtp.gmail.com'
        self.smtp_port = 587
        self.smtp_username = None
        self.smtp_password = None
        self.use_tls = True
        self.default_sender = None
    
    def test_connection(self):
        """Test SMTP connection"""
        try:
            if not all([self.smtp_server, self.smtp_port, self.smtp_username, self.smtp_password]):
                return False, "إعدادات SMTP غير مكتملة"
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            if self.use_tls:
                server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            server.quit()
            return True, "تم الاتصال بنجاح"
        except Exception as e:
            return False, f"فشل الاتصال: {str(e)}"
    
    def send_email(self, to_email, subject, body_html, body_text=None, template_name=None):
        """Send email"""
        try:
            # Create email log entry
            email_log = EmailLog(
                recipient_email=to_email,
                subject=subject,
                template_name=template_name,
                status='pending'
            )
            db.session.add(email_log)
            db.session.commit()
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = self.default_sender
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add text and HTML parts
            if body_text:
                text_part = MIMEText(body_text, 'plain', 'utf-8')
                msg.attach(text_part)
            
            html_part = MIMEText(body_html, 'html', 'utf-8')
            msg.attach(html_part)
            
            # Send email
            if not self.smtp_server:
                raise Exception("SMTP Server غير محدد. يرجى إعداد SMTP من لوحة الإدمن.")

            if not self.smtp_username:
                raise Exception("SMTP Username غير محدد. يرجى إعداد البريد الإلكتروني من لوحة الإدمن.")

            if not self.smtp_password:
                raise Exception("SMTP Password غير محدد. يرجى إعداد كلمة مرور التطبيق من لوحة الإدمن.")

            if not self.default_sender:
                raise Exception("Default Sender غير محدد. يرجى إعداد البريد المرسل من لوحة الإدمن.")

            print(f"🔄 محاولة الاتصال بـ SMTP: {self.smtp_server}:{self.smtp_port}")
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)

            if self.use_tls:
                print("🔐 تفعيل TLS...")
                server.starttls()

            print(f"🔑 تسجيل الدخول باستخدام: {self.smtp_username}")
            server.login(self.smtp_username, self.smtp_password)

            print("📤 إرسال الرسالة...")
            server.send_message(msg)
            server.quit()
            print("✅ تم إرسال البريد بنجاح")
            
            # Update log
            email_log.status = 'sent'
            email_log.sent_at = datetime.utcnow()
            db.session.commit()
            
            return True, "تم إرسال البريد بنجاح"
            
        except Exception as e:
            # Update log with error
            if 'email_log' in locals():
                email_log.status = 'failed'
                email_log.error_message = str(e)
                db.session.commit()
            
            return False, f"فشل إرسال البريد: {str(e)}"
    
    def send_template_email(self, to_email, template_name, variables=None):
        """Send email using template"""
        template = EmailTemplate.query.filter_by(name=template_name, is_active=True).first()
        if not template:
            return False, f"القالب '{template_name}' غير موجود"
        
        variables = variables or {}
        
        # Render template with variables
        try:
            subject = render_template_string(template.subject, **variables)
            body_html = render_template_string(template.body_html, **variables)
            body_text = render_template_string(template.body_text, **variables) if template.body_text else None
            
            return self.send_email(to_email, subject, body_html, body_text, template_name)
        except Exception as e:
            return False, f"خطأ في معالجة القالب: {str(e)}"
    
    def send_test_email(self, to_email):
        """Send test email"""
        subject = "رسالة تجريبية من أكاديمية القرآن الكريم"
        body_html = """
        <html>
        <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c3e50;">مرحباً!</h2>
                <p>هذه رسالة تجريبية للتأكد من صحة إعدادات البريد الإلكتروني.</p>
                <p>إذا وصلتك هذه الرسالة، فهذا يعني أن إعدادات SMTP تعمل بشكل صحيح.</p>
                <hr style="border: 1px solid #eee; margin: 20px 0;">
                <p style="color: #7f8c8d; font-size: 12px;">
                    تم إرسال هذه الرسالة من نظام إدارة أكاديمية القرآن الكريم<br>
                    التاريخ: {date}
                </p>
            </div>
        </body>
        </html>
        """.format(date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        body_text = """
        مرحباً!
        
        هذه رسالة تجريبية للتأكد من صحة إعدادات البريد الإلكتروني.
        إذا وصلتك هذه الرسالة، فهذا يعني أن إعدادات SMTP تعمل بشكل صحيح.
        
        تم إرسال هذه الرسالة من نظام إدارة أكاديمية القرآن الكريم
        التاريخ: {date}
        """.format(date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        return self.send_email(to_email, subject, body_html, body_text, 'test_email')


def get_user_management_templates():
    """Get user management email templates"""
    return [
        {
            'name': 'user_suspended',
            'display_name': 'إشعار حظر مؤقت للمستخدم',
            'subject': 'تم حظر حسابك مؤقتاً - {{ academy_name }}',
            'template_type': 'user_management',
            'is_system': True,
            'variables': ['user_name', 'suspension_reason', 'suspension_end_date', 'academy_name', 'academy_email', 'academy_phone', 'academy_whatsapp', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h2 style="color: #dc3545; margin: 0;">تم حظر حسابك مؤقتاً</h2>
                        </div>

                        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #856404;"><strong>تنبيه:</strong> تم حظر حسابك مؤقتاً من قبل إدارة {{ academy_name }}</p>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>نود إعلامك بأنه تم حظر حسابك مؤقتاً لدى {{ academy_name }}.</p>

                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h4 style="color: #495057; margin-top: 0;">تفاصيل الحظر:</h4>
                            <p><strong>السبب:</strong> {{ suspension_reason or "غير محدد" }}</p>
                            {% if suspension_end_date %}
                            <p><strong>تاريخ انتهاء الحظر:</strong> {{ suspension_end_date }}</p>
                            {% endif %}
                        </div>

                        <p>إذا كنت تعتقد أن هذا الإجراء خطأ أو تريد الاستئناف، يرجى التواصل معنا:</p>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;"><strong>الهاتف:</strong> {{ academy_phone }}</p>{% endif %}
                            {% if academy_whatsapp %}<p style="margin: 5px 0;"><strong>واتساب:</strong> {{ academy_whatsapp }}</p>{% endif %}
                        </div>

                        <p>نعتذر عن أي إزعاج قد يسببه هذا الإجراء.</p>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تم حظر حسابك مؤقتاً - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            نود إعلامك بأنه تم حظر حسابك مؤقتاً لدى {{ academy_name }}.

            تفاصيل الحظر:
            السبب: {{ suspension_reason or "غير محدد" }}
            {% if suspension_end_date %}تاريخ انتهاء الحظر: {{ suspension_end_date }}{% endif %}

            للتواصل معنا:
            البريد الإلكتروني: {{ academy_email }}
            {% if academy_phone %}الهاتف: {{ academy_phone }}{% endif %}
            {% if academy_whatsapp %}واتساب: {{ academy_whatsapp }}{% endif %}

            {{ academy_name }}
            '''
        },
        {
            'name': 'user_banned',
            'display_name': 'إشعار حظر نهائي للمستخدم',
            'subject': 'تم حظر حسابك نهائياً - {{ academy_name }}',
            'template_type': 'user_management',
            'is_system': True,
            'variables': ['user_name', 'ban_reason', 'academy_name', 'academy_email', 'academy_phone', 'academy_whatsapp', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h2 style="color: #dc3545; margin: 0;">تم حظر حسابك نهائياً</h2>
                        </div>

                        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #721c24;"><strong>تنبيه مهم:</strong> تم حظر حسابك نهائياً من قبل إدارة {{ academy_name }}</p>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>نود إعلامك بأنه تم حظر حسابك نهائياً لدى {{ academy_name }}.</p>

                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h4 style="color: #495057; margin-top: 0;">سبب الحظر:</h4>
                            <p>{{ ban_reason or "غير محدد" }}</p>
                        </div>

                        <p>هذا الإجراء نهائي ولن تتمكن من الوصول إلى حسابك أو الخدمات المرتبطة به.</p>

                        <p>إذا كنت تعتقد أن هذا الإجراء خطأ، يمكنك التواصل معنا للاستئناف:</p>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;"><strong>الهاتف:</strong> {{ academy_phone }}</p>{% endif %}
                            {% if academy_whatsapp %}<p style="margin: 5px 0;"><strong>واتساب:</strong> {{ academy_whatsapp }}</p>{% endif %}
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تم حظر حسابك نهائياً - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            نود إعلامك بأنه تم حظر حسابك نهائياً لدى {{ academy_name }}.

            سبب الحظر: {{ ban_reason or "غير محدد" }}

            هذا الإجراء نهائي ولن تتمكن من الوصول إلى حسابك أو الخدمات المرتبطة به.

            للتواصل معنا:
            البريد الإلكتروني: {{ academy_email }}
            {% if academy_phone %}الهاتف: {{ academy_phone }}{% endif %}
            {% if academy_whatsapp %}واتساب: {{ academy_whatsapp }}{% endif %}

            {{ academy_name }}
            '''
        },
        {
            'name': 'user_account_disabled',
            'display_name': 'إشعار تعطيل الحساب',
            'subject': 'تم تعطيل حسابك - {{ academy_name }}',
            'template_type': 'user_management',
            'is_system': True,
            'variables': ['user_name', 'disable_reason', 'academy_name', 'academy_email', 'academy_phone', 'academy_whatsapp', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h2 style="color: #ffc107; margin: 0;">تم تعطيل حسابك</h2>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>نود إعلامك بأنه تم تعطيل حسابك لدى {{ academy_name }}.</p>

                        {% if disable_reason %}
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h4 style="color: #495057; margin-top: 0;">سبب التعطيل:</h4>
                            <p>{{ disable_reason }}</p>
                        </div>
                        {% endif %}

                        <p>حسابك معطل مؤقتاً ويمكن إعادة تفعيله من قبل الإدارة.</p>

                        <p>للاستفسار أو طلب إعادة التفعيل، يرجى التواصل معنا:</p>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;"><strong>الهاتف:</strong> {{ academy_phone }}</p>{% endif %}
                            {% if academy_whatsapp %}<p style="margin: 5px 0;"><strong>واتساب:</strong> {{ academy_whatsapp }}</p>{% endif %}
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تم تعطيل حسابك - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            نود إعلامك بأنه تم تعطيل حسابك لدى {{ academy_name }}.

            {% if disable_reason %}سبب التعطيل: {{ disable_reason }}{% endif %}

            حسابك معطل مؤقتاً ويمكن إعادة تفعيله من قبل الإدارة.

            للتواصل معنا:
            البريد الإلكتروني: {{ academy_email }}
            {% if academy_phone %}الهاتف: {{ academy_phone }}{% endif %}
            {% if academy_whatsapp %}واتساب: {{ academy_whatsapp }}{% endif %}

            {{ academy_name }}
            '''
        },
        {
            'name': 'user_account_deleted',
            'display_name': 'إشعار حذف الحساب',
            'subject': 'تم حذف حسابك - {{ academy_name }}',
            'template_type': 'user_management',
            'is_system': True,
            'variables': ['user_name', 'delete_reason', 'can_be_restored', 'academy_name', 'academy_email', 'academy_phone', 'academy_whatsapp', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h2 style="color: #dc3545; margin: 0;">تم حذف حسابك</h2>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>نود إعلامك بأنه تم حذف حسابك لدى {{ academy_name }}.</p>

                        {% if delete_reason %}
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h4 style="color: #495057; margin-top: 0;">سبب الحذف:</h4>
                            <p>{{ delete_reason }}</p>
                        </div>
                        {% endif %}

                        {% if can_be_restored %}
                        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #0c5460;"><strong>ملاحظة:</strong> يمكن استرداد حسابك خلال فترة محددة. يرجى التواصل معنا إذا كنت تريد استرداد حسابك.</p>
                        </div>
                        {% else %}
                        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #721c24;"><strong>تنبيه:</strong> تم حذف حسابك نهائياً ولا يمكن استرداده.</p>
                        </div>
                        {% endif %}

                        <p>للاستفسار أو المساعدة، يرجى التواصل معنا:</p>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;"><strong>الهاتف:</strong> {{ academy_phone }}</p>{% endif %}
                            {% if academy_whatsapp %}<p style="margin: 5px 0;"><strong>واتساب:</strong> {{ academy_whatsapp }}</p>{% endif %}
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تم حذف حسابك - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            نود إعلامك بأنه تم حذف حسابك لدى {{ academy_name }}.

            {% if delete_reason %}سبب الحذف: {{ delete_reason }}{% endif %}

            {% if can_be_restored %}
            ملاحظة: يمكن استرداد حسابك خلال فترة محددة. يرجى التواصل معنا إذا كنت تريد استرداد حسابك.
            {% else %}
            تنبيه: تم حذف حسابك نهائياً ولا يمكن استرداده.
            {% endif %}

            للتواصل معنا:
            البريد الإلكتروني: {{ academy_email }}
            {% if academy_phone %}الهاتف: {{ academy_phone }}{% endif %}
            {% if academy_whatsapp %}واتساب: {{ academy_whatsapp }}{% endif %}

            {{ academy_name }}
            '''
        },
        {
            'name': 'subscription_purchased',
            'display_name': 'إشعار شراء الباقة',
            'subject': 'تم شراء الباقة بنجاح - {{ academy_name }}',
            'template_type': 'subscription',
            'is_system': True,
            'variables': ['user_name', 'package_name', 'package_price', 'payment_method', 'transaction_id', 'purchase_date', 'sessions_count', 'duration_days', 'academy_name', 'academy_email', 'academy_phone', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h1 style="color: #28a745; margin: 0;">🎉 تم شراء الباقة بنجاح!</h1>
                        </div>

                        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #155724;"><strong>✅ تم الدفع بنجاح!</strong> شكراً لك على ثقتك في {{ academy_name }}</p>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>نشكرك على شراء الباقة من {{ academy_name }}. تم استلام دفعتك بنجاح وسيتم مراجعتها من قبل الإدارة.</p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="color: #495057; margin-top: 0;">📋 تفاصيل الشراء:</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>الباقة:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">{{ package_name }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>المبلغ:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">{{ package_price }} ريال</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>وسيلة الدفع:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">{{ payment_method }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>رقم المعاملة:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">{{ transaction_id }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>تاريخ الشراء:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">{{ purchase_date }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>عدد الحصص:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">{{ sessions_count }} حصة</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0;"><strong>مدة الباقة:</strong></td>
                                    <td style="padding: 8px 0;">{{ duration_days }} يوم</td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
                            <h4 style="color: #856404; margin-top: 0;">⏳ الخطوات التالية:</h4>
                            <ul style="margin: 0; padding-right: 20px;">
                                <li>سيتم مراجعة دفعتك من قبل الإدارة</li>
                                <li>ستصلك رسالة تأكيد فور الموافقة على الدفع</li>
                                <li>سيتم تفعيل اشتراكك تلقائياً بعد الموافقة</li>
                                <li>يمكنك متابعة حالة اشتراكك من حسابك</li>
                            </ul>
                        </div>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>للتواصل معنا:</strong></p>
                            <p style="margin: 5px 0;">📧 {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;">📞 {{ academy_phone }}</p>{% endif %}
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                            <p style="color: #6c757d; font-size: 12px;">شكراً لثقتكم بنا</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تم شراء الباقة بنجاح - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            نشكرك على شراء الباقة من {{ academy_name }}. تم استلام دفعتك بنجاح وسيتم مراجعتها من قبل الإدارة.

            تفاصيل الشراء:
            الباقة: {{ package_name }}
            المبلغ: {{ package_price }} ريال
            وسيلة الدفع: {{ payment_method }}
            رقم المعاملة: {{ transaction_id }}
            تاريخ الشراء: {{ purchase_date }}
            عدد الحصص: {{ sessions_count }} حصة
            مدة الباقة: {{ duration_days }} يوم

            الخطوات التالية:
            - سيتم مراجعة دفعتك من قبل الإدارة
            - ستصلك رسالة تأكيد فور الموافقة على الدفع
            - سيتم تفعيل اشتراكك تلقائياً بعد الموافقة
            - يمكنك متابعة حالة اشتراكك من حسابك

            للتواصل معنا:
            📧 {{ academy_email }}
            {% if academy_phone %}📞 {{ academy_phone }}{% endif %}

            {{ academy_name }}
            شكراً لثقتكم بنا
            '''
        },
        {
            'name': 'subscription_expiring',
            'display_name': 'تذكير انتهاء الاشتراك',
            'subject': 'تذكير: اشتراكك على وشك الانتهاء - {{ academy_name }}',
            'template_type': 'subscription',
            'is_system': True,
            'variables': ['user_name', 'package_name', 'days_remaining', 'sessions_remaining', 'end_date', 'renewal_url', 'academy_name', 'academy_email', 'academy_phone', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h1 style="color: #ffc107; margin: 0;">⏰ تذكير انتهاء الاشتراك</h1>
                        </div>

                        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #856404;"><strong>⚠️ تنبيه:</strong> اشتراكك في {{ package_name }} على وشك الانتهاء</p>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>نود تذكيرك بأن اشتراكك في باقة "{{ package_name }}" على وشك الانتهاء.</p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="color: #495057; margin-top: 0;">📊 تفاصيل الاشتراك:</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>الباقة:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">{{ package_name }}</td>
                                </tr>
                                {% if days_remaining %}
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>الأيام المتبقية:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #dc3545;">{{ days_remaining }} يوم</td>
                                </tr>
                                {% endif %}
                                {% if sessions_remaining %}
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;"><strong>الحصص المتبقية:</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #dc3545;">{{ sessions_remaining }} حصة</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td style="padding: 8px 0;"><strong>تاريخ الانتهاء:</strong></td>
                                    <td style="padding: 8px 0; color: #dc3545;">{{ end_date }}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                            <h4 style="color: #0c5460; margin-top: 0;">🔄 لتجديد اشتراكك:</h4>
                            <p style="margin: 10px 0;">لا تفوت فرصة مواصلة رحلتك التعليمية معنا!</p>
                            <div style="text-align: center; margin: 15px 0;">
                                <a href="{{ renewal_url }}" style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; display: inline-block;">
                                    🛒 تجديد الاشتراك الآن
                                </a>
                            </div>
                        </div>

                        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                            <h4 style="color: #721c24; margin-top: 0;">⚠️ مهم:</h4>
                            <ul style="margin: 0; padding-right: 20px;">
                                <li>بعد انتهاء الاشتراك، لن تتمكن من حجز حصص جديدة</li>
                                <li>ستفقد الوصول إلى المواد التعليمية الخاصة</li>
                                <li>يمكنك التجديد في أي وقت لاستعادة جميع المميزات</li>
                            </ul>
                        </div>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>للتواصل معنا:</strong></p>
                            <p style="margin: 5px 0;">📧 {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;">📞 {{ academy_phone }}</p>{% endif %}
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                            <p style="color: #6c757d; font-size: 12px;">نتطلع لمواصلة رحلتك التعليمية معنا</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تذكير: اشتراكك على وشك الانتهاء - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            نود تذكيرك بأن اشتراكك في باقة "{{ package_name }}" على وشك الانتهاء.

            تفاصيل الاشتراك:
            الباقة: {{ package_name }}
            {% if days_remaining %}الأيام المتبقية: {{ days_remaining }} يوم{% endif %}
            {% if sessions_remaining %}الحصص المتبقية: {{ sessions_remaining }} حصة{% endif %}
            تاريخ الانتهاء: {{ end_date }}

            لتجديد اشتراكك:
            لا تفوت فرصة مواصلة رحلتك التعليمية معنا!
            يمكنك التجديد من خلال: {{ renewal_url }}

            مهم:
            - بعد انتهاء الاشتراك، لن تتمكن من حجز حصص جديدة
            - ستفقد الوصول إلى المواد التعليمية الخاصة
            - يمكنك التجديد في أي وقت لاستعادة جميع المميزات

            للتواصل معنا:
            📧 {{ academy_email }}
            {% if academy_phone %}📞 {{ academy_phone }}{% endif %}

            {{ academy_name }}
            نتطلع لمواصلة رحلتك التعليمية معنا
            '''
        },
        {
            'name': 'password_reset',
            'display_name': 'استعادة كلمة المرور',
            'subject': 'طلب استعادة كلمة المرور - {{ academy_name }}',
            'template_type': 'auth',
            'is_system': True,
            'variables': ['user_name', 'reset_url', 'reset_token', 'expiry_time', 'academy_name', 'academy_email', 'academy_phone', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h1 style="color: #007bff; margin: 0;">🔐 استعادة كلمة المرور</h1>
                        </div>

                        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #0c5460;"><strong>🔒 طلب استعادة كلمة المرور</strong> تم استلام طلبك بنجاح</p>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>تلقينا طلباً لاستعادة كلمة المرور الخاصة بحسابك في {{ academy_name }}.</p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="color: #495057; margin-top: 0;">🔑 لإعادة تعيين كلمة المرور:</h3>
                            <p>اضغط على الزر أدناه لإعادة تعيين كلمة المرور:</p>
                            <div style="text-align: center; margin: 20px 0;">
                                <a href="{{ reset_url }}" style="background-color: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                                    🔐 إعادة تعيين كلمة المرور
                                </a>
                            </div>
                            <p style="font-size: 14px; color: #6c757d;">أو انسخ الرابط التالي في متصفحك:</p>
                            <p style="word-break: break-all; background-color: #e9ecef; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px;">{{ reset_url }}</p>
                        </div>

                        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
                            <h4 style="color: #856404; margin-top: 0;">⏰ مهم:</h4>
                            <ul style="margin: 0; padding-right: 20px;">
                                <li>هذا الرابط صالح لمدة {{ expiry_time }} فقط</li>
                                <li>يمكن استخدام الرابط مرة واحدة فقط</li>
                                <li>إذا لم تطلب استعادة كلمة المرور، تجاهل هذه الرسالة</li>
                            </ul>
                        </div>

                        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                            <h4 style="color: #721c24; margin-top: 0;">🛡️ نصائح الأمان:</h4>
                            <ul style="margin: 0; padding-right: 20px;">
                                <li>لا تشارك رابط استعادة كلمة المرور مع أي شخص</li>
                                <li>اختر كلمة مرور قوية تحتوي على أحرف وأرقام ورموز</li>
                                <li>لا تستخدم نفس كلمة المرور في مواقع أخرى</li>
                            </ul>
                        </div>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>للتواصل معنا:</strong></p>
                            <p style="margin: 5px 0;">📧 {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;">📞 {{ academy_phone }}</p>{% endif %}
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                            <p style="color: #6c757d; font-size: 12px;">رسالة تلقائية - لا ترد على هذا البريد</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            استعادة كلمة المرور - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            تلقينا طلباً لاستعادة كلمة المرور الخاصة بحسابك في {{ academy_name }}.

            لإعادة تعيين كلمة المرور:
            اضغط على الرابط التالي أو انسخه في متصفحك:
            {{ reset_url }}

            مهم:
            - هذا الرابط صالح لمدة {{ expiry_time }} فقط
            - يمكن استخدام الرابط مرة واحدة فقط
            - إذا لم تطلب استعادة كلمة المرور، تجاهل هذه الرسالة

            نصائح الأمان:
            - لا تشارك رابط استعادة كلمة المرور مع أي شخص
            - اختر كلمة مرور قوية تحتوي على أحرف وأرقام ورموز
            - لا تستخدم نفس كلمة المرور في مواقع أخرى

            للتواصل معنا:
            📧 {{ academy_email }}
            {% if academy_phone %}📞 {{ academy_phone }}{% endif %}

            {{ academy_name }}
            رسالة تلقائية - لا ترد على هذا البريد
            '''
        },
        {
            'name': 'user_account_restored',
            'display_name': 'إشعار استرداد الحساب',
            'subject': 'تم استرداد حسابك بنجاح - {{ academy_name }}',
            'template_type': 'user_management',
            'is_system': True,
            'variables': ['user_name', 'academy_name', 'academy_email', 'academy_phone', 'academy_whatsapp', 'academy_logo', 'login_url'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h2 style="color: #28a745; margin: 0;">تم استرداد حسابك بنجاح</h2>
                        </div>

                        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #155724;"><strong>مبروك!</strong> تم استرداد حسابك بنجاح وأصبح نشطاً مرة أخرى.</p>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name }},</p>

                        <p>نحن سعداء لإعلامك بأنه تم استرداد حسابك لدى {{ academy_name }} بنجاح.</p>

                        <p>يمكنك الآن تسجيل الدخول والوصول إلى جميع الخدمات المتاحة.</p>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{ login_url }}" style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">تسجيل الدخول الآن</a>
                        </div>

                        <p>إذا واجهت أي مشاكل في تسجيل الدخول، يرجى التواصل معنا:</p>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;"><strong>الهاتف:</strong> {{ academy_phone }}</p>{% endif %}
                            {% if academy_whatsapp %}<p style="margin: 5px 0;"><strong>واتساب:</strong> {{ academy_whatsapp }}</p>{% endif %}
                        </div>

                        <p>نرحب بعودتك ونتطلع لخدمتك.</p>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تم استرداد حسابك بنجاح - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }},

            نحن سعداء لإعلامك بأنه تم استرداد حسابك لدى {{ academy_name }} بنجاح.

            يمكنك الآن تسجيل الدخول والوصول إلى جميع الخدمات المتاحة.

            رابط تسجيل الدخول: {{ login_url }}

            للتواصل معنا:
            البريد الإلكتروني: {{ academy_email }}
            {% if academy_phone %}الهاتف: {{ academy_phone }}{% endif %}
            {% if academy_whatsapp %}واتساب: {{ academy_whatsapp }}{% endif %}

            نرحب بعودتك ونتطلع لخدمتك.

            {{ academy_name }}
            '''
        }
    ]


def get_default_templates():
    """Get default email templates for system initialization"""
    return [
        {
            'name': 'welcome_email',
            'display_name': 'رسالة الترحيب',
            'subject': 'مرحباً بك في أكاديمية القرآن الكريم',
            'template_type': 'welcome',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone', 'academy_whatsapp', 'academy_address', 'academy_website', 'academy_logo', 'academy_facebook', 'academy_twitter', 'academy_instagram', 'academy_youtube', 'login_url'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h1 style="color: #2c3e50; text-align: center; margin-bottom: 30px;">
                            مرحباً بك في {{ academy_name }}
                        </h1>
                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نرحب بك في أكاديمية القرآن الكريم! نحن سعداء لانضمامك إلى عائلتنا الكريمة.
                        </p>
                        <div style="background-color: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0;">
                            <h3 style="color: #27ae60; margin-top: 0;">معلومات حسابك:</h3>
                            <p><strong>البريد الإلكتروني:</strong> {{ user_email }}</p>
                            <p><strong>تاريخ التسجيل:</strong> {{ current_date }}</p>
                        </div>
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{ login_url }}" style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                                دخول إلى حسابي
                            </a>
                        </div>
                        <p style="font-size: 14px; color: #7f8c8d; text-align: center;">
                            إذا كان لديك أي استفسار، لا تتردد في التواصل معنا
                        </p>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            مرحباً {{ user_name }}،
            
            نرحب بك في أكاديمية القرآن الكريم! نحن سعداء لانضمامك إلى عائلتنا الكريمة.
            
            معلومات حسابك:
            البريد الإلكتروني: {{ user_email }}
            تاريخ التسجيل: {{ current_date }}
            
            يمكنك الدخول إلى حسابك من خلال الرابط: {{ login_url }}
            
            إذا كان لديك أي استفسار، لا تتردد في التواصل معنا.
            
            مع أطيب التحيات،
            فريق {{ academy_name }}
            '''
        },
        {
            'name': 'test_email',
            'display_name': 'رسالة اختبار',
            'subject': 'رسالة اختبار من {{ academy_name }}',
            'template_type': 'test',
            'is_system': True,
            'variables': ['user_name', 'test_message', 'academy_name', 'academy_email', 'academy_phone', 'academy_logo', 'current_date'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h1 style="color: #007bff; margin: 0;">🧪 رسالة اختبار</h1>
                        </div>

                        <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                            <p style="margin: 0; color: #0c5460;"><strong>✅ نجح الاختبار!</strong> تم إرسال هذه الرسالة بنجاح من نظام {{ academy_name }}</p>
                        </div>

                        <p>عزيزي/عزيزتي {{ user_name or "المستخدم" }},</p>

                        <p>هذه رسالة اختبار للتأكد من أن نظام البريد الإلكتروني يعمل بشكل صحيح.</p>

                        {% if test_message %}
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h4 style="color: #495057; margin-top: 0;">رسالة الاختبار:</h4>
                            <p>{{ test_message }}</p>
                        </div>
                        {% endif %}

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h4 style="color: #495057; margin-top: 0;">معلومات الاختبار:</h4>
                            <p style="margin: 5px 0;"><strong>التاريخ والوقت:</strong> {{ current_date }}</p>
                            <p style="margin: 5px 0;"><strong>النظام:</strong> نظام إدارة أكاديمية القرآن الكريم</p>
                            <p style="margin: 5px 0;"><strong>الحالة:</strong> ✅ يعمل بشكل طبيعي</p>
                        </div>

                        <p>إذا تلقيت هذه الرسالة، فهذا يعني أن إعدادات البريد الإلكتروني صحيحة ويمكن إرسال الإشعارات بنجاح.</p>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p style="margin: 5px 0;"><strong>للتواصل معنا:</strong></p>
                            <p style="margin: 5px 0;">📧 {{ academy_email }}</p>
                            {% if academy_phone %}<p style="margin: 5px 0;">📞 {{ academy_phone }}</p>{% endif %}
                        </div>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 14px;">{{ academy_name }}</p>
                            <p style="color: #6c757d; font-size: 12px;">رسالة اختبار تلقائية</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            رسالة اختبار من {{ academy_name }}

            عزيزي/عزيزتي {{ user_name or "المستخدم" }},

            هذه رسالة اختبار للتأكد من أن نظام البريد الإلكتروني يعمل بشكل صحيح.

            {% if test_message %}
            رسالة الاختبار: {{ test_message }}
            {% endif %}

            معلومات الاختبار:
            التاريخ والوقت: {{ current_date }}
            النظام: نظام إدارة أكاديمية القرآن الكريم
            الحالة: ✅ يعمل بشكل طبيعي

            إذا تلقيت هذه الرسالة، فهذا يعني أن إعدادات البريد الإلكتروني صحيحة ويمكن إرسال الإشعارات بنجاح.

            للتواصل معنا:
            📧 {{ academy_email }}
            {% if academy_phone %}📞 {{ academy_phone }}{% endif %}

            {{ academy_name }}
            رسالة اختبار تلقائية
            '''
        },
        {
            'name': 'session_created',
            'display_name': 'إشعار إنشاء حصة جديدة',
            'subject': 'تم جدولة حصة جديدة - {{ academy_name }}',
            'template_type': 'notification',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #28a745; margin-bottom: 10px;">✅ تم جدولة حصة جديدة</h1>
                            <p style="color: #6c757d; margin: 0;">{{ academy_slogan }}</p>
                        </div>

                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نود إعلامك بأنه تم جدولة حصة جديدة لك بنجاح.
                        </p>

                        <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #28a745;">
                            <h3 style="color: #155724; margin-top: 0; margin-bottom: 15px;">📅 تفاصيل الحصة:</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">التاريخ:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_date }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">الوقت:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_time }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">المدة:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_duration }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">المعلم:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ teacher_name }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">الطالب:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ student_name }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">نوع الحصة:</td>
                                    <td style="padding: 8px 0; color: #212529;">{{ session_type }}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{ session_url }}" style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                                🔗 عرض تفاصيل الحصة
                            </a>
                        </div>

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-right: 4px solid #ffc107;">
                            <p style="margin: 0; color: #856404;">
                                <strong>💡 تذكير:</strong> ستصلك تذكيرات إضافية قبل موعد الحصة بيوم واحد وقبل 5 دقائق من بدايتها.
                            </p>
                        </div>

                        <hr style="border: 1px solid #dee2e6; margin: 30px 0;">

                        <div style="text-align: center; color: #6c757d; font-size: 14px;">
                            <p style="margin: 5px 0;"><strong>{{ academy_name }}</strong></p>
                            <p style="margin: 5px 0;">📧 {{ academy_email }} | 📞 {{ academy_phone }}</p>
                            <p style="margin: 5px 0; font-style: italic;">{{ academy_slogan }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            نود إعلامك بأنه تم جدولة حصة جديدة لك بنجاح.

            تفاصيل الحصة:
            التاريخ: {{ session_date }}
            الوقت: {{ session_time }}
            المدة: {{ session_duration }}
            المعلم: {{ teacher_name }}
            الطالب: {{ student_name }}
            نوع الحصة: {{ session_type }}

            يمكنك عرض تفاصيل الحصة من خلال: {{ session_url }}

            تذكير: ستصلك تذكيرات إضافية قبل موعد الحصة بيوم واحد وقبل 5 دقائق من بدايتها.

            مع أطيب التحيات،
            {{ academy_name }}
            {{ academy_email }} | {{ academy_phone }}
            '''
        },
        {
            'name': 'session_reminder',
            'display_name': 'تذكير بالحصة',
            'subject': 'تذكير: حصتك القادمة في {{ session_date }}',
            'template_type': 'reminder',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone', 'academy_whatsapp', 'academy_address', 'academy_website', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px;">
                        <h2 style="color: #856404;">تذكير بحصتك القادمة</h2>
                        <p>عزيزي/عزيزتي {{ user_name }}،</p>
                        <p>نذكرك بأن لديك حصة قادمة:</p>
                        <ul>
                            <li><strong>التاريخ:</strong> {{ session_date }}</li>
                            <li><strong>الوقت:</strong> {{ session_time }}</li>
                            <li><strong>المعلم:</strong> {{ teacher_name }}</li>
                        </ul>
                        <div style="text-align: center; margin: 20px 0;">
                            <a href="{{ session_url }}" style="background-color: #f39c12; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                                الانضمام للحصة
                            </a>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،
            
            نذكرك بأن لديك حصة قادمة:
            
            التاريخ: {{ session_date }}
            الوقت: {{ session_time }}
            المعلم: {{ teacher_name }}
            
            يمكنك الانضمام للحصة من خلال: {{ session_url }}
            '''
        },
        {
            'name': 'subscription_session_created',
            'display_name': 'إشعار حصة الاشتراك',
            'subject': 'تم جدولة حصة جديدة من اشتراكك - {{ academy_name }}',
            'template_type': 'session',
            'is_system': True,
            'variables': ['student_name', 'teacher_name', 'session_date', 'session_time', 'session_duration', 'package_name', 'sessions_remaining', 'meeting_link', 'recipient_name', 'recipient_type', 'academy_name', 'academy_email', 'academy_phone', 'academy_logo'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            {% if academy_logo %}
                            <img src="{{ academy_logo }}" alt="{{ academy_name }}" style="max-height: 80px; margin-bottom: 20px;">
                            {% endif %}
                            <h2 style="color: #28a745; margin: 0;">تم جدولة حصة جديدة من اشتراكك</h2>
                        </div>

                        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                            <h3 style="color: #155724; margin-top: 0;">عزيزي/عزيزتي {{ recipient_name }}</h3>
                            <p style="color: #155724; margin-bottom: 0;">
                                {% if recipient_type == 'student' %}
                                تم جدولة حصة جديدة لك من اشتراكك في باقة "{{ package_name }}"
                                {% else %}
                                تم تعيينك لتدريس حصة جديدة للطالب {{ student_name }} من اشتراكه في باقة "{{ package_name }}"
                                {% endif %}
                            </p>
                        </div>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                            <h4 style="color: #495057; margin-top: 0;">تفاصيل الحصة:</h4>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">التاريخ:</td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ session_date }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">الوقت:</td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ session_time }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">المدة:</td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ session_duration }} دقيقة</td>
                                </tr>
                                {% if recipient_type == 'student' %}
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">المعلم:</td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ teacher_name }}</td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">الطالب:</td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ student_name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; font-weight: bold; color: #495057;">الباقة:</td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6; color: #6c757d;">{{ package_name }}</td>
                                </tr>
                                {% if recipient_type == 'student' %}
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #495057;">الحصص المتبقية:</td>
                                    <td style="padding: 8px 0; color: #28a745; font-weight: bold;">{{ sessions_remaining }} حصة</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>

                        {% if meeting_link and meeting_link != 'سيتم إرساله قريباً' %}
                        <div style="text-align: center; margin: 25px 0;">
                            <a href="{{ meeting_link }}" style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                                دخول للحصة
                            </a>
                        </div>
                        {% else %}
                        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 25px 0;">
                            <p style="color: #856404; margin: 0; text-align: center;">
                                <i class="fas fa-info-circle"></i> سيتم إرسال رابط الحصة قريباً
                            </p>
                        </div>
                        {% endif %}

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; margin: 0;">للتواصل معنا:</p>
                            <p style="color: #6c757d; margin: 5px 0;">
                                📧 {{ academy_email }}
                                {% if academy_phone %} | 📞 {{ academy_phone }}{% endif %}
                            </p>
                            <p style="color: #6c757d; font-size: 14px; margin-top: 15px;">{{ academy_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            تم جدولة حصة جديدة من اشتراكك - {{ academy_name }}

            عزيزي/عزيزتي {{ recipient_name }},

            {% if recipient_type == 'student' %}
            تم جدولة حصة جديدة لك من اشتراكك في باقة "{{ package_name }}"
            {% else %}
            تم تعيينك لتدريس حصة جديدة للطالب {{ student_name }} من اشتراكه في باقة "{{ package_name }}"
            {% endif %}

            تفاصيل الحصة:
            التاريخ: {{ session_date }}
            الوقت: {{ session_time }}
            المدة: {{ session_duration }} دقيقة
            {% if recipient_type == 'student' %}
            المعلم: {{ teacher_name }}
            الحصص المتبقية: {{ sessions_remaining }} حصة
            {% else %}
            الطالب: {{ student_name }}
            {% endif %}
            الباقة: {{ package_name }}

            {% if meeting_link and meeting_link != 'سيتم إرساله قريباً' %}
            رابط الحصة: {{ meeting_link }}
            {% else %}
            سيتم إرسال رابط الحصة قريباً
            {% endif %}

            للتواصل معنا:
            📧 {{ academy_email }}
            {% if academy_phone %}📞 {{ academy_phone }}{% endif %}

            {{ academy_name }}
            '''
        }
    ]


# Email service instance will be created when needed
email_service = None
