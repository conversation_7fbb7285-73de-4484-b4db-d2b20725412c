{% extends "base.html" %}

{% block title %}إعدادات البريد الإلكتروني - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}إعدادات البريد الإلكتروني{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('admin.settings') }}">الإعدادات</a></li>
                <li class="breadcrumb-item active" aria-current="page">إعدادات البريد الإلكتروني</li>
            </ol>
        </nav>

        <!-- Back Button -->
        <div class="mb-3">
            <a href="{{ url_for('admin.settings') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
            </a>
        </div>

        <form method="POST" action="{{ url_for('admin.email_settings') }}" id="emailSettingsForm">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            
            <!-- SMTP Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>إعدادات خادم SMTP
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_server" class="form-label">خادم SMTP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="smtp_server" name="smtp_server" 
                                       value="{{ email_config.smtp_server or '' }}" 
                                       placeholder="smtp.gmail.com" required>
                                <div class="form-text">عنوان خادم البريد الصادر</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_port" class="form-label">منفذ SMTP <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                       value="{{ email_config.smtp_port or 587 }}" 
                                       placeholder="587" required>
                                <div class="form-text">المنفذ المستخدم (587 للـ TLS، 465 للـ SSL)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="smtp_username" name="smtp_username" 
                                       value="{{ email_config.smtp_username or '' }}" 
                                       placeholder="<EMAIL>" required>
                                <div class="form-text">البريد الإلكتروني للمرسل</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                       value="{{ email_config.smtp_password or '' }}" 
                                       placeholder="كلمة مرور التطبيق" required>
                                <div class="form-text">كلمة مرور التطبيق (ليس كلمة مرور الحساب)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_sender" class="form-label">المرسل الافتراضي</label>
                                <input type="email" class="form-control" id="default_sender" name="default_sender" 
                                       value="{{ email_config.default_sender or '' }}" 
                                       placeholder="<EMAIL>">
                                <div class="form-text">البريد الذي سيظهر كمرسل</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="use_tls" name="use_tls" 
                                           {% if email_config.use_tls %}checked{% endif %}>
                                    <label class="form-check-label" for="use_tls">
                                        استخدام TLS للتشفير
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Notifications -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>إعدادات الإشعارات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="welcome_email_enabled" name="welcome_email_enabled" 
                                       {% if email_config.welcome_email_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="welcome_email_enabled">
                                    <strong>رسالة الترحيب</strong>
                                    <div class="text-muted small">إرسال رسالة ترحيب للمستخدمين الجدد</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="session_reminder_enabled" name="session_reminder_enabled" 
                                       {% if email_config.session_reminder_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="session_reminder_enabled">
                                    <strong>تذكير الحصص</strong>
                                    <div class="text-muted small">إرسال تذكير قبل موعد الحصة</div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="payment_confirmation_enabled" name="payment_confirmation_enabled"
                                       {% if email_config.payment_confirmation_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="payment_confirmation_enabled">
                                    <strong>تأكيد الدفع</strong>
                                    <div class="text-muted small">إرسال تأكيد عند إتمام الدفع</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="subscription_approval_enabled" name="subscription_approval_enabled"
                                       {% if email_config.subscription_approval_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="subscription_approval_enabled">
                                    <strong>موافقة الاشتراك</strong>
                                    <div class="text-muted small">إرسال إشعار عند الموافقة على الاشتراك</div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="user_management_notifications_enabled" name="user_management_notifications_enabled"
                                       {% if email_config.user_management_notifications_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="user_management_notifications_enabled">
                                    <strong>إشعارات إدارة المستخدمين</strong>
                                    <div class="text-muted small">إرسال إشعارات الحظر والتعطيل والحذف</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="subscription_purchase_enabled" name="subscription_purchase_enabled"
                                       {% if email_config.subscription_purchase_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="subscription_purchase_enabled">
                                    <strong>🆕 إشعار شراء الباقة</strong>
                                    <div class="text-muted small">إرسال فاتورة مفصلة عند شراء الباقة</div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="subscription_expiry_enabled" name="subscription_expiry_enabled"
                                       {% if email_config.subscription_expiry_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="subscription_expiry_enabled">
                                    <strong>🆕 تذكير انتهاء الاشتراك</strong>
                                    <div class="text-muted small">تذكير قبل انتهاء المدة أو الحصص</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="password_reset_enabled" name="password_reset_enabled"
                                       {% if email_config.password_reset_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="password_reset_enabled">
                                    <strong>🆕 استعادة كلمة المرور</strong>
                                    <div class="text-muted small">إرسال رابط آمن لاستعادة كلمة المرور</div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Notifications Section -->
                    <hr class="my-4">
                    <h6 class="text-primary mb-3">
                        <i class="fas fa-user-shield me-2"></i>إشعارات الإدمن
                    </h6>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="admin_payment_notification_enabled" name="admin_payment_notification_enabled"
                                       {% if email_config.admin_payment_notification_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="admin_payment_notification_enabled">
                                    <strong>💰 إشعار الدفعات الجديدة</strong>
                                    <div class="text-muted small">إشعار الإدمن عند استلام دفعة جديدة تحتاج مراجعة</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="admin_new_user_notification_enabled" name="admin_new_user_notification_enabled"
                                       {% if email_config.admin_new_user_notification_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="admin_new_user_notification_enabled">
                                    <strong>👤 إشعار المستخدمين الجدد</strong>
                                    <div class="text-muted small">إشعار الإدمن عند تسجيل مستخدم جديد يحتاج موافقة</div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="admin_session_booking_notification" name="admin_session_booking_notification"
                                       {% if email_config.admin_session_booking_notification %}checked{% endif %}>
                                <label class="form-check-label" for="admin_session_booking_notification">
                                    <strong>📅 إشعار حجز الحصص</strong>
                                    <div class="text-muted small">إشعار الإدمن عند حجز حصة جديدة</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="admin_subscription_expiry_notification" name="admin_subscription_expiry_notification"
                                       {% if email_config.admin_subscription_expiry_notification %}checked{% endif %}>
                                <label class="form-check-label" for="admin_subscription_expiry_notification">
                                    <strong>⏰ إشعار انتهاء الاشتراكات</strong>
                                    <div class="text-muted small">إشعار الإدمن بالاشتراكات التي ستنتهي قريباً</div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Session Notifications Section -->
                    <hr class="my-4">
                    <h6 class="text-success mb-3">
                        <i class="fas fa-calendar-check me-2"></i>إشعارات الحصص
                    </h6>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="trial_session_notification_enabled" name="trial_session_notification_enabled"
                                       {% if email_config.trial_session_notification_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="trial_session_notification_enabled">
                                    <strong>🎯 إشعار الحصص التجريبية</strong>
                                    <div class="text-muted small">إشعار الطالب والمعلم عند إنشاء حصة تجريبية</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="makeup_session_notification_enabled" name="makeup_session_notification_enabled"
                                       {% if email_config.makeup_session_notification_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="makeup_session_notification_enabled">
                                    <strong>🔄 إشعار الحصص التعويضية</strong>
                                    <div class="text-muted small">إشعار الطالب والمعلم عند إنشاء حصة تعويضية</div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="session_deletion_notification_enabled" name="session_deletion_notification_enabled"
                                       {% if email_config.session_deletion_notification_enabled %}checked{% endif %}>
                                <label class="form-check-label" for="session_deletion_notification_enabled">
                                    <strong>🗑️ إشعار حذف الحصص</strong>
                                    <div class="text-muted small">إشعار الطالب والمعلم عند حذف حصة مجدولة</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- مساحة فارغة للتوسع المستقبلي -->
                        </div>
                    </div>
                </div>
            </div>



            <!-- Test Email -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-paper-plane me-2"></i>اختبار البريد الإلكتروني
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="email" class="form-control" id="test_email"
                                       placeholder="أدخل بريد إلكتروني للاختبار">
                                <button type="button" class="btn btn-outline-primary" onclick="sendTestEmail()">
                                    <i class="fas fa-paper-plane me-1"></i>إرسال رسالة تجريبية
                                </button>
                            </div>
                            <div class="form-text">سيتم إرسال رسالة تجريبية للتأكد من صحة الإعدادات</div>
                            <div id="test-email-result" class="mt-2"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Template Preview -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة القوالب
                    </h5>
                    <div class="d-flex gap-2">
                        <span class="badge bg-primary">{{ templates|length }} قالب</span>
                        {% set active_templates = templates|selectattr('is_active')|list %}
                        <span class="badge bg-success">{{ active_templates|length }} مفعل</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="preview_template" class="form-label">اختر قالب للمعاينة</label>
                            <select class="form-select" id="preview_template" onchange="previewTemplate()">
                                <option value="">اختر قالب...</option>
                                {% for template in templates %}
                                <option value="{{ template.id }}">{{ template.display_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">إجراءات سريعة</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="previewTemplate()" disabled id="preview-btn">
                                    <i class="fas fa-eye me-1"></i>معاينة
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="editTemplate()" disabled id="edit-btn">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="testTemplateEmail()" disabled id="test-template-btn">
                                    <i class="fas fa-paper-plane me-1"></i>اختبار
                                </button>
                            </div>
                            <div class="form-text">اختر قالباً لتفعيل الأزرار</div>
                        </div>
                    </div>
                    <div id="template-preview-area" style="display: none;">
                        <hr>
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <strong>معاينة سريعة:</strong>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="openFullPreview()">
                                    <i class="fas fa-external-link-alt me-1"></i>معاينة كاملة
                                </button>
                            </div>
                            <div id="preview-content">
                                <!-- Preview content will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Quick Access to System Templates -->
                    <div class="mt-3">
                        <hr>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted"><strong>وصول سريع للقوالب الأساسية:</strong></small>
                            <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list me-1"></i>عرض الكل
                            </a>
                        </div>
                        <div class="row g-2">
                            {% for template in templates[:4] %}
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-info btn-sm w-100"
                                        onclick="quickSelectTemplate({{ template.id }}, '{{ template.display_name }}')">
                                    <i class="fas fa-{{ 'home' if template.template_type == 'welcome' else 'bell' if template.template_type == 'reminder' else 'credit-card' if template.template_type == 'payment' else 'info-circle' }} me-1"></i>
                                    <small>{{ template.display_name }}</small>
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Templates Management -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>إدارة قوالب البريد الإلكتروني
                    </h5>
                    <div>
                        <a href="{{ url_for('admin.new_email_template') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-1"></i>قالب جديد
                        </a>
                        <a href="{{ url_for('admin.email_templates') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-list me-1"></i>عرض جميع القوالب
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for template in templates[:4] %}
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ template.display_name }}</h6>
                                        <small class="text-muted">{{ template.name }}</small>
                                        <div class="mt-2">
                                            {% if template.is_active %}
                                                <span class="badge bg-success">مفعل</span>
                                            {% else %}
                                                <span class="badge bg-secondary">معطل</span>
                                            {% endif %}
                                            {% if template.is_system %}
                                                <span class="badge bg-info">نظام</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('admin.preview_email_template', template_id=template.id) }}"
                                           class="btn btn-outline-info" title="معاينة">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('admin.edit_email_template', template_id=template.id) }}"
                                           class="btn btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% if templates|length > 4 %}
                    <div class="text-center">
                        <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-primary">
                            عرض جميع القوالب ({{ templates|length }})
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Reminder Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>إعدادات تذكيرات الحصص
                    </h5>
                </div>
                <div class="card-body">
                    <!-- تم دمج هذا النموذج في النموذج الرئيسي -->

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="reminder_enabled" name="reminder_enabled"
                                           {% if email_config and email_config.reminder_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="reminder_enabled">
                                        <strong>تفعيل التذكيرات</strong>
                                    </label>
                                </div>
                                <small class="text-muted">تفعيل نظام التذكيرات للحصص</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="reminder_auto_send" name="reminder_auto_send"
                                           {% if email_config and email_config.reminder_auto_send %}checked{% endif %}>
                                    <label class="form-check-label" for="reminder_auto_send">
                                        <strong>إرسال تلقائي</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال التذكيرات تلقائياً دون تدخل يدوي</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="reminder_hours_before" class="form-label">التذكير قبل (ساعات)</label>
                                <input type="number" class="form-control" id="reminder_hours_before" name="reminder_hours_before"
                                       value="{{ email_config.reminder_hours_before or 24 }}" min="1" max="168">
                                <small class="text-muted">عدد الساعات قبل موعد الحصة لإرسال التذكير</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="reminder_minutes_before" class="form-label">التذكير قبل (دقائق)</label>
                                <input type="number" class="form-control" id="reminder_minutes_before" name="reminder_minutes_before"
                                       value="{{ email_config.reminder_minutes_before or 30 }}" min="5" max="120">
                                <small class="text-muted">عدد الدقائق قبل موعد الحصة لإرسال تذكير عاجل</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="reminder_include_teacher_info" name="reminder_include_teacher_info"
                                           {% if email_config and email_config.reminder_include_teacher_info %}checked{% endif %}>
                                    <label class="form-check-label" for="reminder_include_teacher_info">
                                        <strong>تضمين معلومات المعلم</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إضافة معلومات المعلم في التذكير</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="reminder_include_session_notes" name="reminder_include_session_notes"
                                           {% if email_config and email_config.reminder_include_session_notes %}checked{% endif %}>
                                    <label class="form-check-label" for="reminder_include_session_notes">
                                        <strong>تضمين ملاحظات الحصة</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إضافة ملاحظات الحصة في التذكير</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="preferred_reminder_time" class="form-label">الوقت المفضل لإرسال التذكيرات اليومية</label>
                                <select class="form-select" id="preferred_reminder_time" name="preferred_reminder_time">
                                    <option value="6" {% if email_config.preferred_reminder_time == 6 %}selected{% endif %}>6:00 صباحاً</option>
                                    <option value="7" {% if email_config.preferred_reminder_time == 7 %}selected{% endif %}>7:00 صباحاً</option>
                                    <option value="8" {% if email_config.preferred_reminder_time == 8 %}selected{% endif %}>8:00 صباحاً</option>
                                    <option value="9" {% if email_config.preferred_reminder_time == 9 or not email_config.preferred_reminder_time %}selected{% endif %}>9:00 صباحاً</option>
                                    <option value="10" {% if email_config.preferred_reminder_time == 10 %}selected{% endif %}>10:00 صباحاً</option>
                                    <option value="11" {% if email_config.preferred_reminder_time == 11 %}selected{% endif %}>11:00 صباحاً</option>
                                    <option value="12" {% if email_config.preferred_reminder_time == 12 %}selected{% endif %}>12:00 ظهراً</option>
                                    <option value="13" {% if email_config.preferred_reminder_time == 13 %}selected{% endif %}>1:00 مساءً</option>
                                    <option value="14" {% if email_config.preferred_reminder_time == 14 %}selected{% endif %}>2:00 مساءً</option>
                                    <option value="15" {% if email_config.preferred_reminder_time == 15 %}selected{% endif %}>3:00 مساءً</option>
                                    <option value="16" {% if email_config.preferred_reminder_time == 16 %}selected{% endif %}>4:00 مساءً</option>
                                    <option value="17" {% if email_config.preferred_reminder_time == 17 %}selected{% endif %}>5:00 مساءً</option>
                                    <option value="18" {% if email_config.preferred_reminder_time == 18 %}selected{% endif %}>6:00 مساءً</option>
                                    <option value="19" {% if email_config.preferred_reminder_time == 19 %}selected{% endif %}>7:00 مساءً</option>
                                    <option value="20" {% if email_config.preferred_reminder_time == 20 %}selected{% endif %}>8:00 مساءً</option>
                                    <option value="21" {% if email_config.preferred_reminder_time == 21 %}selected{% endif %}>9:00 مساءً</option>
                                </select>
                                <small class="text-muted">الوقت المفضل لإرسال التذكيرات اليومية للحصص</small>
                            </div>
                            <div class="col-md-6">
                                <!-- مساحة فارغة -->
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>كيف تعمل التذكيرات:</h6>
                            <ul class="mb-0">
                                <li><strong>عند الإنشاء:</strong> يتم إرسال تذكير فوري للطالب والمعلم عند جدولة حصة جديدة</li>
                                <li><strong>قبل يوم:</strong> يتم فحص الحصص يومياً وإرسال تذكيرات للحصص المجدولة في اليوم التالي</li>
                                <li><strong>قبل 5 دقائق:</strong> يتم فحص الحصص كل دقيقة وإرسال تذكير عاجل قبل بداية الحصة</li>
                                <li><strong>منع التكرار:</strong> لا يتم إرسال نفس التذكير أكثر من مرة للحصة الواحدة</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-outline-info" onclick="testReminderSystem()">
                                <i class="fas fa-play me-1"></i>اختبار النظام
                            </button>
                        </div>
                    <!-- تم دمج الحفظ في النموذج الرئيسي -->
                </div>
            </div>

            <!-- User Management Notifications -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users-cog me-2"></i>إعدادات إشعارات إدارة المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <!-- تم دمج هذا النموذج في النموذج الرئيسي -->

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="new_user_registration_notification" name="new_user_registration_notification"
                                           {% if email_config and email_config.new_user_registration_notification %}checked{% endif %}>
                                    <label class="form-check-label" for="new_user_registration_notification">
                                        <strong>إشعار تسجيل مستخدم جديد</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إشعار عند تسجيل مستخدم جديد</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="user_profile_update_notification" name="user_profile_update_notification"
                                           {% if email_config and email_config.user_profile_update_notification %}checked{% endif %}>
                                    <label class="form-check-label" for="user_profile_update_notification">
                                        <strong>إشعار تحديث الملف الشخصي</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إشعار عند تحديث بيانات المستخدم</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="user_status_change_notification" name="user_status_change_notification"
                                           {% if email_config and email_config.user_status_change_notification %}checked{% endif %}>
                                    <label class="form-check-label" for="user_status_change_notification">
                                        <strong>إشعار تغيير حالة المستخدم</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إشعار عند تغيير حالة المستخدم (حظر، تفعيل، إلخ)</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="subscription_status_change_notification" name="subscription_status_change_notification"
                                           {% if email_config and email_config.subscription_status_change_notification %}checked{% endif %}>
                                    <label class="form-check-label" for="subscription_status_change_notification">
                                        <strong>إشعار تغيير حالة الاشتراك</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إشعار عند تغيير حالة الاشتراك</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="payment_received_notification" name="payment_received_notification"
                                           {% if email_config and email_config.payment_received_notification %}checked{% endif %}>
                                    <label class="form-check-label" for="payment_received_notification">
                                        <strong>إشعار استلام الدفعة</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إشعار عند استلام دفعة جديدة</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="session_completion_notification" name="session_completion_notification"
                                           {% if email_config and email_config.session_completion_notification %}checked{% endif %}>
                                    <label class="form-check-label" for="session_completion_notification">
                                        <strong>إشعار إكمال الحصة</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إشعار عند إكمال حصة</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="admin_daily_summary" name="admin_daily_summary"
                                           {% if email_config and email_config.admin_daily_summary %}checked{% endif %}>
                                    <label class="form-check-label" for="admin_daily_summary">
                                        <strong>ملخص يومي للإدمن</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال ملخص يومي بالأنشطة للإدمن</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="admin_weekly_report" name="admin_weekly_report"
                                           {% if email_config and email_config.admin_weekly_report %}checked{% endif %}>
                                    <label class="form-check-label" for="admin_weekly_report">
                                        <strong>تقرير أسبوعي للإدمن</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إيميل للمستخدم عند استرداد حسابه</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="activation_notifications" name="activation_notifications"
                                           {% if email_config and email_config.user_management_notifications_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="activation_notifications">
                                        <strong>إشعارات تفعيل الحساب</strong>
                                    </label>
                                </div>
                                <small class="text-muted">إرسال إيميل للمستخدم عند تفعيل حسابه بعد الحظر</small>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>قوالب إدارة المستخدمين المتاحة:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>user_suspended:</strong> إشعار الحظر المؤقت</li>
                                        <li><strong>user_banned:</strong> إشعار الحظر النهائي</li>
                                        <li><strong>user_account_disabled:</strong> إشعار تعطيل الحساب</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>user_account_deleted:</strong> إشعار حذف الحساب</li>
                                        <li><strong>user_account_restored:</strong> إشعار استرداد الحساب</li>
                                        <li><strong>جميع القوالب:</strong> تدعم متغيرات الأكاديمية</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>حفظ إعدادات الإشعارات
                            </button>

                            <div>
                                <button type="button" class="btn btn-outline-info" onclick="testUserNotification()">
                                    <i class="fas fa-paper-plane me-1"></i>اختبار الإشعارات
                                </button>
                                <a href="{{ url_for('admin.email_templates') }}?filter=user_management" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>عرض القوالب
                                </a>
                            </div>
                        </div>
                    <!-- تم نقل زر الحفظ للنموذج الرئيسي -->
                </div>
            </div>

            <!-- Email Logs -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>سجل الرسائل الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المستلم</th>
                                    <th>الموضوع</th>
                                    <th>القالب</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>{{ log.recipient_email }}</td>
                                    <td>{{ log.subject[:50] }}{% if log.subject|length > 50 %}...{% endif %}</td>
                                    <td>
                                        {% if log.template_name %}
                                            <span class="badge bg-info">{{ log.template_name }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.status == 'sent' %}
                                            <span class="badge bg-success">تم الإرسال</span>
                                        {% elif log.status == 'failed' %}
                                            <span class="badge bg-danger" title="{{ log.error_message }}">فشل</span>
                                        {% else %}
                                            <span class="badge bg-warning">في الانتظار</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">لا توجد رسائل مرسلة بعد</p>
                    {% endif %}
                </div>
            </div>

            <!-- Common SMTP Providers -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>إعدادات مقدمي الخدمة الشائعين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="border rounded p-3 mb-3">
                                <h6><i class="fab fa-google me-2"></i>Gmail</h6>
                                <small class="text-muted">
                                    <strong>خادم:</strong> smtp.gmail.com<br>
                                    <strong>منفذ:</strong> 587<br>
                                    <strong>TLS:</strong> نعم<br>
                                    <strong>ملاحظة:</strong> استخدم كلمة مرور التطبيق
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 mb-3">
                                <h6><i class="fab fa-microsoft me-2"></i>Outlook</h6>
                                <small class="text-muted">
                                    <strong>خادم:</strong> smtp-mail.outlook.com<br>
                                    <strong>منفذ:</strong> 587<br>
                                    <strong>TLS:</strong> نعم<br>
                                    <strong>ملاحظة:</strong> يدعم OAuth2
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3 mb-3">
                                <h6><i class="fas fa-envelope me-2"></i>مخصص</h6>
                                <small class="text-muted">
                                    <strong>خادم:</strong> mail.yourdomain.com<br>
                                    <strong>منفذ:</strong> 587 أو 465<br>
                                    <strong>TLS:</strong> حسب الخادم<br>
                                    <strong>ملاحظة:</strong> تواصل مع مقدم الخدمة
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i>إعدادات ذات صلة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('admin.payment_settings') }}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-credit-card me-2"></i>إعدادات الدفع
                                <div class="small text-muted">لإشعارات الدفع</div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ url_for('admin.security_settings') }}" class="btn btn-outline-warning w-100 mb-2">
                                <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                                <div class="small text-muted">لإشعارات الأمان</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>حفظ إعدادات البريد الإلكتروني
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation
    document.getElementById('emailSettingsForm').addEventListener('submit', function(e) {
        const smtpServer = document.getElementById('smtp_server').value.trim();
        const smtpPort = document.getElementById('smtp_port').value.trim();
        const smtpUsername = document.getElementById('smtp_username').value.trim();
        const smtpPassword = document.getElementById('smtp_password').value.trim();
        
        if (!smtpServer || !smtpPort || !smtpUsername || !smtpPassword) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
        
        // Re-enable button after 3 seconds (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Preview email templates
    function previewTemplate(type) {
        // This would open a modal or new window with template preview
        alert('معاينة قالب ' + type + ' - ستكون متاحة قريباً');
    }

    // Send test email
    function sendTestEmail() {
        const testEmail = document.getElementById('test_email').value.trim();
        const resultDiv = document.getElementById('test-email-result');

        if (!testEmail) {
            showTestResult('يرجى إدخال بريد إلكتروني للاختبار', 'danger');
            return;
        }

        // Show loading
        showTestResult('جاري إرسال الرسالة التجريبية...', 'info');

        // Send AJAX request
        fetch('{{ url_for("admin.test_email") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                email: testEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showTestResult(data.message, 'success');
            } else {
                showTestResult(data.message, 'danger');
            }
        })
        .catch(error => {
            showTestResult('حدث خطأ في الاتصال: ' + error.message, 'danger');
        });
    }

    function showTestResult(message, type) {
        const resultDiv = document.getElementById('test-email-result');
        resultDiv.innerHTML = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
    }

    // Template preview functions
    let selectedTemplateId = null;

    function previewTemplate() {
        const select = document.getElementById('preview_template');
        selectedTemplateId = select.value;

        if (!selectedTemplateId) {
            document.getElementById('template-preview-area').style.display = 'none';
            document.getElementById('preview-btn').disabled = true;
            document.getElementById('edit-btn').disabled = true;
            document.getElementById('test-template-btn').disabled = true;
            return;
        }

        // Enable buttons
        document.getElementById('preview-btn').disabled = false;
        document.getElementById('edit-btn').disabled = false;
        document.getElementById('test-template-btn').disabled = false;

        // Show preview area
        document.getElementById('template-preview-area').style.display = 'block';

        // Load preview content
        const previewContent = document.getElementById('preview-content');
        previewContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

        // Load template preview via AJAX
        fetch(`/admin/email-template/${selectedTemplateId}/quick-preview`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    previewContent.innerHTML = `
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-2">
                                    <strong>القالب:</strong> ${data.template_name}
                                    <span class="badge bg-${data.is_active ? 'success' : 'secondary'} ms-2">
                                        ${data.is_active ? 'مفعل' : 'معطل'}
                                    </span>
                                    <span class="badge bg-info ms-1">${data.template_type}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>الموضوع:</strong> ${data.subject}
                                </div>
                                <div class="small text-muted">
                                    ${data.preview_text}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="openFullPreview()">
                                        <i class="fas fa-eye me-1"></i>معاينة كاملة
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editTemplate()">
                                        <i class="fas fa-edit me-1"></i>تعديل القالب
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="testTemplateEmail()">
                                        <i class="fas fa-paper-plane me-1"></i>اختبار إرسال
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    previewContent.innerHTML = `
                        <div class="alert alert-danger small mb-0">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            خطأ في تحميل المعاينة: ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                previewContent.innerHTML = `
                    <div class="alert alert-danger small mb-0">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        خطأ في الاتصال: ${error.message}
                    </div>
                `;
            });
    }

    function editTemplate() {
        if (selectedTemplateId) {
            window.open(`/admin/email-template/${selectedTemplateId}/edit`, '_blank');
        }
    }

    function openFullPreview() {
        if (selectedTemplateId) {
            window.open(`/admin/email-template/${selectedTemplateId}/preview`, '_blank');
        }
    }

    function testTemplateEmail() {
        if (!selectedTemplateId) {
            alert('يرجى اختيار قالب أولاً');
            return;
        }

        const testEmail = prompt('أدخل البريد الإلكتروني لاختبار القالب:');
        if (!testEmail) return;

        // Validate email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(testEmail)) {
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return;
        }

        // Show loading in preview area
        const previewContent = document.getElementById('preview-content');
        const originalContent = previewContent.innerHTML;
        previewContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري إرسال القالب...</div>';

        // Send template test email
        fetch('/admin/test-template-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                email: testEmail,
                template_id: selectedTemplateId
            })
        })
        .then(response => response.json())
        .then(data => {
            // Restore original content
            previewContent.innerHTML = originalContent;

            if (data.success) {
                alert('✅ تم إرسال القالب بنجاح إلى ' + testEmail);
            } else {
                alert('❌ فشل إرسال القالب: ' + data.message);
            }
        })
        .catch(error => {
            // Restore original content
            previewContent.innerHTML = originalContent;
            alert('❌ خطأ في الاتصال: ' + error.message);
        });
    }

    // Quick select template function
    function quickSelectTemplate(templateId, templateName) {
        const select = document.getElementById('preview_template');
        select.value = templateId;
        previewTemplate();
    }

    // Initialize template preview
    document.getElementById('preview_template').addEventListener('change', previewTemplate);

    // Reminder system functions
    function testReminderSystem() {
        if (confirm('هل تريد اختبار نظام التذكيرات؟ سيتم فحص الحصص وإرسال التذكيرات المناسبة.')) {
            fetch('/admin/test-reminder-system', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ تم اختبار النظام بنجاح:\n' + data.message);
                } else {
                    alert('❌ فشل اختبار النظام: ' + data.message);
                }
            })
            .catch(error => {
                alert('❌ خطأ في الاتصال: ' + error.message);
            });
        }
    }



    // Quick setup for common providers
    function setupProvider(provider) {
        if (provider === 'gmail') {
            document.getElementById('smtp_server').value = 'smtp.gmail.com';
            document.getElementById('smtp_port').value = '587';
            document.getElementById('use_tls').checked = true;
        } else if (provider === 'outlook') {
            document.getElementById('smtp_server').value = 'smtp-mail.outlook.com';
            document.getElementById('smtp_port').value = '587';
            document.getElementById('use_tls').checked = true;
        }
    }

    // User management notification functions
    function testUserNotification() {
        const testEmail = prompt('أدخل البريد الإلكتروني لاختبار إشعارات إدارة المستخدمين:');
        if (!testEmail) return;

        if (!testEmail.includes('@')) {
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return;
        }

        // Show loading
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        btn.disabled = true;

        // Send test notification
        fetch('/admin/test-user-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                email: testEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ تم إرسال إشعار اختبار بنجاح!');
            } else {
                alert('❌ خطأ في الإرسال: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ خطأ في الاتصال: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
</script>
{% endblock %}
