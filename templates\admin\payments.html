{% extends "base.html" %}

{% block title %}إدارة المدفوعات - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}إدارة المدفوعات{% endblock %}

{% block content %}
<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">حالة الدفع</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {{ 'selected' if status_filter == 'pending' }}>معلق</option>
                            <option value="completed" {{ 'selected' if status_filter == 'completed' }}>مكتمل</option>
                            <option value="failed" {{ 'selected' if status_filter == 'failed' }}>فاشل</option>
                            <option value="refunded" {{ 'selected' if status_filter == 'refunded' }}>مسترد</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ url_for('admin.payments') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إزالة الفلاتر
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">مدفوعات مكتملة</div>
                        <div class="h5 mb-0">{{ payments.items|selectattr('status', 'equalto', 'completed')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">مدفوعات معلقة</div>
                        <div class="h5 mb-0">{{ payments.items|selectattr('status', 'equalto', 'pending')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-danger border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-danger text-uppercase mb-1">مدفوعات فاشلة</div>
                        <div class="h5 mb-0">{{ payments.items|selectattr('status', 'equalto', 'failed')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">إجمالي المبلغ</div>
                        <div class="h5 mb-0">
                            {{ format_currency(payments.items|selectattr('status', 'equalto', 'completed')|map(attribute='amount')|sum) }}
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>قائمة المدفوعات
                </h5>
                <span class="badge bg-primary">{{ payments.total }} دفعة</span>
            </div>
            <div class="card-body">
                {% if payments.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>الاشتراك</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>تاريخ الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="https://via.placeholder.com/32" class="rounded-circle me-2" alt="Avatar">
                                        <div>
                                            <div class="fw-bold">{{ payment.subscription.user.full_name if payment.subscription else 'غير محدد' }}</div>
                                            <div class="small text-muted">{{ payment.subscription.user.email if payment.subscription else '-' }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if payment.subscription %}
                                    <div>
                                        <div class="fw-bold">{{ payment.subscription.package.name }}</div>
                                        <div class="small text-muted">{{ payment.subscription.package.sessions_count }} حصة</div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="fw-bold">${{ "%.2f"|format(payment.amount) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ payment.payment_method or 'مراجعة يدوية' }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {{ 'bg-success' if payment.status == 'completed' else 'bg-warning' if payment.status == 'pending' else 'bg-danger' }}">
                                        {% if payment.status == 'completed' %}مكتمل
                                        {% elif payment.status == 'pending' %}معلق
                                        {% elif payment.status == 'failed' %}فاشل
                                        {% else %}مسترد{% endif %}
                                    </span>
                                </td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') if payment.payment_date else '-' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" data-bs-target="#paymentModal{{ payment.id }}"
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        {% if payment.status == 'pending' %}
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                onclick="updatePaymentStatus({{ payment.id }}, 'completed')"
                                                title="تأكيد الدفع">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="updatePaymentStatus({{ payment.id }}, 'failed')"
                                                title="رفض الدفع">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                        
                                        {% if payment.status == 'completed' %}
                                        <button type="button" class="btn btn-sm btn-outline-warning"
                                                onclick="updatePaymentStatus({{ payment.id }}, 'refunded')"
                                                title="استرداد">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if payments.pages > 1 %}
                <nav aria-label="صفحات المدفوعات">
                    <ul class="pagination justify-content-center">
                        {% if payments.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.payments', page=payments.prev_num, status=status_filter) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in payments.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != payments.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.payments', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if payments.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.payments', page=payments.next_num, status=status_filter) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مدفوعات</h5>
                    <p class="text-muted">لم يتم العثور على مدفوعات بالمعايير المحددة.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Payment Details Modals -->
{% for payment in payments.items %}
<div class="modal fade" id="paymentModal{{ payment.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الدفعة #{{ payment.id }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الدفع</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المبلغ:</strong></td>
                                <td>${{ "%.2f"|format(payment.amount) }}</td>
                            </tr>
                            <tr>
                                <td><strong>طريقة الدفع:</strong></td>
                                <td>{{ payment.payment_method or 'مراجعة يدوية' }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if payment.status == 'completed' else 'bg-warning' if payment.status == 'pending' else 'bg-danger' }}">
                                        {% if payment.status == 'completed' %}مكتمل
                                        {% elif payment.status == 'pending' %}معلق
                                        {% elif payment.status == 'failed' %}فاشل
                                        {% else %}مسترد{% endif %}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الدفع:</strong></td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') if payment.payment_date else '-' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if payment.subscription %}
                        <h6>معلومات الاشتراك</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المستخدم:</strong></td>
                                <td>{{ payment.subscription.user.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>{{ payment.subscription.user.email }}</td>
                            </tr>
                            <tr>
                                <td><strong>الباقة:</strong></td>
                                <td>{{ payment.subscription.package.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الحصص:</strong></td>
                                <td>{{ payment.subscription.package.sessions_count }}</td>
                            </tr>
                        </table>
                        {% endif %}
                    </div>
                </div>
                
                {% if payment.transaction_id %}
                <hr>
                <h6>معلومات المعاملة</h6>
                <p><strong>رقم المعاملة:</strong> {{ payment.transaction_id }}</p>
                {% endif %}
                
                {% if payment.notes %}
                <hr>
                <h6>ملاحظات</h6>
                <p>{{ payment.notes }}</p>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                {% if payment.status == 'pending' %}
                <button type="button" class="btn btn-success" 
                        onclick="updatePaymentStatus({{ payment.id }}, 'completed')">
                    تأكيد الدفع
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.getElementById('status').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Update payment status
    function updatePaymentStatus(paymentId, newStatus) {
        const actions = {
            'completed': 'تأكيد',
            'failed': 'رفض',
            'refunded': 'استرداد'
        };
        
        const action = actions[newStatus] || 'تحديث';
        
        if (confirm(`هل أنت متأكد من ${action} هذه الدفعة؟`)) {
            fetch(`/api/payments/${paymentId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء تحديث الدفعة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحديث الدفعة');
            });
        }
    }
</script>
{% endblock %}
