{% extends "base.html" %}

{% block title %}تم إلغاء الدفع - {{ academy_name }}{% endblock %}
{% block page_title %}تم إلغاء الدفع{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Cancel Message -->
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark text-center">
                <div class="display-1 mb-3">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h3 class="mb-0">تم إلغاء عملية الدفع</h3>
            </div>
            <div class="card-body text-center">
                <h5 class="text-warning mb-3">لم يتم خصم أي مبلغ من حسابك</h5>
                <p class="lead">تم إلغاء عملية الدفع بناءً على طلبك. لم يتم إنشاء أي اشتراك.</p>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ماذا حدث؟</strong>
                    <ul class="list-unstyled mt-2 mb-0">
                        <li>❌ تم إلغاء عملية الدفع</li>
                        <li>💳 لم يتم خصم أي مبلغ</li>
                        <li>📋 لم يتم إنشاء اشتراك</li>
                        <li>🔄 يمكنك المحاولة مرة أخرى</li>
                    </ul>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-4 mb-2">
                        <a href="{{ url_for('student.packages') }}" class="btn btn-primary w-100">
                            <i class="fas fa-redo me-2"></i>المحاولة مرة أخرى
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{{ url_for('student.packages') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-box me-2"></i>عرض الباقات
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{{ url_for('student.dashboard') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-home me-2"></i>العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Why was payment cancelled? -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-question-circle me-2"></i>لماذا تم إلغاء الدفع؟
                </h5>
            </div>
            <div class="card-body">
                <p>قد يكون سبب إلغاء الدفع أحد الأسباب التالية:</p>
                <ul>
                    <li>قمت بإغلاق نافذة الدفع قبل إكمال العملية</li>
                    <li>نقرت على زر "إلغاء" في صفحة الدفع</li>
                    <li>انتهت مهلة جلسة الدفع</li>
                    <li>حدث خطأ تقني مؤقت</li>
                </ul>
                
                <div class="alert alert-success">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>نصيحة:</strong> تأكد من استقرار اتصال الإنترنت وعدم إغلاق النافذة أثناء عملية الدفع.
                </div>
            </div>
        </div>

        <!-- Alternative Options -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>خيارات أخرى
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-3x text-primary mb-3"></i>
                                <h6>جرب وسيلة دفع أخرى</h6>
                                <p class="text-muted small">يمكنك اختيار وسيلة دفع مختلفة من الخيارات المتاحة</p>
                                <a href="{{ url_for('student.packages') }}" class="btn btn-primary btn-sm">
                                    اختر وسيلة أخرى
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-secondary">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-3x text-secondary mb-3"></i>
                                <h6>مراجعة يدوية</h6>
                                <p class="text-muted small">يمكنك طلب مراجعة يدوية من الإدارة بدلاً من الدفع الإلكتروني</p>
                                <a href="{{ url_for('student.packages') }}" class="btn btn-outline-secondary btn-sm">
                                    طلب مراجعة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support Info -->
        <div class="card mt-4 border-info">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <i class="fas fa-headset fa-3x text-info"></i>
                    </div>
                    <div class="col-md-10">
                        <h6 class="text-info">تحتاج مساعدة؟</h6>
                        <p class="text-muted small mb-0">
                            إذا كنت تواجه مشاكل متكررة في الدفع أو لديك أي استفسار، 
                            فريق الدعم الفني متاح لمساعدتك.
                        </p>
                        <div class="mt-2">
                            <a href="#" class="btn btn-outline-info btn-sm me-2">
                                <i class="fas fa-envelope me-1"></i>راسل الدعم
                            </a>
                            <a href="#" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-phone me-1"></i>اتصل بنا
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation to the cancel icon
        const cancelIcon = document.querySelector('.fa-times-circle');
        if (cancelIcon) {
            cancelIcon.style.animation = 'shake 0.5s ease-in-out';
        }
    });
</script>

<style>
    @keyframes shake {
        0%, 100% {
            transform: translateX(0);
        }
        25% {
            transform: translateX(-5px);
        }
        75% {
            transform: translateX(5px);
        }
    }
    
    .card.border-warning {
        box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
    }
</style>
{% endblock %}
