"""
خدمة إشعارات التسجيل
إرسال إشعارات البريد الإلكتروني عند تسجيل المستخدمين الجدد
"""

from datetime import datetime
from flask import current_app
from models import EmailSettings, AcademySettings, db
from utils.email_service import EmailService


class RegistrationNotificationService:
    def __init__(self):
        self.email_service = EmailService()
    
    def _get_academy_info(self):
        """جلب معلومات الأكاديمية"""
        academy_settings = AcademySettings.query.first()
        if academy_settings:
            return {
                'academy_name': academy_settings.academy_name or 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email or '',
                'academy_phone': academy_settings.contact_phone or '',
                'academy_website': academy_settings.website_url or '',
                'academy_address': academy_settings.address or ''
            }
        return {
            'academy_name': 'أكاديمية القرآن الكريم',
            'academy_email': '',
            'academy_phone': '',
            'academy_website': '',
            'academy_address': ''
        }
    
    def _is_registration_notification_enabled(self):
        """فحص إذا كان إشعار التسجيل مفعل"""
        email_settings = EmailSettings.query.first()
        if not email_settings:
            return True  # افتراضياً مفعل
        
        # فحص إذا كان هناك إعداد خاص بإشعارات التسجيل
        return getattr(email_settings, 'new_user_registration_notification', True)
    
    def send_registration_welcome_email(self, user, admin_approval_required=True):
        """إرسال إشعار ترحيب بالمستخدم الجديد"""
        
        if not self._is_registration_notification_enabled():
            print("ℹ️ إشعارات التسجيل معطلة")
            return False, "إشعارات التسجيل معطلة"
        
        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()
            
            # تحديد نوع المستخدم
            user_role_display = {
                'student': 'طالب',
                'teacher': 'معلم',
                'admin': 'مدير'
            }.get(user.role, user.role)
            
            # إعداد المتغيرات
            variables = {
                'user_name': user.full_name,
                'user_email': user.email,
                'user_role': user.role,
                'user_role_display': user_role_display,
                'registration_date': datetime.now().strftime('%Y-%m-%d %H:%M'),
                'admin_approval_required': admin_approval_required,
                'login_url': f"{academy_info.get('academy_website', '')}/auth/login" if academy_info.get('academy_website') else '#',
                'contact_url': f"{academy_info.get('academy_website', '')}/contact" if academy_info.get('academy_website') else '#',
                **academy_info
            }
            
            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=user.email,
                template_name='user_registration_welcome',
                variables=variables
            )
            
            if success:
                print(f"✅ تم إرسال إشعار الترحيب إلى {user.email}")
            else:
                print(f"❌ فشل إرسال إشعار الترحيب: {message}")
            
            return success, message
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الترحيب: {str(e)}")
            return False, str(e)
    
    def send_admin_new_user_notification(self, user):
        """إرسال إشعار للإدمن بتسجيل مستخدم جديد"""

        try:
            # استخدام الخدمة الجديدة لإشعارات الإدمن
            from utils.admin_notifications import send_new_user_notification_to_admin

            success, message = send_new_user_notification_to_admin(user)

            if success:
                print(f"✅ تم إرسال إشعار المستخدم الجديد للإدمن")
            else:
                print(f"❌ فشل إرسال إشعار المستخدم الجديد للإدمن: {message}")

            return success, message

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الإدمن: {str(e)}")
            return False, str(e)

    def send_account_activation_email(self, user, admin_message=None):
        """إرسال إشعار تفعيل الحساب"""

        if not self._is_registration_notification_enabled():
            print("ℹ️ إشعارات التسجيل معطلة")
            return False, "إشعارات التسجيل معطلة"

        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()

            # تحديد نوع المستخدم
            user_role_display = {
                'student': 'طالب',
                'teacher': 'معلم',
                'admin': 'مدير'
            }.get(user.role, user.role)

            # إعداد المتغيرات
            variables = {
                'user_name': user.full_name,
                'user_email': user.email,
                'user_role': user.role,
                'user_role_display': user_role_display,
                'activation_date': datetime.now().strftime('%Y-%m-%d %H:%M'),
                'admin_message': admin_message or '',
                'login_url': f"{academy_info.get('academy_website', '')}/auth/login" if academy_info.get('academy_website') else '#',
                'dashboard_url': f"{academy_info.get('academy_website', '')}/student/dashboard" if user.role == 'student' else f"{academy_info.get('academy_website', '')}/teacher/dashboard" if user.role == 'teacher' else '#',
                'packages_url': f"{academy_info.get('academy_website', '')}/packages" if academy_info.get('academy_website') else '#',
                **academy_info
            }

            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=user.email,
                template_name='account_activation_notification',
                variables=variables
            )

            if success:
                print(f"✅ تم إرسال إشعار تفعيل الحساب إلى {user.email}")
            else:
                print(f"❌ فشل إرسال إشعار تفعيل الحساب: {message}")

            return success, message

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار تفعيل الحساب: {str(e)}")
            return False, str(e)


# دالة مساعدة للاستخدام السريع
def send_registration_welcome(user, admin_approval_required=True, notify_admin=True):
    """
    دالة مساعدة لإرسال إشعارات التسجيل
    
    Args:
        user: كائن المستخدم
        admin_approval_required: هل يحتاج موافقة إدمن
        notify_admin: هل يتم إشعار الإدمن
    """
    
    notification_service = RegistrationNotificationService()
    
    results = []
    
    # إرسال ترحيب للمستخدم
    user_success, user_message = notification_service.send_registration_welcome_email(
        user, admin_approval_required
    )
    results.append(('user_welcome', user_success, user_message))
    
    # إرسال إشعار للإدمن
    if notify_admin:
        admin_success, admin_message = notification_service.send_admin_new_user_notification(user)
        results.append(('admin_notification', admin_success, admin_message))
    
    return results


def send_account_activation_notification(user, admin_message=None):
    """
    دالة مساعدة لإرسال إشعار تفعيل الحساب

    Args:
        user: كائن المستخدم
        admin_message: رسالة اختيارية من الإدمن
    """

    notification_service = RegistrationNotificationService()

    success, message = notification_service.send_account_activation_email(
        user, admin_message
    )

    return success, message
