"""
خدمة إشعارات الحصص التجريبية والتعويضية
إرسال إشعارات البريد الإلكتروني للطلاب والمعلمين عند إنشاء الحصص
"""

from datetime import datetime
from flask import current_app
from models import EmailSettings, AcademySettings, db
from utils.email_service import EmailService


class SessionNotificationService:
    def __init__(self):
        self.email_service = EmailService()
    
    def _get_academy_info(self):
        """جلب معلومات الأكاديمية"""
        academy_settings = AcademySettings.query.first()
        if academy_settings:
            return {
                'academy_name': academy_settings.academy_name or 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email or '',
                'academy_phone': academy_settings.contact_phone or '',
                'academy_website': academy_settings.website_url or '',
                'academy_address': academy_settings.address or ''
            }
        return {
            'academy_name': 'أكاديمية القرآن الكريم',
            'academy_email': '',
            'academy_phone': '',
            'academy_website': '',
            'academy_address': ''
        }
    
    def _is_trial_session_notification_enabled(self):
        """فحص إذا كان إشعار الحصص التجريبية مفعل"""
        email_settings = EmailSettings.query.first()
        if not email_settings:
            return True  # افتراضياً مفعل
        
        return getattr(email_settings, 'trial_session_notification_enabled', True)
    
    def _is_makeup_session_notification_enabled(self):
        """فحص إذا كان إشعار الحصص التعويضية مفعل"""
        email_settings = EmailSettings.query.first()
        if not email_settings:
            return True  # افتراضياً مفعل
        
        return getattr(email_settings, 'makeup_session_notification_enabled', True)
    
    def _prepare_session_variables(self, session, recipient_role, trial_instructions=None, makeup_reason=None):
        """إعداد متغيرات الحصة للقالب"""
        
        # جلب معلومات الأكاديمية
        academy_info = self._get_academy_info()
        
        # تحديد نوع الحصة للعرض
        session_type_display = {
            'trial': 'حصة تجريبية',
            'makeup': 'حصة تعويضية',
            'scheduled': 'حصة مجدولة'
        }.get(session.session_type, 'حصة مجدولة')
        
        # تنسيق التاريخ والوقت
        session_date = session.scheduled_datetime.strftime('%Y-%m-%d')
        session_time = session.scheduled_datetime.strftime('%H:%M')
        
        # رابط لوحة الحصص
        sessions_dashboard_url = f"{academy_info.get('academy_website', '')}/sessions" if academy_info.get('academy_website') else '#'
        
        # إعداد المتغيرات
        variables = {
            'recipient_role': recipient_role,
            'session_type_display': session_type_display,
            'teacher_name': session.teacher.full_name,
            'student_name': session.student.full_name,
            'session_date': session_date,
            'session_time': session_time,
            'session_duration': session.duration_minutes,
            'session_notes': session.notes or '',
            'sessions_dashboard_url': sessions_dashboard_url,
            **academy_info
        }
        
        # إضافة المتغيرات الخاصة بنوع الحصة
        if trial_instructions:
            variables['trial_instructions'] = trial_instructions
        
        if makeup_reason:
            variables['makeup_reason'] = makeup_reason
        
        return variables
    
    def send_trial_session_notification(self, session, trial_instructions=None):
        """إرسال إشعار الحصة التجريبية للطالب والمعلم"""
        
        if not self._is_trial_session_notification_enabled():
            print("ℹ️ إشعارات الحصص التجريبية معطلة")
            return False, "إشعارات الحصص التجريبية معطلة"
        
        try:
            results = []
            
            # إرسال للمعلم
            teacher_variables = self._prepare_session_variables(
                session, 'teacher', trial_instructions=trial_instructions
            )
            teacher_variables['recipient_name'] = session.teacher.full_name
            
            teacher_success, teacher_message = self.email_service.send_template_email(
                to_email=session.teacher.email,
                template_name='trial_session_created',
                variables=teacher_variables
            )
            
            results.append(('teacher', teacher_success, teacher_message))
            
            # إرسال للطالب
            student_variables = self._prepare_session_variables(
                session, 'student', trial_instructions=trial_instructions
            )
            student_variables['recipient_name'] = session.student.full_name
            
            student_success, student_message = self.email_service.send_template_email(
                to_email=session.student.email,
                template_name='trial_session_created',
                variables=student_variables
            )
            
            results.append(('student', student_success, student_message))
            
            # تقييم النتائج
            successful_sends = sum(1 for _, success, _ in results if success)
            total_sends = len(results)
            
            if successful_sends == total_sends:
                print(f"✅ تم إرسال إشعار الحصة التجريبية لجميع المستقبلين ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال لجميع المستقبلين ({successful_sends}/{total_sends})"
            elif successful_sends > 0:
                print(f"⚠️ تم إرسال إشعار الحصة التجريبية جزئياً ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال جزئياً ({successful_sends}/{total_sends})"
            else:
                print(f"❌ فشل إرسال إشعار الحصة التجريبية لجميع المستقبلين")
                return False, "فشل الإرسال لجميع المستقبلين"
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الحصة التجريبية: {str(e)}")
            return False, str(e)
    
    def send_makeup_session_notification(self, session, makeup_reason=None):
        """إرسال إشعار الحصة التعويضية للطالب والمعلم"""
        
        if not self._is_makeup_session_notification_enabled():
            print("ℹ️ إشعارات الحصص التعويضية معطلة")
            return False, "إشعارات الحصص التعويضية معطلة"
        
        try:
            results = []
            
            # إرسال للمعلم
            teacher_variables = self._prepare_session_variables(
                session, 'teacher', makeup_reason=makeup_reason
            )
            teacher_variables['recipient_name'] = session.teacher.full_name
            
            teacher_success, teacher_message = self.email_service.send_template_email(
                to_email=session.teacher.email,
                template_name='makeup_session_created',
                variables=teacher_variables
            )
            
            results.append(('teacher', teacher_success, teacher_message))
            
            # إرسال للطالب
            student_variables = self._prepare_session_variables(
                session, 'student', makeup_reason=makeup_reason
            )
            student_variables['recipient_name'] = session.student.full_name
            
            student_success, student_message = self.email_service.send_template_email(
                to_email=session.student.email,
                template_name='makeup_session_created',
                variables=student_variables
            )
            
            results.append(('student', student_success, student_message))
            
            # تقييم النتائج
            successful_sends = sum(1 for _, success, _ in results if success)
            total_sends = len(results)
            
            if successful_sends == total_sends:
                print(f"✅ تم إرسال إشعار الحصة التعويضية لجميع المستقبلين ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال لجميع المستقبلين ({successful_sends}/{total_sends})"
            elif successful_sends > 0:
                print(f"⚠️ تم إرسال إشعار الحصة التعويضية جزئياً ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال جزئياً ({successful_sends}/{total_sends})"
            else:
                print(f"❌ فشل إرسال إشعار الحصة التعويضية لجميع المستقبلين")
                return False, "فشل الإرسال لجميع المستقبلين"
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الحصة التعويضية: {str(e)}")
            return False, str(e)

    def send_subscription_session_notification(self, session):
        """
        إرسال إشعار حصة الاشتراك للطالب والمعلم

        Args:
            session: كائن الحصة

        Returns:
            tuple: (success: bool, message: str)
        """

        # فحص إذا كانت الإشعارات مفعلة (تم إزالة الفحص لأن النظام مفعل)

        try:
            print(f"📧 إرسال إشعار حصة الاشتراك للحصة {session.id}")

            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()

            # جلب معلومات الاشتراك (إذا كان موجوداً)
            subscription = session.subscription
            package_name = subscription.package.name if subscription else "حصة مجدولة"
            sessions_remaining = subscription.sessions_remaining if subscription else "غير محدد"

            # إعداد المتغيرات المشتركة
            common_variables = {
                'student_name': session.student.full_name,
                'teacher_name': session.teacher.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes,
                'package_name': package_name,
                'sessions_remaining': sessions_remaining,
                'meeting_link': session.meeting_link or 'سيتم إرساله قريباً',
                **academy_info
            }

            results = []

            # إرسال للمعلم
            teacher_variables = {
                **common_variables,
                'recipient_name': session.teacher.full_name,
                'recipient_type': 'teacher'
            }

            teacher_success, teacher_message = self.email_service.send_template_email(
                to_email=session.teacher.email,
                template_name='subscription_session_created',
                variables=teacher_variables
            )

            results.append(('teacher', teacher_success, teacher_message))

            # إرسال للطالب
            student_variables = {
                **common_variables,
                'recipient_name': session.student.full_name,
                'recipient_type': 'student'
            }

            student_success, student_message = self.email_service.send_template_email(
                to_email=session.student.email,
                template_name='subscription_session_created',
                variables=student_variables
            )

            results.append(('student', student_success, student_message))

            # تقييم النتائج
            successful_sends = sum(1 for _, success, _ in results if success)
            total_sends = len(results)

            if successful_sends == total_sends:
                print(f"✅ تم إرسال إشعار حصة الاشتراك لجميع المستقبلين ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال لجميع المستقبلين ({successful_sends}/{total_sends})"
            elif successful_sends > 0:
                print(f"⚠️ تم إرسال إشعار حصة الاشتراك جزئياً ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال جزئياً ({successful_sends}/{total_sends})"
            else:
                print(f"❌ فشل إرسال إشعار حصة الاشتراك لجميع المستقبلين")
                return False, "فشل الإرسال لجميع المستقبلين"

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار حصة الاشتراك: {str(e)}")
            return False, str(e)

    def send_general_session_notification(self, session):
        """
        إرسال إشعار حصة مجدولة عامة للطالب والمعلم

        Args:
            session: كائن الحصة

        Returns:
            tuple: (success: bool, message: str)
        """

        if not self._is_notification_enabled('session_creation'):
            print("ℹ️ إشعارات إنشاء الحصص معطلة")
            return False, "الإشعارات معطلة"

        try:
            print(f"📧 إرسال إشعار حصة مجدولة عامة للحصة {session.id}")

            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()

            # إعداد المتغيرات المشتركة
            common_variables = {
                'student_name': session.student.full_name,
                'teacher_name': session.teacher.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes,
                'meeting_link': session.meeting_link or 'سيتم إرساله قريباً',
                'session_notes': session.notes or 'لا توجد ملاحظات',
                **academy_info
            }

            results = []

            # إرسال للمعلم
            teacher_variables = {
                **common_variables,
                'recipient_name': session.teacher.full_name,
                'recipient_type': 'teacher'
            }

            teacher_success, teacher_message = self.email_service.send_template_email(
                to_email=session.teacher.email,
                template_name='general_session_created',
                variables=teacher_variables
            )

            results.append(('teacher', teacher_success, teacher_message))

            # إرسال للطالب
            student_variables = {
                **common_variables,
                'recipient_name': session.student.full_name,
                'recipient_type': 'student'
            }

            student_success, student_message = self.email_service.send_template_email(
                to_email=session.student.email,
                template_name='general_session_created',
                variables=student_variables
            )

            results.append(('student', student_success, student_message))

            # تقييم النتائج
            successful_sends = sum(1 for _, success, _ in results if success)
            total_sends = len(results)

            if successful_sends == total_sends:
                print(f"✅ تم إرسال إشعار الحصة المجدولة لجميع المستقبلين ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال لجميع المستقبلين ({successful_sends}/{total_sends})"
            elif successful_sends > 0:
                print(f"⚠️ تم إرسال إشعار الحصة المجدولة جزئياً ({successful_sends}/{total_sends})")
                return True, f"تم الإرسال جزئياً ({successful_sends}/{total_sends})"
            else:
                print(f"❌ فشل إرسال إشعار الحصة المجدولة لجميع المستقبلين")
                return False, "فشل الإرسال لجميع المستقبلين"

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الحصة المجدولة: {str(e)}")
            return False, str(e)


# دوال مساعدة للاستخدام السريع
def send_trial_session_notification(session, trial_instructions=None):
    """
    دالة مساعدة لإرسال إشعار حصة تجريبية
    
    Args:
        session: كائن الحصة
        trial_instructions: تعليمات الحصة التجريبية (اختياري)
    """
    
    notification_service = SessionNotificationService()
    return notification_service.send_trial_session_notification(session, trial_instructions)


def send_makeup_session_notification(session, makeup_reason=None):
    """
    دالة مساعدة لإرسال إشعار حصة تعويضية
    
    Args:
        session: كائن الحصة
        makeup_reason: سبب الحصة التعويضية (اختياري)
    """
    
    notification_service = SessionNotificationService()
    return notification_service.send_makeup_session_notification(session, makeup_reason)


def send_subscription_session_notification(session):
    """
    دالة مساعدة لإرسال إشعار حصة الاشتراك

    Args:
        session: كائن الحصة
    """

    notification_service = SessionNotificationService()
    return notification_service.send_subscription_session_notification(session)


def send_general_session_notification(session):
    """
    دالة مساعدة لإرسال إشعار حصة مجدولة عامة
    استخدام نفس قالب حصص الاشتراك

    Args:
        session: كائن الحصة
    """

    notification_service = SessionNotificationService()
    return notification_service.send_subscription_session_notification(session)


def notify_session_created(session, trial_instructions=None, makeup_reason=None):
    """
    دالة شاملة لإرسال إشعارات الحصص حسب النوع

    Args:
        session: كائن الحصة
        trial_instructions: تعليمات الحصة التجريبية (للحصص التجريبية)
        makeup_reason: سبب الحصة التعويضية (للحصص التعويضية)
    """

    print(f"🔔 تحديد نوع الإشعار للحصة {session.id}")
    print(f"   session_type: {session.session_type}")
    print(f"   subscription_id: {session.subscription_id}")

    if session.session_type == 'trial':
        print("📧 إرسال إشعار حصة تجريبية")
        return send_trial_session_notification(session, trial_instructions)
    elif session.session_type == 'makeup':
        print("📧 إرسال إشعار حصة تعويضية")
        return send_makeup_session_notification(session, makeup_reason)
    elif session.subscription_id:
        print("📧 إرسال إشعار حصة اشتراك")
        return send_subscription_session_notification(session)
    else:
        # للحصص المجدولة العادية، نرسل إشعار عام
        print("📧 إرسال إشعار حصة مجدولة عامة")
        return send_general_session_notification(session)
