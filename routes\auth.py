from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from models import User, db
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        remember = bool(request.form.get('remember'))
        
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            if user.is_active_user():
                login_user(user, remember=remember)
                user.last_login = datetime.utcnow()
                db.session.commit()

                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)

                # Redirect based on role
                if user.role == 'admin':
                    return redirect(url_for('admin.dashboard'))
                elif user.role == 'teacher':
                    return redirect(url_for('teacher.dashboard'))
                elif user.role == 'student':
                    return redirect(url_for('student.dashboard'))
            else:
                # Get appropriate message based on user status
                message = user.get_login_message()
                message_type = 'warning' if user.status in ['pending', 'suspended'] else 'danger'
                flash(message, message_type)
        else:
            flash('البريد الإلكتروني أو كلمة المرور غير صحيحة.', 'danger')
    
    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        phone = request.form.get('phone')
        role = request.form.get('role', 'student')
        
        # Validation
        if password != confirm_password:
            flash('كلمات المرور غير متطابقة.', 'danger')
            return render_template('auth/register.html')
        
        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني مستخدم بالفعل.', 'danger')
            return render_template('auth/register.html')
        
        # Create new user
        user = User(
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            role=role,
            status='pending'  # Requires admin approval
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()

        # إرسال بريد ترحيبي للمستخدم الجديد
        try:
            from utils.registration_notifications import send_registration_welcome

            # إرسال إشعارات التسجيل
            results = send_registration_welcome(
                user=user,
                admin_approval_required=True,  # يحتاج موافقة إدمن
                notify_admin=True  # إشعار الإدمن
            )

            # فحص نتائج الإرسال
            welcome_sent = False
            admin_notified = False

            for notification_type, success, message in results:
                if notification_type == 'user_welcome' and success:
                    welcome_sent = True
                elif notification_type == 'admin_notification' and success:
                    admin_notified = True

            if welcome_sent:
                flash('تم إنشاء حسابك بنجاح وإرسال رسالة ترحيب إلى بريدك الإلكتروني. سيتم مراجعة حسابك من قبل الإدارة.', 'success')
            else:
                flash('تم إنشاء حسابك بنجاح. سيتم مراجعته من قبل الإدارة.', 'success')

        except Exception as e:
            print(f"خطأ في إرسال إشعارات التسجيل: {str(e)}")
            flash('تم إنشاء حسابك بنجاح. سيتم مراجعته من قبل الإدارة.', 'success')

        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if request.method == 'POST':
        email = request.form.get('email')
        user = User.query.filter_by(email=email).first()

        if user:
            # Generate password reset token
            import secrets
            from datetime import datetime, timedelta

            reset_token = secrets.token_urlsafe(32)
            reset_expiry = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour

            # Store token in user record (you may want to create a separate table for this)
            user.reset_token = reset_token
            user.reset_token_expiry = reset_expiry
            db.session.commit()

            # Send password reset email
            try:
                from utils.email_service import EmailService
                from utils.user_management import get_academy_variables
                from models import EmailSettings

                email_settings = EmailSettings.query.first()
                if email_settings and email_settings.password_reset_enabled:
                    email_service = EmailService()
                    academy_vars = get_academy_variables()

                    reset_url = url_for('auth.reset_password', token=reset_token, _external=True)

                    email_vars = {
                        'user_name': user.full_name,
                        'reset_url': reset_url,
                        'reset_token': reset_token,
                        'expiry_time': '60 دقيقة',
                        **academy_vars
                    }

                    success, message = email_service.send_template_email(user.email, 'password_reset', email_vars)
                    if success:
                        flash('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.', 'info')
                    else:
                        flash('حدث خطأ في إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.', 'danger')
                else:
                    flash('خدمة استعادة كلمة المرور غير مفعلة حالياً.', 'warning')
            except Exception as e:
                print(f"خطأ في إرسال إيميل استعادة كلمة المرور: {str(e)}")
                flash('حدث خطأ في إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.', 'danger')
        else:
            flash('البريد الإلكتروني غير موجود.', 'danger')

    return render_template('auth/forgot_password.html')

@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Reset password using token"""
    user = User.query.filter_by(reset_token=token).first()

    if not user or not user.reset_token_expiry:
        flash('رابط إعادة تعيين كلمة المرور غير صحيح أو منتهي الصلاحية.', 'danger')
        return redirect(url_for('auth.login'))

    # Check if token is expired
    from datetime import datetime
    if user.reset_token_expiry < datetime.utcnow():
        flash('رابط إعادة تعيين كلمة المرور منتهي الصلاحية.', 'danger')
        return redirect(url_for('auth.forgot_password'))

    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not password or len(password) < 6:
            flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل.', 'danger')
            return render_template('auth/reset_password.html', token=token)

        if password != confirm_password:
            flash('كلمات المرور غير متطابقة.', 'danger')
            return render_template('auth/reset_password.html', token=token)

        # Update password and clear reset token
        user.set_password(password)
        user.reset_token = None
        user.reset_token_expiry = None
        db.session.commit()

        flash('تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/reset_password.html', token=token)
