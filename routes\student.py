from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from functools import wraps
from models import User, Package, Subscription, Session, Payment, SessionRating, Notification, PaymentGateway, UserNotificationSettings, db
from utils.payment_helpers import get_payment_methods_for_display
from utils.currency_helper import get_system_currency

from datetime import datetime
from sqlalchemy import func

student_bp = Blueprint('student', __name__)

def student_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'student':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@student_bp.route('/dashboard')
@login_required
@student_required
def dashboard():
    # Get active subscription
    active_subscription = Subscription.query.filter_by(
        user_id=current_user.id,
        status='active'
    ).first()
    
    # Statistics
    total_sessions = Session.query.filter_by(student_id=current_user.id).count()
    completed_sessions = Session.query.filter_by(
        student_id=current_user.id,
        status='completed'
    ).count()

    # New session type statistics
    trial_sessions = Session.get_trial_sessions_count(current_user.id, 'student')
    makeup_sessions = Session.get_makeup_sessions_count(current_user.id, 'student')
    cancelled_sessions = Session.get_cancelled_sessions_count(current_user.id, 'student')

    upcoming_sessions = Session.query.filter(
        Session.student_id == current_user.id,
        Session.status == 'scheduled',
        Session.scheduled_datetime >= datetime.now()
    ).order_by(Session.scheduled_datetime).limit(3).all()

    # Subscription sessions (sessions scheduled by admin from student's subscription)
    subscription_sessions = Session.query.filter(
        Session.student_id == current_user.id,
        Session.status == 'scheduled',
        Session.subscription_id.isnot(None),  # Sessions linked to a subscription
        Session.scheduled_datetime >= datetime.now()
    ).order_by(Session.scheduled_datetime).limit(3).all()

    # Recent completed sessions
    recent_completed = Session.query.filter_by(
        student_id=current_user.id,
        status='completed'
    ).order_by(Session.completed_at.desc()).limit(3).all()

    # Attendance rate
    attendance_rate = 0
    if total_sessions > 0:
        attended_sessions = Session.query.filter_by(
            student_id=current_user.id,
            student_attended=True
        ).count()
        attendance_rate = (attended_sessions / total_sessions) * 100

    # Count subscription sessions
    subscription_sessions_count = Session.query.filter(
        Session.student_id == current_user.id,
        Session.status == 'scheduled',
        Session.subscription_id.isnot(None),  # Sessions linked to a subscription
        Session.scheduled_datetime >= datetime.now()
    ).count()

    stats = {
        'total_sessions': total_sessions,
        'completed_sessions': completed_sessions,
        'trial_sessions': trial_sessions,
        'makeup_sessions': makeup_sessions,
        'cancelled_sessions': cancelled_sessions,
        'upcoming_sessions_count': len(upcoming_sessions),
        'subscription_sessions_count': subscription_sessions_count,
        'attendance_rate': round(attendance_rate, 1)
    }

    return render_template('student/dashboard.html',
                         stats=stats,
                         active_subscription=active_subscription,
                         upcoming_sessions=upcoming_sessions,
                         subscription_sessions=subscription_sessions,
                         recent_completed=recent_completed)

@student_bp.route('/packages')
@login_required
@student_required
def packages():
    packages = Package.query.filter_by(is_active=True).all()
    
    # Check if student has pending subscription
    pending_subscription = Subscription.query.filter_by(
        user_id=current_user.id,
        status='pending'
    ).first()

    return render_template('student/packages.html',
                         packages=packages,
                         pending_subscription=pending_subscription)

@student_bp.route('/packages/<int:package_id>/purchase')
@login_required
@student_required
def purchase_package(package_id):
    """Show payment method selection page"""
    package = Package.query.get_or_404(package_id)

    # Check if student already has active or pending subscription
    existing_subscription = Subscription.query.filter(
        Subscription.user_id == current_user.id,
        Subscription.status.in_(['active', 'pending', 'paid_pending_approval'])
    ).first()

    if existing_subscription:
        flash('لديك اشتراك نشط أو معلق بالفعل.', 'warning')
        return redirect(url_for('student.packages'))

    # Get available payment methods
    payment_methods = get_payment_methods_for_display()

    return render_template('student/payment_method_selection.html',
                         package=package,
                         payment_methods=payment_methods)

@student_bp.route('/packages/<int:package_id>/purchase', methods=['POST'])
@login_required
@student_required
def process_purchase(package_id):
    """Process the purchase with selected payment method"""
    package = Package.query.get_or_404(package_id)
    payment_method = request.form.get('payment_method')

    # Check if student already has active or pending subscription
    existing_subscription = Subscription.query.filter(
        Subscription.user_id == current_user.id,
        Subscription.status.in_(['active', 'pending', 'paid_pending_approval'])
    ).first()

    if existing_subscription:
        flash('لديك اشتراك نشط أو معلق بالفعل.', 'warning')
        return redirect(url_for('student.packages'))

    if payment_method == 'manual_review':
        # Create subscription for manual review (old system)
        subscription = Subscription(
            user_id=current_user.id,
            package_id=package_id,
            status='pending',
            sessions_remaining=package.sessions_count
        )

        db.session.add(subscription)
        db.session.commit()

        # Create payment record
        payment = Payment(
            subscription_id=subscription.id,
            user_id=current_user.id,
            amount=package.price,
            status='pending',
            payment_method='pending_review'
        )

        db.session.add(payment)
        db.session.commit()

        # Send subscription purchase notification to student
        try:
            from utils.email_service import EmailService
            from models import AcademySettings

            email_service = EmailService()
            academy_settings = AcademySettings.query.first()

            # إعداد متغيرات البريد الإلكتروني
            academy_vars = {
                'academy_name': academy_settings.academy_name if academy_settings else 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email if academy_settings else '<EMAIL>',
                'academy_phone': academy_settings.contact_phone if academy_settings else '',
                'academy_logo': academy_settings.get_logo_url() if academy_settings else ''
            }

            email_vars = {
                'user_name': current_user.full_name,
                'package_name': package.name,
                'package_price': f"{package.price:.2f}",
                'payment_method': 'مراجعة إدارية',
                'transaction_id': f"SUB-{subscription.id}",
                'purchase_date': subscription.purchase_date.strftime('%Y-%m-%d'),
                'sessions_count': package.sessions_count,
                'duration_days': package.duration_days,
                **academy_vars
            }

            success, message = email_service.send_template_email(current_user.email, 'subscription_purchased', email_vars)
            if not success:
                print(f"فشل إرسال إشعار شراء الباقة: {message}")

            # Send payment confirmation email
            payment_vars = {
                'user_name': current_user.full_name,
                'amount': f"{package.price:.2f}",
                'currency': package.currency,
                'payment_date': datetime.now().strftime('%Y-%m-%d %H:%M'),
                'package_name': package.name,
                **academy_vars
            }

            success2, message2 = email_service.send_template_email(current_user.email, 'payment_confirmation', payment_vars)
            if not success2:
                print(f"فشل إرسال تأكيد الدفع: {message2}")

        except Exception as e:
            print(f"خطأ في إرسال إشعار شراء الباقة: {str(e)}")

        # Create notification for admin
        admin_users = User.query.filter_by(role='admin').all()
        for admin in admin_users:
            notification = Notification(
                user_id=admin.id,
                title='طلب اشتراك جديد',
                message=f'طلب اشتراك جديد من {current_user.full_name} في باقة {package.name}',
                notification_type='subscription'
            )
            db.session.add(notification)

        db.session.commit()

        flash('تم إرسال طلب الاشتراك بنجاح. سيتم مراجعته من قبل الإدارة.', 'success')
        return redirect(url_for('student.subscriptions'))

    elif payment_method in ['stripe', 'paypal']:
        # Check if payment gateway is active
        gateway = PaymentGateway.query.filter_by(name=payment_method, is_active=True).first()
        if not gateway:
            flash(f'وسيلة الدفع {payment_method} غير متاحة حالياً.', 'error')
            return redirect(url_for('student.purchase_package', package_id=package_id))

        # Redirect to payment gateway with package_id (don't create subscription yet)
        if payment_method == 'stripe':
            return redirect(url_for('student.stripe_payment', package_id=package_id))
        elif payment_method == 'paypal':
            return redirect(url_for('student.paypal_payment', package_id=package_id))

    else:
        flash('يرجى اختيار وسيلة دفع صحيحة.', 'error')
        return redirect(url_for('student.purchase_package', package_id=package_id))

@student_bp.route('/payment/stripe/<int:package_id>')
@login_required
@student_required
def stripe_payment(package_id):
    """Stripe payment page"""
    package = Package.query.get_or_404(package_id)

    # Check if student already has active or pending subscription
    existing_subscription = Subscription.query.filter(
        Subscription.user_id == current_user.id,
        Subscription.status.in_(['active', 'pending', 'paid_pending_approval'])
    ).first()

    if existing_subscription:
        flash('لديك اشتراك نشط أو معلق بالفعل.', 'warning')
        return redirect(url_for('student.packages'))

    # Check if Stripe is active
    stripe_gateway = PaymentGateway.query.filter_by(name='stripe', is_active=True).first()
    if not stripe_gateway:
        flash('دفع Stripe غير متاح حالياً.', 'error')
        return redirect(url_for('student.packages'))

    # Use test key if no key is configured
    stripe_key = stripe_gateway.api_key if stripe_gateway.api_key else 'pk_test_demo_key'

    return render_template('student/stripe_payment.html',
                         package=package,
                         stripe_key=stripe_key)

@student_bp.route('/payment/paypal/<int:package_id>')
@login_required
@student_required
def paypal_payment(package_id):
    """PayPal payment page"""
    package = Package.query.get_or_404(package_id)

    # Check if student already has active or pending subscription
    existing_subscription = Subscription.query.filter(
        Subscription.user_id == current_user.id,
        Subscription.status.in_(['active', 'pending', 'paid_pending_approval'])
    ).first()

    if existing_subscription:
        flash('لديك اشتراك نشط أو معلق بالفعل.', 'warning')
        return redirect(url_for('student.packages'))

    # Check if PayPal is active
    paypal_gateway = PaymentGateway.query.filter_by(name='paypal', is_active=True).first()
    if not paypal_gateway:
        flash('دفع PayPal غير متاح حالياً.', 'error')
        return redirect(url_for('student.packages'))

    return render_template('student/paypal_payment.html',
                         package=package,
                         paypal_client_id=paypal_gateway.api_key)

@student_bp.route('/payment/stripe/process', methods=['POST'])
@login_required
@student_required
def process_stripe_payment():
    """Process Stripe payment (production mode)"""
    try:
        import stripe
        from flask import current_app

        data = request.get_json()
        package_id = data.get('package_id')
        payment_method_id = data.get('payment_method_id')

        if not payment_method_id:
            return jsonify({'success': False, 'error': 'معرف طريقة الدفع مطلوب'})

        package = Package.query.get_or_404(package_id)

        # Check if student already has active or pending subscription
        existing_subscription = Subscription.query.filter(
            Subscription.user_id == current_user.id,
            Subscription.status.in_(['active', 'pending', 'paid_pending_approval'])
        ).first()

        if existing_subscription:
            return jsonify({'success': False, 'error': 'لديك اشتراك نشط أو معلق بالفعل'})

        # Get Stripe configuration
        stripe_gateway = PaymentGateway.query.filter_by(name='stripe', is_active=True).first()
        if not stripe_gateway or not stripe_gateway.secret_key:
            return jsonify({'success': False, 'error': 'إعدادات Stripe غير مكتملة'})

        # Set Stripe API key
        stripe.api_key = stripe_gateway.secret_key

        # Check if this is demo mode
        is_demo = stripe_gateway.secret_key.startswith('sk_test_') or stripe_gateway.api_key == 'pk_test_demo_key'

        if is_demo and stripe_gateway.api_key == 'pk_test_demo_key':
            # Demo mode - simulate payment
            return handle_demo_stripe_payment(package_id)

        # Real Stripe payment processing
        try:
            # Create payment intent
            intent = stripe.PaymentIntent.create(
                amount=int(package.price * 100),  # Amount in cents
                currency=get_system_currency().lower(),
                payment_method=payment_method_id,
                confirmation_method='manual',
                confirm=True,
                return_url=f"{request.url_root}student/payment/success",
                metadata={
                    'package_id': package_id,
                    'user_id': current_user.id,
                    'user_email': current_user.email
                }
            )

            if intent.status == 'succeeded':
                # Payment successful - create subscription and payment record
                return handle_successful_stripe_payment(intent, package_id)
            elif intent.status == 'requires_action':
                # 3D Secure authentication required
                return jsonify({
                    'success': False,
                    'requires_action': True,
                    'payment_intent': {
                        'id': intent.id,
                        'client_secret': intent.client_secret
                    }
                })
            else:
                return jsonify({'success': False, 'error': 'فشل في معالجة الدفع'})

        except stripe.error.CardError as e:
            return jsonify({'success': False, 'error': f'خطأ في البطاقة: {e.user_message}'})
        except stripe.error.RateLimitError:
            return jsonify({'success': False, 'error': 'تم تجاوز الحد المسموح من الطلبات'})
        except stripe.error.InvalidRequestError as e:
            return jsonify({'success': False, 'error': f'طلب غير صحيح: {str(e)}'})
        except stripe.error.AuthenticationError:
            return jsonify({'success': False, 'error': 'خطأ في المصادقة مع Stripe'})
        except stripe.error.APIConnectionError:
            return jsonify({'success': False, 'error': 'خطأ في الاتصال بخدمة الدفع'})
        except stripe.error.StripeError as e:
            return jsonify({'success': False, 'error': f'خطأ في خدمة الدفع: {str(e)}'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'حدث خطأ غير متوقع: {str(e)}'})

def handle_demo_stripe_payment(package_id):
    """Handle demo Stripe payment"""
    package = Package.query.get_or_404(package_id)

    # Create subscription after successful payment
    subscription = Subscription(
        user_id=current_user.id,
        package_id=package_id,
        status='paid_pending_approval',
        sessions_remaining=package.sessions_count
    )

    db.session.add(subscription)
    db.session.flush()  # Get the subscription ID

    # Create payment record - keep as pending until admin approval
    payment = Payment(
        subscription_id=subscription.id,
        user_id=current_user.id,
        amount=subscription.package.price,
        status='pending_approval',  # New status for admin review
        payment_method='stripe',
        transaction_id=f'demo_txn_{subscription.id}_{datetime.now().strftime("%Y%m%d%H%M%S")}',
        gateway='stripe'
    )

    # Mark subscription as paid pending approval
    subscription.mark_as_paid_pending()

    db.session.add(payment)
    db.session.commit()

    return jsonify({'success': True})

def handle_successful_stripe_payment(intent, package_id):
    """Handle successful Stripe payment"""
    package = Package.query.get_or_404(package_id)

    # Create subscription after successful payment
    subscription = Subscription(
        user_id=current_user.id,
        package_id=package_id,
        status='paid_pending_approval',
        sessions_remaining=package.sessions_count
    )

    db.session.add(subscription)
    db.session.flush()  # Get the subscription ID

    # Create payment record
    payment = Payment(
        subscription_id=subscription.id,
        user_id=current_user.id,
        amount=package.price,
        status='completed',  # Real payment completed
        payment_method='stripe',
        transaction_id=intent.id,
        gateway='stripe'
    )

    # Mark subscription as paid pending approval
    subscription.mark_as_paid_pending()

    db.session.add(payment)
    db.session.commit()

    return jsonify({'success': True})

@student_bp.route('/payment/paypal/process', methods=['POST'])
@login_required
@student_required
def process_paypal_payment():
    """Process PayPal payment (production mode)"""
    try:
        data = request.get_json()
        package_id = data.get('package_id')
        order_id = data.get('order_id')  # PayPal order ID

        if not order_id:
            return jsonify({'success': False, 'error': 'معرف طلب PayPal مطلوب'})

        package = Package.query.get_or_404(package_id)

        # Check if student already has active or pending subscription
        existing_subscription = Subscription.query.filter(
            Subscription.user_id == current_user.id,
            Subscription.status.in_(['active', 'pending', 'paid_pending_approval'])
        ).first()

        if existing_subscription:
            return jsonify({'success': False, 'error': 'لديك اشتراك نشط أو معلق بالفعل'})

        # Get PayPal configuration
        paypal_gateway = PaymentGateway.query.filter_by(name='paypal', is_active=True).first()
        if not paypal_gateway:
            return jsonify({'success': False, 'error': 'إعدادات PayPal غير مكتملة'})

        # Check if this is demo mode
        is_demo = paypal_gateway.api_key == 'demo_client_id'

        if is_demo:
            # Demo mode - simulate payment
            return handle_demo_paypal_payment(package_id)

        # For production PayPal payments, the JavaScript SDK handles the payment
        # and we receive the order_id after successful payment
        # Create subscription and payment record
        return handle_successful_paypal_payment_simple(order_id, package_id)

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'حدث خطأ غير متوقع: {str(e)}'})

def handle_demo_paypal_payment(package_id):
    """Handle demo PayPal payment"""
    package = Package.query.get_or_404(package_id)

    # Create subscription after successful payment
    subscription = Subscription(
        user_id=current_user.id,
        package_id=package_id,
        status='paid_pending_approval',
        sessions_remaining=package.sessions_count
    )

    db.session.add(subscription)
    db.session.flush()  # Get the subscription ID

    # Create payment record - keep as pending until admin approval
    payment = Payment(
        subscription_id=subscription.id,
        user_id=current_user.id,
        amount=subscription.package.price,
        status='pending_approval',  # New status for admin review
        payment_method='paypal',
        transaction_id=f'demo_pp_{subscription.id}_{datetime.now().strftime("%Y%m%d%H%M%S")}',
        gateway='paypal'
    )

    # Mark subscription as paid pending approval
    subscription.mark_as_paid_pending()

    db.session.add(payment)
    db.session.commit()

    return jsonify({'success': True})

def handle_successful_paypal_payment_simple(order_id, package_id):
    """Handle successful PayPal payment (simplified)"""
    package = Package.query.get_or_404(package_id)

    # Create subscription after successful payment
    subscription = Subscription(
        user_id=current_user.id,
        package_id=package_id,
        status='paid_pending_approval',
        sessions_remaining=package.sessions_count
    )

    db.session.add(subscription)
    db.session.flush()  # Get the subscription ID

    # Create payment record
    payment = Payment(
        subscription_id=subscription.id,
        user_id=current_user.id,
        amount=package.price,
        status='completed',  # Payment completed via PayPal
        payment_method='paypal',
        transaction_id=order_id,
        gateway='paypal'
    )

    # Mark subscription as paid pending approval
    subscription.mark_as_paid_pending()

    db.session.add(payment)
    db.session.commit()

    return jsonify({'success': True})

def handle_successful_paypal_payment(paypal_order, package_id):
    """Handle successful PayPal payment"""
    package = Package.query.get_or_404(package_id)

    # Create subscription after successful payment
    subscription = Subscription(
        user_id=current_user.id,
        package_id=package_id,
        status='paid_pending_approval',
        sessions_remaining=package.sessions_count
    )

    db.session.add(subscription)
    db.session.flush()  # Get the subscription ID

    # Create payment record
    payment = Payment(
        subscription_id=subscription.id,
        user_id=current_user.id,
        amount=package.price,
        status='completed',  # Real payment completed
        payment_method='paypal',
        transaction_id=paypal_order.id,
        gateway='paypal'
    )

    # Mark subscription as paid pending approval
    subscription.mark_as_paid_pending()

    db.session.add(payment)
    db.session.commit()

    return jsonify({'success': True})

@student_bp.route('/payment/success')
@login_required
@student_required
def payment_success():
    """Payment success page"""
    try:
        from models import AcademySettings
        academy_settings = AcademySettings.query.first()

        academy_vars = {
            'academy_name': academy_settings.academy_name if academy_settings else 'أكاديمية القرآن الكريم',
            'academy_email': academy_settings.contact_email if academy_settings else '<EMAIL>',
            'academy_phone': academy_settings.contact_phone if academy_settings else '',
            'academy_whatsapp': academy_settings.contact_whatsapp if academy_settings else '',
            'academy_logo': academy_settings.get_logo_url() if academy_settings else ''
        }
    except Exception as e:
        print(f"خطأ في تحميل إعدادات الأكاديمية: {str(e)}")
        academy_vars = {
            'academy_name': 'أكاديمية القرآن الكريم',
            'academy_email': '<EMAIL>',
            'academy_phone': '',
            'academy_whatsapp': '',
            'academy_logo': ''
        }

    return render_template('student/payment_success.html',
                         **academy_vars)

@student_bp.route('/payment/cancel')
@login_required
@student_required
def payment_cancel():
    """Payment cancel page"""
    return render_template('student/payment_cancel.html')

@student_bp.route('/subscriptions')
@login_required
@student_required
def subscriptions():
    subscriptions = Subscription.query.filter_by(
        user_id=current_user.id
    ).order_by(Subscription.purchase_date.desc()).all()
    
    return render_template('student/subscriptions.html',
                         subscriptions=subscriptions)

@student_bp.route('/sessions')
@login_required
@student_required
def sessions():
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    type_filter = request.args.get('type', '')

    query = Session.query.filter_by(student_id=current_user.id)

    if status_filter:
        query = query.filter_by(status=status_filter)

    if type_filter == 'subscription':
        query = query.filter(Session.subscription_id.isnot(None))
    elif type_filter == 'trial':
        query = query.filter_by(session_type='trial')
    elif type_filter == 'makeup':
        query = query.filter_by(session_type='makeup')

    sessions = query.order_by(Session.scheduled_datetime.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('student/sessions.html',
                         sessions=sessions,
                         status_filter=status_filter,
                         type_filter=type_filter)

@student_bp.route('/sessions/<int:session_id>')
@login_required
@student_required
def session_details(session_id):
    session = Session.query.filter_by(
        id=session_id,
        student_id=current_user.id
    ).first_or_404()
    
    # Get existing rating
    existing_rating = SessionRating.query.filter_by(
        session_id=session_id,
        rater_id=current_user.id
    ).first()
    
    return render_template('student/session_details.html',
                         session=session,
                         existing_rating=existing_rating)

@student_bp.route('/sessions/<int:session_id>/join', methods=['POST'])
@login_required
@student_required
def join_session(session_id):
    session = Session.query.filter_by(
        id=session_id,
        student_id=current_user.id
    ).first_or_404()

    if session.status != 'scheduled':
        flash('لا يمكن الانضمام إلى هذه الحصة.', 'danger')
        return redirect(url_for('student.sessions'))

    # Check if session time is appropriate (within 15 minutes of start time)
    now = datetime.now()
    session_time = session.scheduled_datetime
    time_diff = abs((session_time - now).total_seconds() / 60)  # in minutes

    if time_diff > 15:
        flash('يمكنك الانضمام للحصة قبل 15 دقيقة من موعدها أو بعد بدايتها.', 'warning')
        return redirect(url_for('student.session_details', session_id=session_id))

    # Mark student as attended
    session.student_attended = True
    db.session.commit()

    # Ensure meeting link is up to date
    if not session.meeting_link:
        session.generate_meeting_link()
        db.session.commit()
    elif session.meeting_provider == 'google_meet' and session.calendar_event_id:
        # Update meeting link from calendar if it's Google Meet
        session._update_meeting_link_from_calendar()
        db.session.commit()

    # Flash success message and redirect to meeting link
    flash('تم تسجيل حضورك للحصة بنجاح. سيتم توجيهك للحصة الآن...', 'success')

    # If meeting link exists, redirect to it
    if session.meeting_link:
        return f'''
        <script>
            setTimeout(function() {{
                window.open('{session.meeting_link}', '_blank');
                window.location.href = '{url_for('student.session_details', session_id=session_id)}';
            }}, 1000);
        </script>
        <div style="text-align: center; padding: 50px; font-family: Arial;">
            <h3>🎉 تم تسجيل حضورك بنجاح!</h3>
            <p>سيتم توجيهك للحصة خلال ثانية واحدة...</p>
            <p><a href="{session.meeting_link}" target="_blank">انقر هنا إذا لم يتم التوجيه تلقائياً</a></p>
        </div>
        '''
    else:
        flash('لم يتم العثور على رابط الحصة. يرجى المحاولة مرة أخرى.', 'warning')
        return redirect(url_for('student.session_details', session_id=session_id))

@student_bp.route('/sessions/<int:session_id>/rate', methods=['POST'])
@login_required
@student_required
def rate_session(session_id):
    session = Session.query.filter_by(
        id=session_id,
        student_id=current_user.id,
        status='completed'
    ).first_or_404()
    
    rating_value = int(request.form.get('rating'))
    comment = request.form.get('comment', '')
    
    if rating_value < 1 or rating_value > 5:
        flash('التقييم يجب أن يكون بين 1 و 5 نجوم.', 'danger')
        return redirect(url_for('student.session_details', session_id=session_id))
    
    # Check if rating already exists
    existing_rating = SessionRating.query.filter_by(
        session_id=session_id,
        rater_id=current_user.id
    ).first()
    
    if existing_rating:
        existing_rating.rating = rating_value
        existing_rating.comment = comment
    else:
        rating = SessionRating(
            session_id=session_id,
            rater_id=current_user.id,
            rating=rating_value,
            comment=comment
        )
        db.session.add(rating)
    
    db.session.commit()
    
    # Create notification for teacher
    notification = Notification(
        user_id=session.teacher_id,
        title='تقييم جديد للحصة',
        message=f'قام {current_user.full_name} بتقييم الحصة بـ {rating_value} نجوم',
        notification_type='rating'
    )
    db.session.add(notification)
    db.session.commit()
    
    flash('تم إرسال التقييم بنجاح.', 'success')
    return redirect(url_for('student.session_details', session_id=session_id))

@student_bp.route('/teachers')
@login_required
@student_required
def teachers():
    # Get teachers who taught this student
    teachers_query = db.session.query(User).join(
        Session, User.id == Session.teacher_id
    ).filter(
        Session.student_id == current_user.id,
        User.role == 'teacher'
    ).distinct()
    
    teachers_data = []
    for teacher in teachers_query:
        total_sessions = Session.query.filter_by(
            student_id=current_user.id,
            teacher_id=teacher.id
        ).count()
        
        completed_sessions = Session.query.filter_by(
            student_id=current_user.id,
            teacher_id=teacher.id,
            status='completed'
        ).count()
        
        # Average rating given to this teacher
        avg_rating = db.session.query(func.avg(SessionRating.rating)).join(
            Session, SessionRating.session_id == Session.id
        ).filter(
            Session.student_id == current_user.id,
            Session.teacher_id == teacher.id,
            SessionRating.rater_id == current_user.id
        ).scalar() or 0
        
        teachers_data.append({
            'teacher': teacher,
            'total_sessions': total_sessions,
            'completed_sessions': completed_sessions,
            'avg_rating': round(avg_rating, 1) if avg_rating else 0
        })
    
    return render_template('student/teachers.html', teachers_data=teachers_data)

@student_bp.route('/profile')
@login_required
@student_required
def profile():
    """Student profile page"""
    # Get student statistics
    total_sessions = Session.query.filter_by(student_id=current_user.id).count()
    completed_sessions = Session.query.filter_by(
        student_id=current_user.id,
        status='completed'
    ).count()

    # Get active subscription
    active_subscription = Subscription.query.filter_by(
        user_id=current_user.id,
        status='active'
    ).first()

    # Get recent sessions
    recent_sessions = Session.query.filter_by(
        student_id=current_user.id
    ).order_by(Session.scheduled_datetime.desc()).limit(5).all()

    # Calculate completion rate
    completion_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0

    return render_template('student/profile.html',
                         total_sessions=total_sessions,
                         completed_sessions=completed_sessions,
                         active_subscription=active_subscription,
                         recent_sessions=recent_sessions,
                         completion_rate=completion_rate)

@student_bp.route('/profile/update', methods=['POST'])
@login_required
@student_required
def update_profile():
    """Update student profile"""
    try:
        # Update basic info
        current_user.first_name = request.form.get('first_name', '').strip()
        current_user.last_name = request.form.get('last_name', '').strip()
        current_user.phone = request.form.get('phone', '').strip()

        # Validate required fields
        if not current_user.first_name or not current_user.last_name:
            flash('الاسم الأول والأخير مطلوبان.', 'error')
            return redirect(url_for('student.profile'))

        # Update password if provided
        new_password = request.form.get('new_password')
        if new_password:
            current_password = request.form.get('current_password')
            if not current_user.check_password(current_password):
                flash('كلمة المرور الحالية غير صحيحة.', 'error')
                return redirect(url_for('student.profile'))

            # Validate new password
            if len(new_password) < 6:
                flash('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل.', 'error')
                return redirect(url_for('student.profile'))

            current_user.set_password(new_password)
            flash('تم تحديث كلمة المرور بنجاح.', 'success')

        db.session.commit()
        flash('تم تحديث الملف الشخصي بنجاح.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الملف الشخصي.', 'error')

    return redirect(url_for('student.profile'))

@student_bp.route('/settings')
@login_required
@student_required
def settings():
    """Student settings page"""
    # Get or create notification settings
    notification_settings = UserNotificationSettings.get_or_create_for_user(current_user.id)
    return render_template('student/settings.html', notification_settings=notification_settings)

@student_bp.route('/settings/notifications', methods=['POST'])
@login_required
@student_required
def update_notification_settings():
    """Update notification settings"""
    try:
        # Get or create notification settings
        notification_settings = UserNotificationSettings.get_or_create_for_user(current_user.id)

        # Update settings from form
        notification_settings.email_notifications = request.form.get('email_notifications') == 'on'
        notification_settings.session_reminders = request.form.get('session_reminders') == 'on'
        notification_settings.payment_notifications = request.form.get('payment_notifications') == 'on'

        db.session.commit()
        flash('تم تحديث إعدادات الإشعارات بنجاح.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الإعدادات.', 'error')

    return redirect(url_for('student.settings'))

@student_bp.route('/delete-account', methods=['POST'])
@login_required
@student_required
def delete_account():
    """Delete student account permanently"""
    try:
        user_id = current_user.id
        user_name = current_user.full_name

        # Delete related data first (to avoid foreign key constraints)
        # Delete sessions
        Session.query.filter_by(student_id=user_id).delete()

        # Delete subscriptions
        Subscription.query.filter_by(user_id=user_id).delete()

        # Delete payments
        Payment.query.filter_by(user_id=user_id).delete()

        # Delete session ratings
        SessionRating.query.filter_by(rater_id=user_id).delete()

        # Delete notifications
        Notification.query.filter_by(user_id=user_id).delete()

        # Delete notification settings
        UserNotificationSettings.query.filter_by(user_id=user_id).delete()

        # Finally delete the user
        db.session.delete(current_user)
        db.session.commit()

        # Log out the user
        from flask_login import logout_user
        logout_user()

        flash(f'تم حذف حساب {user_name} نهائياً. نأسف لرؤيتك تغادر!', 'info')
        return redirect(url_for('auth.login'))

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف الحساب. يرجى المحاولة مرة أخرى أو التواصل مع الإدارة.', 'error')
        return redirect(url_for('student.settings'))

@student_bp.route('/invoices')
@login_required
@student_required
def invoices():
    """عرض فواتير الطالب"""
    # الحصول على جميع المدفوعات الخاصة بالطالب
    payments = Payment.query.filter_by(user_id=current_user.id).order_by(Payment.payment_date.desc()).all()

    # إحصائيات الفواتير
    total_invoices = len(payments)
    paid_invoices = len([p for p in payments if p.status == 'completed'])
    pending_invoices = len([p for p in payments if p.status == 'pending'])
    failed_invoices = len([p for p in payments if p.status == 'failed'])

    # إجمالي المبلغ المدفوع
    total_amount = sum([p.amount for p in payments if p.status == 'completed'])

    return render_template('student/invoices.html',
                         payments=payments,
                         total_invoices=total_invoices,
                         paid_invoices=paid_invoices,
                         pending_invoices=pending_invoices,
                         failed_invoices=failed_invoices,
                         total_amount=total_amount)

@student_bp.route('/invoice/<int:payment_id>')
@login_required
@student_required
def invoice_details(payment_id):
    """عرض تفاصيل فاتورة محددة"""
    payment = Payment.query.filter_by(id=payment_id, user_id=current_user.id).first_or_404()

    return render_template('student/invoice_details.html', payment=payment)

@student_bp.route('/invoice/<int:payment_id>/download/<format>')
@login_required
@student_required
def download_invoice(payment_id, format):
    """تحميل الفاتورة بصيغة Excel فقط"""
    payment = Payment.query.filter_by(id=payment_id, user_id=current_user.id).first_or_404()

    if format == 'excel':
        return generate_invoice_excel(payment)
    else:
        flash('صيغة غير مدعومة. يمكن تحميل Excel فقط.', 'danger')
        return redirect(url_for('student.invoice_details', payment_id=payment_id))

# تم حذف دالة PDF - الطباعة متاحة من المتصفح مباشرة

def generate_invoice_excel(payment):
    """إنشاء فاتورة Excel"""
    try:
        import pandas as pd
        from io import BytesIO
        from flask import make_response

        # إعداد البيانات
        data = {
            'Field / الحقل': [
                'Invoice ID / رقم الفاتورة',
                'Date / التاريخ',
                'Customer / العميل',
                'Email / البريد الإلكتروني',
                'Package / الباقة',
                'Amount / المبلغ',
                'Payment Method / وسيلة الدفع',
                'Status / الحالة',
                'Transaction ID / رقم المعاملة'
            ],
            'Value / القيمة': [
                str(payment.id),
                payment.payment_date.strftime('%Y-%m-%d %H:%M'),
                payment.user.full_name,
                payment.user.email,
                payment.subscription.package.name if payment.subscription else 'N/A',
                f'{payment.amount} {payment.currency or "SAR"}',
                payment.payment_method,
                payment.status,
                payment.transaction_id or 'N/A'
            ]
        }

        # إنشاء DataFrame
        df = pd.DataFrame(data)

        # إنشاء buffer
        buffer = BytesIO()

        # كتابة Excel
        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Invoice', index=False)

        # إعداد الاستجابة
        buffer.seek(0)
        response = make_response(buffer.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=invoice_{payment.id}.xlsx'

        return response

    except ImportError:
        flash('مكتبة Excel غير متوفرة. يرجى تثبيت pandas و openpyxl.', 'danger')
        return redirect(url_for('student.invoice_details', payment_id=payment.id))
    except Exception as e:
        flash(f'خطأ في إنشاء Excel: {str(e)}', 'danger')
        return redirect(url_for('student.invoice_details', payment_id=payment.id))

# تم حذف دالة JSON - Excel متاح للتحميل
