{% extends "setup/base.html" %}

{% block title %}إعدادات الدفع - {{ super() }}{% endblock %}

{% block header_title %}إعدادات بوابات الدفع{% endblock %}
{% block header_subtitle %}قم بإعداد بوابات الدفع لقبول المدفوعات من الطلاب{% endblock %}

{% block content %}
<div class="text-center mb-4">
    <div class="feature-icon">
        <i class="fas fa-credit-card"></i>
    </div>
    <h3 class="mb-3">إعدادات بوابات الدفع</h3>
    <p class="text-muted">
        يمكنك تخطي هذه الخطوة وإعدادها لاحقاً من لوحة التحكم
    </p>
</div>

<form method="POST" id="paymentForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    
    <!-- Stripe Settings -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="enable_stripe" name="enable_stripe" 
                       {{ 'checked' if stripe_gateway and stripe_gateway.is_active else '' }}>
                <label class="form-check-label" for="enable_stripe">
                    <i class="fab fa-stripe me-2"></i><strong>تفعيل Stripe</strong>
                </label>
            </div>
        </div>
        <div class="card-body" id="stripe_settings">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="stripe_public_key" class="form-label">
                        <i class="fas fa-key me-2"></i>Publishable Key
                    </label>
                    <input type="text" class="form-control" id="stripe_public_key" name="stripe_public_key" 
                           value="{{ stripe_gateway.public_key if stripe_gateway else '' }}" 
                           placeholder="pk_test_...">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="stripe_secret_key" class="form-label">
                        <i class="fas fa-lock me-2"></i>Secret Key
                    </label>
                    <input type="password" class="form-control" id="stripe_secret_key" name="stripe_secret_key" 
                           value="{{ stripe_gateway.secret_key if stripe_gateway else '' }}" 
                           placeholder="sk_test_...">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="stripe_webhook_secret" class="form-label">
                    <i class="fas fa-webhook me-2"></i>Webhook Secret (اختياري)
                </label>
                <input type="password" class="form-control" id="stripe_webhook_secret" name="stripe_webhook_secret" 
                       value="{{ stripe_gateway.webhook_secret if stripe_gateway else '' }}" 
                       placeholder="whsec_...">
            </div>
        </div>
    </div>
    
    <!-- PayPal Settings -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="enable_paypal" name="enable_paypal" 
                       {{ 'checked' if paypal_gateway and paypal_gateway.is_active else '' }}>
                <label class="form-check-label" for="enable_paypal">
                    <i class="fab fa-paypal me-2"></i><strong>تفعيل PayPal</strong>
                </label>
            </div>
        </div>
        <div class="card-body" id="paypal_settings">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="paypal_client_id" class="form-label">
                        <i class="fas fa-id-card me-2"></i>Client ID
                    </label>
                    <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" 
                           value="{{ paypal_gateway.client_id if paypal_gateway else '' }}">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="paypal_client_secret" class="form-label">
                        <i class="fas fa-user-secret me-2"></i>Client Secret
                    </label>
                    <input type="password" class="form-control" id="paypal_client_secret" name="paypal_client_secret" 
                           value="{{ paypal_gateway.client_secret if paypal_gateway else '' }}">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="paypal_environment" class="form-label">
                    <i class="fas fa-server me-2"></i>البيئة
                </label>
                <select class="form-select" id="paypal_environment" name="paypal_environment">
                    <option value="sandbox" {{ 'selected' if paypal_gateway and paypal_gateway.environment == 'sandbox' else '' }}>
                        Sandbox (للاختبار)
                    </option>
                    <option value="live" {{ 'selected' if paypal_gateway and paypal_gateway.environment == 'live' else '' }}>
                        Live (الإنتاج)
                    </option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه:</strong> تأكد من صحة البيانات قبل الحفظ. يمكنك تعديل هذه الإعدادات لاحقاً من لوحة التحكم.
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>ملاحظة:</strong> يمكنك تخطي هذه الخطوة وإعداد بوابات الدفع لاحقاً عند الحاجة.
    </div>
    
    <div class="d-flex justify-content-between align-items-center mt-4">
        <a href="{{ url_for('setup.academy_settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>السابق
        </a>
        
        <div class="text-center">
            <button type="submit" name="skip_step" value="1" class="btn btn-outline-secondary me-2">
                <i class="fas fa-forward me-2"></i>تخطي
            </button>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-arrow-left me-2"></i>التالي
            </button>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('paymentForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Toggle settings visibility
    const stripeCheckbox = document.getElementById('enable_stripe');
    const paypalCheckbox = document.getElementById('enable_paypal');
    const stripeSettings = document.getElementById('stripe_settings');
    const paypalSettings = document.getElementById('paypal_settings');
    
    function toggleStripeSettings() {
        const inputs = stripeSettings.querySelectorAll('input');
        if (stripeCheckbox.checked) {
            stripeSettings.style.display = 'block';
            inputs.forEach(input => input.disabled = false);
        } else {
            stripeSettings.style.display = 'none';
            inputs.forEach(input => input.disabled = true);
        }
    }
    
    function togglePaypalSettings() {
        const inputs = paypalSettings.querySelectorAll('input, select');
        if (paypalCheckbox.checked) {
            paypalSettings.style.display = 'block';
            inputs.forEach(input => input.disabled = false);
        } else {
            paypalSettings.style.display = 'none';
            inputs.forEach(input => input.disabled = true);
        }
    }
    
    stripeCheckbox.addEventListener('change', toggleStripeSettings);
    paypalCheckbox.addEventListener('change', togglePaypalSettings);
    
    // Initial state
    toggleStripeSettings();
    togglePaypalSettings();
    
    form.addEventListener('submit', function(e) {
        if (e.submitter === submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        }
    });
});
</script>
{% endblock %}
