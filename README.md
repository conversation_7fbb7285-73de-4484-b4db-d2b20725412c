# نظام إدارة التعلم للقرآن الكريم (Quran LMS)

نظام إدارة تعلم متكامل مصمم خصيصاً لأكاديميات تعليم القرآن الكريم، مبني بـ Python Flask مع تصميم متجاوب بالكامل.

## المميزات الرئيسية

### 🔐 نظام المصادقة والأدوار
- تسجيل دخول آمن
- ثلاثة أدوار: إدارة، معلم، طالب
- نظام موافقة الحسابات
- إدارة الملفات الشخصية

### 📦 نظام الباقات والاشتراكات
- إنشاء وإدارة باقات متنوعة
- نظام مراجعة الاشتراكات
- تتبع الاشتراكات النشطة والمنتهية
- إدارة الحصص المتبقية

### 📅 نظام جدولة الحصص
- ثلاثة أنواع حصص: تجريبية، تعويضية، مجدولة
- جدولة يدوية وتلقائية
- ربط المعلمين بالطلاب
- تتبع الحضور والغياب

### 💳 نظام الدفع
- دعم بوابات دفع متعددة (Stripe, PayPal)
- تتبع المدفوعات والفواتير
- تقارير مالية مفصلة
- إدارة المبالغ المستردة

### 📊 نظام التقارير والإحصائيات
- تقارير مالية شاملة
- إحصائيات الحصص والحضور
- تقارير أداء المعلمين والطلاب
- لوحات تحكم تفاعلية

### 🔔 نظام الإشعارات
- إشعارات داخلية فورية
- إشعارات البريد الإلكتروني
- تنبيهات الحصص والمواعيد
- إشعارات الدفع والاشتراكات

### ⭐ نظام التقييم
- تقييم الحصص من الطلاب والمعلمين
- تعليقات وملاحظات
- تتبع جودة التعليم
- تحسين الخدمات

### 🎨 التصميم والواجهة
- تصميم متجاوب بالكامل
- دعم جميع أحجام الشاشات
- قائمة جانبية قابلة للطي
- وضع ليلي ونهاري
- خطوط عربية جميلة

### ⚙️ إعدادات الأكاديمية
- تخصيص اسم ولوجو الأكاديمية
- إعدادات الألوان والعلامة التجارية
- إعدادات بوابات الدفع
- إعدادات البريد الإلكتروني

## متطلبات النظام

- Python 3.8+
- Flask 2.3+
- SQLAlchemy
- Bootstrap 5
- PostgreSQL (للإنتاج) أو SQLite (للتطوير)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd quranlms
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتحرير ملف .env وإضافة القيم المطلوبة
```

### 5. تهيئة قاعدة البيانات
```bash
python init_db.py
```

### 6. تشغيل التطبيق
```bash
python run.py
```

سيعمل التطبيق على `http://localhost:5000`

## بيانات الدخول الافتراضية

**الإدارة:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

⚠️ **مهم:** يرجى تغيير كلمة المرور بعد أول تسجيل دخول!

## الاستضافة على Render

### 1. إنشاء حساب على Render
قم بزيارة [render.com](https://render.com) وأنشئ حساباً جديداً.

### 2. ربط المستودع
- اربط حساب GitHub الخاص بك
- اختر مستودع المشروع

### 3. إعداد قاعدة البيانات
- أنشئ قاعدة بيانات PostgreSQL جديدة
- احفظ رابط الاتصال

### 4. إعداد الخدمة
- أنشئ خدمة ويب جديدة
- اختر المستودع
- استخدم الإعدادات من ملف `render.yaml`

### 5. إعداد متغيرات البيئة
أضف المتغيرات التالية في إعدادات Render:
```
SECRET_KEY=your-secret-key-here
DATABASE_URL=your-postgresql-url
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

### 6. النشر
- اضغط على "Deploy"
- انتظر اكتمال عملية النشر

## هيكل المشروع

```
quranlms/
├── app.py                 # التطبيق الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── init_db.py            # تهيئة قاعدة البيانات
├── run.py                # ملف التشغيل
├── requirements.txt      # المتطلبات
├── render.yaml          # إعدادات Render
├── routes/              # مسارات التطبيق
│   ├── auth.py         # مسارات المصادقة
│   ├── admin.py        # مسارات الإدارة
│   ├── teacher.py      # مسارات المعلم
│   ├── student.py      # مسارات الطالب
│   └── api.py          # واجهة برمجة التطبيقات
├── templates/           # قوالب HTML
│   ├── base.html       # القالب الأساسي
│   ├── auth/           # قوالب المصادقة
│   ├── admin/          # قوالب الإدارة
│   ├── teacher/        # قوالب المعلم
│   └── student/        # قوالب الطالب
└── static/             # الملفات الثابتة
    ├── css/
    ├── js/
    └── images/
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## الترخيص

## 🌐 النشر على Render

### 1. إعداد قاعدة البيانات
- أنشئ PostgreSQL database على Render
- احصل على DATABASE_URL

### 2. إعداد Web Service
- اربط مستودع GitHub
- اختر Python environment
- استخدم Build Command: `pip install -r requirements.txt && python production_setup.py`
- استخدم Start Command: `gunicorn app:app`

### 3. متغيرات البيئة المطلوبة
```
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://...
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
```

## 🛡️ الأمان والحماية

- **تشفير كلمات المرور**: Werkzeug
- **حماية CSRF**: Flask-WTF
- **جلسات آمنة**: Flask-Login
- **التحقق من البريد الإلكتروني**
- **نظام صلاحيات متدرج**

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

---

**أكاديمية القرآن الكريم** - نظام إدارة تعلم متكامل 🕌
