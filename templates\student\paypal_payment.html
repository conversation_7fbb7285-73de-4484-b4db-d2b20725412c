{% extends "base.html" %}

{% block title %}دفع آمن - PayPal - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}دفع آمن عبر PayPal{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Order Summary -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>ملخص الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5>{{ package.name }}</h5>
                        <p class="text-muted">{{ package.description }}</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>{{ package.sessions_count }} حصة</li>
                            <li><i class="fas fa-check text-success me-2"></i>مدة {{ package.duration_days }} يوم</li>
                            {% if package.features %}
                                {% for feature in package.features.split(',') %}
                                <li><i class="fas fa-check text-success me-2"></i>{{ feature.strip() }}</li>
                                {% endfor %}
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="display-6 text-primary">
                            {{ "%.2f"|format(package.price) }} {{ academy_currency }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PayPal Payment -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fab fa-paypal me-2"></i>الدفع عبر PayPal
                </h5>
            </div>
            <div class="card-body">
                <!-- PayPal Buttons Container -->
                <div id="paypal-button-container"></div>

                <!-- Demo mode fallback -->
                <div id="demo-mode" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>وضع التجربة:</strong> هذا عرض تجريبي لواجهة PayPal.
                        لا يتم خصم أي مبلغ فعلي.
                    </div>

                    <div class="text-center mb-4">
                        <div class="card bg-light p-4">
                            <div class="display-1 text-primary mb-3">
                                <i class="fab fa-paypal"></i>
                            </div>
                            <h5>ادفع بأمان عبر PayPal</h5>
                            <p class="text-muted">سيتم تحويلك إلى PayPal لإكمال عملية الدفع</p>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" id="demo-paypal-button" class="btn btn-warning btn-lg">
                            <i class="fab fa-paypal me-2"></i>
                            ادفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} عبر PayPal
                        </button>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('student.purchase_package', package_id=package.id) }}"
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة لاختيار وسيلة الدفع
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Info -->
        <div class="card mt-4 border-success">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <i class="fas fa-shield-alt fa-3x text-success"></i>
                    </div>
                    <div class="col-md-10">
                        <h6 class="text-success">حماية متقدمة بواسطة PayPal</h6>
                        <p class="text-muted small mb-0">
                            <i class="fas fa-check text-success me-1"></i>حماية المشتري<br>
                            <i class="fas fa-check text-success me-1"></i>تشفير SSL متقدم<br>
                            <i class="fas fa-check text-success me-1"></i>مراقبة الاحتيال 24/7
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-warning mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>جاري التحويل إلى PayPal...</h5>
                <p class="text-muted">يرجى عدم إغلاق هذه الصفحة</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- PayPal SDK -->
{% if paypal_client_id == 'demo_client_id' %}
    <!-- Demo mode - no real PayPal SDK -->
{% else %}
    <script src="https://www.paypal.com/sdk/js?client-id={{ paypal_client_id }}&currency=USD&intent=capture&enable-funding=venmo&disable-funding=credit,card"></script>
{% endif %}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paypalClientId = '{{ paypal_client_id }}';

        // Check if this is demo mode
        if (!paypalClientId || paypalClientId === 'demo_client_id') {
            // Show demo mode
            document.getElementById('demo-mode').style.display = 'block';
            setupDemoMode();
        } else {
            // Setup real PayPal buttons
            setupPayPalButtons();
        }
    });

    function setupDemoMode() {
        const demoButton = document.getElementById('demo-paypal-button');

        demoButton.addEventListener('click', function() {
            // Show loading modal
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            loadingModal.show();

            // Disable button
            demoButton.disabled = true;
            demoButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحويل...';

            // Simulate PayPal processing
            setTimeout(function() {
                // Simulate successful payment
                processDemoPayPalPayment();
            }, 2000);
        });
    }

    function setupPayPalButtons() {
        // Check if PayPal SDK is loaded
        if (typeof paypal === 'undefined') {
            console.error('PayPal SDK not loaded');
            document.getElementById('paypal-button-container').innerHTML =
                '<div class="alert alert-danger">فشل في تحميل PayPal. يرجى إعادة تحميل الصفحة.</div>';
            return;
        }

        paypal.Buttons({
            style: {
                layout: 'vertical',
                color: 'gold',
                shape: 'rect',
                label: 'paypal'
            },

            createOrder: function(data, actions) {
                return actions.order.create({
                    purchase_units: [{
                        amount: {
                            value: '{{ "%.2f"|format(package.price) }}',
                            currency_code: 'USD'
                        },
                        description: 'Package: {{ package.name }}',
                        custom_id: '{{ package.id }}'
                    }]
                });
            },

            onApprove: function(data, actions) {
                return actions.order.capture().then(function(details) {
                    // Payment successful
                    console.log('PayPal payment successful:', details);
                    processRealPayPalPayment(data.orderID);
                }).catch(function(error) {
                    console.error('PayPal capture error:', error);
                    alert('حدث خطأ في تأكيد الدفع. يرجى المحاولة مرة أخرى.');
                });
            },

            onError: function(err) {
                console.error('PayPal Error:', err);
                alert('حدث خطأ في معالجة الدفع عبر PayPal. يرجى المحاولة مرة أخرى.');
            },

            onCancel: function(data) {
                alert('تم إلغاء عملية الدفع.');
            }
        }).render('#paypal-button-container');
    }

    async function processDemoPayPalPayment() {
        try {
            const response = await fetch('/student/payment/paypal/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    package_id: {{ package.id }},
                    order_id: 'demo_order_' + Date.now()
                })
            });

            const result = await response.json();

            if (result.success) {
                // Payment successful
                window.location.href = '{{ url_for("student.payment_success") }}';
            } else {
                // Payment failed
                alert('حدث خطأ في معالجة الدفع: ' + (result.error || 'خطأ غير معروف'));

                // Re-enable button
                const demoButton = document.getElementById('demo-paypal-button');
                demoButton.disabled = false;
                demoButton.innerHTML = '<i class="fab fa-paypal me-2"></i>ادفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} عبر PayPal';

                // Hide loading modal
                const loadingModal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
                loadingModal.hide();
            }
        } catch (error) {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');

            // Re-enable button
            const demoButton = document.getElementById('demo-paypal-button');
            demoButton.disabled = false;
            demoButton.innerHTML = '<i class="fab fa-paypal me-2"></i>ادفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} عبر PayPal';

            // Hide loading modal
            const loadingModal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
            loadingModal.hide();
        }
    }

    async function processRealPayPalPayment(orderId) {
        try {
            const response = await fetch('/student/payment/paypal/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    package_id: {{ package.id }},
                    order_id: orderId
                })
            });

            const result = await response.json();

            if (result.success) {
                // Payment successful
                window.location.href = '{{ url_for("student.payment_success") }}';
            } else {
                // Payment failed
                alert('حدث خطأ في معالجة الدفع: ' + (result.error || 'خطأ غير معروف'));
            }
        } catch (error) {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
        }
    }
</script>
{% endblock %}
