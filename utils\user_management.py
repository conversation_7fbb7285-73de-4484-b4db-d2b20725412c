"""
User management utilities for admin operations
Handles user suspension, banning, deletion, and restoration
"""

from datetime import datetime, timedelta
from flask import url_for
from models import User, UserActionLog, Notification, AcademySettings, db


def log_user_action(user_id, admin_id, action, old_status, new_status, reason=None, suspension_end_date=None):
    """Log admin action on user account"""
    action_log = UserActionLog(
        user_id=user_id,
        admin_id=admin_id,
        action=action,
        old_status=old_status,
        new_status=new_status,
        reason=reason,
        suspension_end_date=suspension_end_date
    )
    db.session.add(action_log)
    return action_log


def create_user_notification(user_id, title, message, notification_type='user_management', priority='normal', icon=None):
    """Create notification for user using new notification service"""
    from utils.notification_service import NotificationService
    return NotificationService.create_notification(
        user_id=user_id,
        title=title,
        message=message,
        notification_type=notification_type,
        priority=priority,
        icon=icon
    )


def get_academy_variables():
    """Get academy settings variables for email templates"""
    settings = AcademySettings.query.first()
    if settings:
        return settings.to_dict()
    return {
        'academy_name': 'أكاديمية القرآن الكريم',
        'academy_email': '',
        'academy_phone': '',
        'academy_whatsapp': '',
        'academy_logo': ''
    }


def suspend_user(user_id, admin_id, reason, suspension_days=None, suspension_end_date=None):
    """Suspend user temporarily"""
    try:
        user = User.query.get(user_id)
        if not user:
            return False, "المستخدم غير موجود"
        
        old_status = user.status
        
        # Calculate suspension end date
        if suspension_days and not suspension_end_date:
            suspension_end_date = datetime.utcnow() + timedelta(days=suspension_days)
        
        # Update user status
        user.status = 'suspended'
        user.ban_reason = reason
        user.suspension_end_date = suspension_end_date
        
        # Log action
        log_user_action(user_id, admin_id, 'suspended', old_status, 'suspended', reason, suspension_end_date)
        
        # Create notification
        title = 'تم حظر حسابك مؤقتاً'
        message = f'تم حظر حسابك مؤقتاً. السبب: {reason}'
        if suspension_end_date:
            message += f' حتى تاريخ {suspension_end_date.strftime("%Y-%m-%d %H:%M")}'
        create_user_notification(user_id, title, message, priority='urgent', icon='fas fa-pause-circle')
        
        db.session.commit()
        
        # Send email notification
        try:
            from utils.email_service import EmailService
            email_service = EmailService()

            if email_service is None:
                print("خطأ: فشل في إنشاء EmailService")
                return True, "تم حظر المستخدم بنجاح (لكن فشل إرسال الإشعار)"

            if not hasattr(email_service, 'send_template_email'):
                print("خطأ: EmailService لا يحتوي على send_template_email")
                return True, "تم حظر المستخدم بنجاح (لكن فشل إرسال الإشعار)"

            academy_vars = get_academy_variables()

            email_vars = {
                'user_name': user.full_name,
                'suspension_reason': reason,
                'suspension_end_date': suspension_end_date.strftime('%Y-%m-%d %H:%M') if suspension_end_date else None,
                **academy_vars
            }

            success, message = email_service.send_template_email(user.email, 'user_suspended', email_vars)
            if not success:
                print(f"فشل إرسال القالب: {message}")
        except Exception as e:
            print(f"خطأ في إرسال إيميل الحظر: {str(e)}")
        
        return True, "تم حظر المستخدم بنجاح"
        
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في حظر المستخدم: {str(e)}"


def ban_user(user_id, admin_id, reason):
    """Ban user permanently"""
    try:
        user = User.query.get(user_id)
        if not user:
            return False, "المستخدم غير موجود"
        
        old_status = user.status
        
        # Update user status
        user.status = 'banned'
        user.ban_reason = reason
        user.suspension_end_date = None  # Clear any temporary suspension
        
        # Log action
        log_user_action(user_id, admin_id, 'banned', old_status, 'banned', reason)
        
        # Create notification
        title = 'تم حظر حسابك نهائياً'
        message = f'تم حظر حسابك نهائياً. السبب: {reason}'
        create_user_notification(user_id, title, message, priority='urgent', icon='fas fa-ban')
        
        db.session.commit()
        
        # Send email notification
        try:
            from utils.email_service import EmailService
            email_service = EmailService()
            academy_vars = get_academy_variables()

            email_vars = {
                'user_name': user.full_name,
                'ban_reason': reason,
                **academy_vars
            }

            success, message = email_service.send_template_email(user.email, 'user_banned', email_vars)
            if not success:
                print(f"فشل إرسال القالب: {message}")
        except Exception as e:
            print(f"خطأ في إرسال إيميل الحظر النهائي: {str(e)}")
        
        return True, "تم حظر المستخدم نهائياً بنجاح"
        
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في حظر المستخدم نهائياً: {str(e)}"


def disable_user(user_id, admin_id, reason=None):
    """Disable user account"""
    try:
        user = User.query.get(user_id)
        if not user:
            return False, "المستخدم غير موجود"
        
        old_status = user.status
        
        # Update user status
        user.status = 'inactive'
        if reason:
            user.ban_reason = reason
        
        # Log action
        log_user_action(user_id, admin_id, 'disabled', old_status, 'inactive', reason)
        
        # Create notification
        title = 'تم تعطيل حسابك'
        message = f'تم تعطيل حسابك من قبل الإدارة.'
        if reason:
            message += f' السبب: {reason}'
        create_user_notification(user_id, title, message, priority='high', icon='fas fa-user-times')
        
        db.session.commit()
        
        # Send email notification
        try:
            from utils.email_service import EmailService
            email_service = EmailService()
            academy_vars = get_academy_variables()
            
            email_vars = {
                'user_name': user.full_name,
                'disable_reason': reason,
                **academy_vars
            }
            
            success, message = email_service.send_template_email(user.email, 'user_account_disabled', email_vars)
            if not success:
                print(f"فشل إرسال القالب: {message}")
        except Exception as e:
            print(f"خطأ في إرسال إيميل تعطيل الحساب: {str(e)}")
        
        return True, "تم تعطيل المستخدم بنجاح"
        
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في تعطيل المستخدم: {str(e)}"


def delete_user(user_id, admin_id, reason=None, permanent=False):
    """Delete user account (soft delete by default)"""
    try:
        user = User.query.get(user_id)
        if not user:
            return False, "المستخدم غير موجود"
        
        old_status = user.status
        
        if permanent:
            # Hard delete - remove from database completely
            # Log action first
            log_user_action(user_id, admin_id, 'deleted_permanently', old_status, 'deleted', reason)
            db.session.commit()

            # Delete related data first to avoid foreign key constraints
            try:
                # Delete user notifications
                from models import Notification
                Notification.query.filter_by(user_id=user_id).delete()

                # Delete user action logs
                from models import UserActionLog
                UserActionLog.query.filter_by(user_id=user_id).delete()

                # Delete session ratings by this user (using rater_id)
                from models import SessionRating
                SessionRating.query.filter_by(rater_id=user_id).delete()

                # Delete session reminders for this user
                from models import SessionReminder
                SessionReminder.query.filter_by(user_id=user_id).delete()

                # Delete or update sessions to remove user references
                from models import Session
                # For sessions where user is student - delete the sessions
                Session.query.filter_by(student_id=user_id).delete()
                # For sessions where user is teacher - delete the sessions
                Session.query.filter_by(teacher_id=user_id).delete()

                # Delete subscriptions (cannot set user_id to NULL due to NOT NULL constraint)
                from models import Subscription
                Subscription.query.filter_by(user_id=user_id).delete()

                # Delete payments (cannot set user_id to NULL due to NOT NULL constraint)
                from models import Payment
                Payment.query.filter_by(user_id=user_id).delete()

                print(f"✅ تم حذف البيانات المرتبطة بالمستخدم {user_id}")

            except Exception as e:
                print(f"⚠️ تحذير: مشكلة في حذف بعض البيانات المرتبطة: {str(e)}")

            # Delete user completely
            db.session.delete(user)
            db.session.commit()

            return True, "تم حذف المستخدم نهائياً من قاعدة البيانات"
        else:
            # Soft delete - mark as deleted but keep in database
            user.status = 'deleted'
            user.deleted_at = datetime.utcnow()
            user.can_be_restored = True
            if reason:
                user.ban_reason = reason
            
            # Log action
            log_user_action(user_id, admin_id, 'deleted', old_status, 'deleted', reason)
            
            # Create notification
            title = 'تم حذف حسابك'
            message = f'تم حذف حسابك من قبل الإدارة.'
            if reason:
                message += f' السبب: {reason}'
            create_user_notification(user_id, title, message)
            
            db.session.commit()
            
            # Send email notification
            try:
                from utils.email_service import EmailService
                email_service = EmailService()
                academy_vars = get_academy_variables()
                
                email_vars = {
                    'user_name': user.full_name,
                    'delete_reason': reason,
                    'can_be_restored': user.can_be_restored,
                    **academy_vars
                }
                
                success, message = email_service.send_template_email(user.email, 'user_account_deleted', email_vars)
                if not success:
                    print(f"فشل إرسال القالب: {message}")
            except Exception as e:
                print(f"خطأ في إرسال إيميل حذف الحساب: {str(e)}")
            
            return True, "تم حذف المستخدم بنجاح (يمكن استرداده)"
        
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في حذف المستخدم: {str(e)}"


def restore_user(user_id, admin_id):
    """Restore deleted user account"""
    try:
        user = User.query.get(user_id)
        if not user:
            return False, "المستخدم غير موجود"
        
        if user.status != 'deleted':
            return False, "المستخدم غير محذوف"
        
        if not user.can_be_restored:
            return False, "لا يمكن استرداد هذا المستخدم"
        
        old_status = user.status
        
        # Restore user
        user.status = 'approved'
        user.deleted_at = None
        user.ban_reason = None
        user.suspension_end_date = None
        
        # Log action
        log_user_action(user_id, admin_id, 'restored', old_status, 'approved')
        
        # Create notification
        title = 'تم استرداد حسابك'
        message = 'تم استرداد حسابك بنجاح وأصبح نشطاً مرة أخرى.'
        create_user_notification(user_id, title, message)
        
        db.session.commit()
        
        # Send email notification
        try:
            from utils.email_service import EmailService
            email_service = EmailService()
            academy_vars = get_academy_variables()
            
            email_vars = {
                'user_name': user.full_name,
                'login_url': url_for('auth.login', _external=True),
                **academy_vars
            }
            
            success, message = email_service.send_template_email(user.email, 'user_account_restored', email_vars)
            if not success:
                print(f"فشل إرسال القالب: {message}")
        except Exception as e:
            print(f"خطأ في إرسال إيميل استرداد الحساب: {str(e)}")
        
        return True, "تم استرداد المستخدم بنجاح"
        
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في استرداد المستخدم: {str(e)}"


def activate_user(user_id, admin_id):
    """Activate suspended/banned/inactive user"""
    try:
        user = User.query.get(user_id)
        if not user:
            return False, "المستخدم غير موجود"
        
        if user.status == 'approved':
            return False, "المستخدم نشط بالفعل"
        
        old_status = user.status
        
        # Activate user
        user.status = 'approved'
        user.ban_reason = None
        user.suspension_end_date = None
        
        # Log action
        log_user_action(user_id, admin_id, 'activated', old_status, 'approved')
        
        # Create notification
        title = 'تم تفعيل حسابك'
        message = 'تم تفعيل حسابك بنجاح وأصبح نشطاً مرة أخرى.'
        create_user_notification(user_id, title, message)

        db.session.commit()

        # إرسال إشعار بريدي بتفعيل الحساب
        try:
            from utils.registration_notifications import send_account_activation_notification

            # رسالة اختيارية من الإدمن
            admin_message = "تم مراجعة حسابك والموافقة عليه من قبل فريق الإدارة. نرحب بك في أكاديميتنا!"

            email_success, email_message = send_account_activation_notification(user, admin_message)

            if email_success:
                print(f"✅ تم إرسال إشعار تفعيل الحساب بالبريد الإلكتروني إلى {user.email}")
            else:
                print(f"⚠️ تم تفعيل الحساب لكن فشل إرسال البريد: {email_message}")

        except Exception as e:
            print(f"⚠️ تم تفعيل الحساب لكن خطأ في إرسال البريد: {str(e)}")

        return True, "تم تفعيل المستخدم بنجاح"
        
    except Exception as e:
        db.session.rollback()
        return False, f"خطأ في تفعيل المستخدم: {str(e)}"
