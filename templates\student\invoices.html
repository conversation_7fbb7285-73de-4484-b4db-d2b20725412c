{% extends "base.html" %}

{% block title %}فواتيري - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}فواتيري{% endblock %}

{% block extra_css %}
<style>
/* تحسينات متجاوبة للفواتير */
@media (max-width: 768px) {
    .card-body .h3 {
        font-size: 1.5rem;
    }

    .card-title.small {
        font-size: 0.875rem;
    }

    .btn-group .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .card-body .h3 {
        font-size: 1.25rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }
}

/* تحسين عرض الجداول */
.table-responsive {
    border-radius: 0.375rem;
}

/* تحسين البطاقات للشاشات الصغيرة */
.card .badge {
    font-size: 0.75rem;
}

/* تحسين الأزرار */
.btn-group .dropdown-menu {
    min-width: 150px;
}

@media print {
    .btn, .dropdown, .navbar, .sidebar {
        display: none !important;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- إحصائيات الفواتير -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1 small">إجمالي الفواتير</h6>
                            <h3 class="mb-0">{{ total_invoices }}</h3>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1 small">فواتير مدفوعة</h6>
                            <h3 class="mb-0">{{ paid_invoices }}</h3>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1 small">فواتير معلقة</h6>
                            <h3 class="mb-0">{{ pending_invoices }}</h3>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1 small">إجمالي المدفوع</h6>
                            <h3 class="mb-0 small">{{ format_currency(total_amount) }}</h3>
                        </div>
                        <div class="ms-2">
                            <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الفواتير -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-file-invoice-dollar me-2"></i>قائمة الفواتير
            </h5>
        </div>
        <div class="card-body">
            {% if payments %}
            <!-- عرض الجدول للشاشات الكبيرة -->
            <div class="table-responsive d-none d-lg-block">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>الباقة</th>
                            <th>المبلغ</th>
                            <th>وسيلة الدفع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>
                                <strong>#{{ payment.id }}</strong>
                            </td>
                            <td>
                                {{ payment.payment_date.strftime('%Y-%m-%d') }}<br>
                                <small class="text-muted">{{ payment.payment_date.strftime('%H:%M') }}</small>
                            </td>
                            <td>
                                {% if payment.subscription and payment.subscription.package %}
                                    <span class="badge bg-info">{{ payment.subscription.package.name }}</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ format_currency(payment.amount) }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ payment.payment_method }}</span>
                            </td>
                            <td>
                                {% if payment.status == 'completed' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>مكتمل
                                    </span>
                                {% elif payment.status == 'pending' %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>معلق
                                    </span>
                                {% elif payment.status == 'failed' %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>فشل
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ payment.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('student.invoice_details', payment_id=payment.id) }}"
                                       class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    {% if payment.status == 'completed' %}
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle"
                                                data-bs-toggle="dropdown" title="خيارات">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item"
                                                   href="javascript:void(0)" onclick="printInvoice({{ payment.id }})">
                                                    <i class="fas fa-print me-2"></i>طباعة
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item"
                                                   href="{{ url_for('student.download_invoice', payment_id=payment.id, format='excel') }}">
                                                    <i class="fas fa-file-excel me-2"></i>تحميل Excel
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- عرض البطاقات للشاشات الصغيرة -->
            <div class="d-lg-none">
                {% for payment in payments %}
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h6 class="card-title mb-1">
                                    <strong>#{{ payment.id }}</strong>
                                    {% if payment.status == 'completed' %}
                                        <span class="badge bg-success ms-2">
                                            <i class="fas fa-check me-1"></i>مكتمل
                                        </span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning ms-2">
                                            <i class="fas fa-clock me-1"></i>معلق
                                        </span>
                                    {% elif payment.status == 'failed' %}
                                        <span class="badge bg-danger ms-2">
                                            <i class="fas fa-times me-1"></i>فشل
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary ms-2">{{ payment.status }}</span>
                                    {% endif %}
                                </h6>
                                <p class="text-muted small mb-1">{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                            <div class="col-6 text-end">
                                <h5 class="text-success mb-1">{{ format_currency(payment.amount) }}</h5>
                                <span class="badge bg-secondary">{{ payment.payment_method }}</span>
                            </div>
                        </div>

                        {% if payment.subscription and payment.subscription.package %}
                        <div class="mt-2">
                            <span class="badge bg-info">{{ payment.subscription.package.name }}</span>
                        </div>
                        {% endif %}

                        <div class="mt-3">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ url_for('student.invoice_details', payment_id=payment.id) }}"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>التفاصيل
                                </a>

                                {% if payment.status == 'completed' %}
                                <button type="button" class="btn btn-outline-success btn-sm"
                                        onclick="printInvoice({{ payment.id }})">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                                <a href="{{ url_for('student.download_invoice', payment_id=payment.id, format='excel') }}"
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-file-excel me-1"></i>Excel
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فواتير</h5>
                <p class="text-muted">لم تقم بأي عمليات شراء حتى الآن.</p>
                <a href="{{ url_for('student.packages') }}" class="btn btn-primary">
                    <i class="fas fa-shopping-cart me-2"></i>شراء باقة
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-lg-6 col-md-12 mb-3">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-print text-primary me-2"></i>
                            يمكنك طباعة الفواتير المكتملة مباشرة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-file-excel text-success me-2"></i>
                            تحميل الفواتير بصيغة Excel للتحليل
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-warning me-2"></i>
                            الفواتير المعلقة في انتظار المراجعة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-shield-alt text-info me-2"></i>
                            جميع المعاملات آمنة ومشفرة
                        </li>
                        <li>
                            <i class="fas fa-headset text-primary me-2"></i>
                            للاستفسارات تواصل مع الدعم الفني
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-12 mb-3">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>الخيارات المتاحة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <i class="fas fa-print fa-3x text-primary mb-2"></i>
                            <p class="small mb-0 fw-bold">طباعة</p>
                            <small class="text-muted">طباعة مباشرة للفاتورة</small>
                        </div>
                        <div class="col-6 mb-3">
                            <i class="fas fa-file-excel fa-3x text-success mb-2"></i>
                            <p class="small mb-0 fw-bold">Excel</p>
                            <small class="text-muted">تحميل للتحليل والأرشفة</small>
                        </div>
                    </div>
                    <div class="alert alert-info small mb-0">
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>نصيحة:</strong> استخدم الطباعة للحصول على نسخة ورقية، و Excel للاحتفاظ بالبيانات رقمياً.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printInvoice(paymentId) {
    // فتح صفحة تفاصيل الفاتورة في نافذة جديدة للطباعة
    const printUrl = `/student/invoice/${paymentId}?print=1`;
    const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');

    // انتظار تحميل الصفحة ثم طباعة
    printWindow.onload = function() {
        setTimeout(function() {
            printWindow.print();
        }, 500);
    };
}
</script>
{% endblock %}
