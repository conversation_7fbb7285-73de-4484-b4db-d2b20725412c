{% extends "base.html" %}

{% block title %}إعدادات الأكاديمية - {{ academy_name }}{% endblock %}
{% block page_title %}إعدادات الأكاديمية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>إعدادات الأكاديمية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.settings') }}" id="settingsForm" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- Basic Information -->
                    <div class="mb-4">
                        <h6 class="text-primary border-bottom pb-2">المعلومات الأساسية</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academy_name" class="form-label">اسم الأكاديمية <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="academy_name" name="academy_name"
                                           value="{{ settings.academy_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academy_slogan" class="form-label">شعار الأكاديمية</label>
                                    <input type="text" class="form-control" id="academy_slogan" name="academy_slogan"
                                           value="{{ settings.academy_slogan or '' }}" placeholder="نحو تعلم أفضل للقرآن الكريم">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="website_url" class="form-label">رابط الموقع</label>
                                    <input type="url" class="form-control" id="website_url" name="website_url"
                                           value="{{ settings.website_url or '' }}" placeholder="https://example.com">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="language" class="form-label">اللغة الافتراضية</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="ar" {% if settings.language == 'ar' %}selected{% endif %}>العربية</option>
                                        <option value="en" {% if settings.language == 'en' %}selected{% endif %}>English</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="academy_description" class="form-label">وصف الأكاديمية</label>
                            <textarea class="form-control" id="academy_description" name="academy_description" 
                                      rows="3" placeholder="وصف مختصر عن الأكاديمية">{{ settings.academy_description or '' }}</textarea>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="mb-4">
                        <h6 class="text-primary border-bottom pb-2">معلومات التواصل</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email"
                                           value="{{ settings.contact_email or '' }}" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                           value="{{ settings.contact_phone or '' }}" placeholder="+966123456789">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_whatsapp" class="form-label">رقم الواتساب</label>
                                    <input type="tel" class="form-control" id="contact_whatsapp" name="contact_whatsapp"
                                           value="{{ settings.contact_whatsapp or '' }}" placeholder="+966123456789">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <option value="Asia/Riyadh" {% if settings.timezone == 'Asia/Riyadh' %}selected{% endif %}>الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai" {% if settings.timezone == 'Asia/Dubai' %}selected{% endif %}>دبي (GMT+4)</option>
                                        <option value="Africa/Cairo" {% if settings.timezone == 'Africa/Cairo' %}selected{% endif %}>القاهرة (GMT+2)</option>
                                        <option value="UTC" {% if settings.timezone == 'UTC' %}selected{% endif %}>UTC (GMT+0)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address"
                                      rows="2" placeholder="عنوان الأكاديمية">{{ settings.address or '' }}</textarea>
                        </div>
                    </div>

                    <!-- Social Media Links -->
                    <div class="mb-4">
                        <h6 class="text-primary border-bottom pb-2">روابط التواصل الاجتماعي</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="facebook_url" class="form-label">
                                        <i class="fab fa-facebook text-primary me-1"></i>فيسبوك
                                    </label>
                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                           value="{{ settings.facebook_url or '' }}" placeholder="https://facebook.com/academy">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="twitter_url" class="form-label">
                                        <i class="fab fa-twitter text-info me-1"></i>تويتر
                                    </label>
                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                           value="{{ settings.twitter_url or '' }}" placeholder="https://twitter.com/academy">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="instagram_url" class="form-label">
                                        <i class="fab fa-instagram text-danger me-1"></i>إنستغرام
                                    </label>
                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                           value="{{ settings.instagram_url or '' }}" placeholder="https://instagram.com/academy">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="youtube_url" class="form-label">
                                        <i class="fab fa-youtube text-danger me-1"></i>يوتيوب
                                    </label>
                                    <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                           value="{{ settings.youtube_url or '' }}" placeholder="https://youtube.com/academy">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Appearance Settings -->
                    <div class="mb-4">
                        <h6 class="text-primary border-bottom pb-2">إعدادات المظهر والألوان</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="primary_color" class="form-label">اللون الأساسي</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="primary_color"
                                               name="primary_color" value="{{ settings.primary_color or '#007bff' }}" onchange="updateColorPreview('primary')">
                                        <input type="text" class="form-control" id="primary_color_text"
                                               value="{{ settings.primary_color or '#007bff' }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="secondary_color" class="form-label">اللون الثانوي</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="secondary_color"
                                               name="secondary_color" value="{{ settings.secondary_color or '#6c757d' }}" onchange="updateColorPreview('secondary')">
                                        <input type="text" class="form-control" id="secondary_color_text"
                                               value="{{ settings.secondary_color or '#6c757d' }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="success_color" class="form-label">لون النجاح</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="success_color"
                                               name="success_color" value="{{ settings.success_color or '#28a745' }}" onchange="updateColorPreview('success')">
                                        <input type="text" class="form-control" id="success_color_text"
                                               value="{{ settings.success_color or '#28a745' }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="danger_color" class="form-label">لون الخطر</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="danger_color"
                                               name="danger_color" value="{{ settings.danger_color or '#dc3545' }}" onchange="updateColorPreview('danger')">
                                        <input type="text" class="form-control" id="danger_color_text"
                                               value="{{ settings.danger_color or '#dc3545' }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="warning_color" class="form-label">لون التحذير</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="warning_color"
                                               name="warning_color" value="{{ settings.warning_color or '#ffc107' }}" onchange="updateColorPreview('warning')">
                                        <input type="text" class="form-control" id="warning_color_text"
                                               value="{{ settings.warning_color or '#ffc107' }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="info_color" class="form-label">لون المعلومات</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="info_color"
                                               name="info_color" value="{{ settings.info_color or '#17a2b8' }}" onchange="updateColorPreview('info')">
                                        <input type="text" class="form-control" id="info_color_text"
                                               value="{{ settings.info_color or '#17a2b8' }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Color Preview -->
                        <div class="mb-3">
                            <label class="form-label">معاينة الألوان</label>
                            <div class="d-flex gap-2 flex-wrap">
                                <div class="text-center">
                                    <div class="color-preview" id="primary_preview"
                                         style="width: 50px; height: 50px; border-radius: 8px; background-color: {{ settings.primary_color or '#007bff' }};"></div>
                                    <small class="text-muted d-block">أساسي</small>
                                </div>
                                <div class="text-center">
                                    <div class="color-preview" id="secondary_preview"
                                         style="width: 50px; height: 50px; border-radius: 8px; background-color: {{ settings.secondary_color or '#6c757d' }};"></div>
                                    <small class="text-muted d-block">ثانوي</small>
                                </div>
                                <div class="text-center">
                                    <div class="color-preview" id="success_preview"
                                         style="width: 50px; height: 50px; border-radius: 8px; background-color: {{ settings.success_color or '#28a745' }};"></div>
                                    <small class="text-muted d-block">نجاح</small>
                                </div>
                                <div class="text-center">
                                    <div class="color-preview" id="danger_preview"
                                         style="width: 50px; height: 50px; border-radius: 8px; background-color: {{ settings.danger_color or '#dc3545' }};"></div>
                                    <small class="text-muted d-block">خطر</small>
                                </div>
                                <div class="text-center">
                                    <div class="color-preview" id="warning_preview"
                                         style="width: 50px; height: 50px; border-radius: 8px; background-color: {{ settings.warning_color or '#ffc107' }};"></div>
                                    <small class="text-muted d-block">تحذير</small>
                                </div>
                                <div class="text-center">
                                    <div class="color-preview" id="info_preview"
                                         style="width: 50px; height: 50px; border-radius: 8px; background-color: {{ settings.info_color or '#17a2b8' }};"></div>
                                    <small class="text-muted d-block">معلومات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- System Settings -->
                    <div class="mb-4">
                        <h6 class="text-primary border-bottom pb-2">إعدادات النظام</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="SAR" {{ 'selected' if settings.currency == 'SAR' else '' }}>ريال سعودي (SAR)</option>
                                        <option value="USD" {{ 'selected' if settings.currency == 'USD' else '' }}>دولار أمريكي (USD)</option>
                                        <option value="AED" {{ 'selected' if settings.currency == 'AED' else '' }}>درهم إماراتي (AED)</option>
                                        <option value="EGP" {{ 'selected' if settings.currency == 'EGP' else '' }}>جنيه مصري (EGP)</option>
                                        <option value="EUR" {{ 'selected' if settings.currency == 'EUR' else '' }}>يورو (EUR)</option>
                                        <option value="GBP" {{ 'selected' if settings.currency == 'GBP' else '' }}>جنيه إسترليني (GBP)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_format" class="form-label">تنسيق التاريخ</label>
                                    <select class="form-select" id="date_format" name="date_format">
                                        <option value="%Y-%m-%d" {{ 'selected' if settings.date_format == '%Y-%m-%d' else '' }}>2024-01-15</option>
                                        <option value="%d/%m/%Y" {{ 'selected' if settings.date_format == '%d/%m/%Y' else '' }}>15/01/2024</option>
                                        <option value="%d-%m-%Y" {{ 'selected' if settings.date_format == '%d-%m-%Y' else '' }}>15-01-2024</option>
                                        <option value="%B %d, %Y" {{ 'selected' if settings.date_format == '%B %d, %Y' else '' }}>January 15, 2024</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="time_format" class="form-label">تنسيق الوقت</label>
                                    <select class="form-select" id="time_format" name="time_format">
                                        <option value="%H:%M" {{ 'selected' if settings.time_format == '%H:%M' else '' }}>24 ساعة (14:30)</option>
                                        <option value="%I:%M %p" {{ 'selected' if settings.time_format == '%I:%M %p' else '' }}>12 ساعة (2:30 PM)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Logo Upload Section -->
                    <div class="mb-4">
                        <h6 class="text-primary border-bottom pb-2">شعار الأكاديمية</h6>
                        
                        <div class="mb-3">
                            <label for="logo_upload" class="form-label">رفع شعار جديد</label>
                            <input type="file" class="form-control" id="logo_upload" name="academy_logo" accept="image/*">
                            <div class="form-text">الحد الأقصى: 2MB. الصيغ المدعومة: JPG, PNG, SVG</div>
                        </div>
                        
                        <div class="current-logo">
                            {% if settings.academy_logo %}
                                <img src="{{ settings.academy_logo }}" alt="شعار الأكاديمية" class="img-thumbnail" style="max-height: 100px;">
                            {% else %}
                                <div class="text-center p-4 border rounded bg-light">
                                    <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">لم يتم رفع شعار بعد</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="previewChanges()">
                            <i class="fas fa-eye me-2"></i>معاينة التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Additional Settings Cards -->
<div class="row mt-4">
    <div class="col-lg-4">
        <div class="card h-100 shadow-sm border-primary" style="transition: transform 0.2s;">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>إعدادات الدفع
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">إدارة بوابات الدفع والعملات المقبولة</p>
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-circle text-success me-1"></i>Stripe
                        <i class="fas fa-circle text-secondary me-1 ms-2"></i>PayPal
                    </small>
                </div>
                <a href="{{ url_for('admin.payment_settings') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة الدفع
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card h-100 shadow-sm border-success" style="transition: transform 0.2s;">
            <div class="card-header bg-success text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-envelope me-2"></i>إعدادات البريد
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">تكوين خادم البريد الإلكتروني والقوالب</p>
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-circle text-warning me-1"></i>SMTP
                        <i class="fas fa-circle text-success me-1 ms-2"></i>إشعارات
                    </small>
                </div>
                <a href="{{ url_for('admin.email_settings') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة البريد
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card h-100 shadow-sm border-warning" style="transition: transform 0.2s;">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">إدارة كلمات المرور والصلاحيات</p>
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-circle text-success me-1"></i>كلمات مرور قوية
                        <i class="fas fa-circle text-success me-1 ms-2"></i>جلسات آمنة
                    </small>
                </div>
                <a href="{{ url_for('admin.security_settings') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة الأمان
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Session Settings Row -->
<div class="row mt-4">
    <div class="col-lg-4">
        <div class="card h-100 shadow-sm border-info" style="transition: transform 0.2s;">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-video me-2"></i>إعدادات الحصص
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">إدارة مزودي خدمات الحصص وتوليد الروابط</p>
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fab fa-google text-primary me-1"></i>Google Meet
                        <i class="fas fa-video text-info me-1 ms-2"></i>Jitsi
                        <i class="fas fa-video text-primary me-1 ms-2"></i>Zoom
                    </small>
                </div>
                <a href="{{ url_for('admin.session_settings') }}" class="btn btn-info btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة الحصص
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card h-100 shadow-sm border-primary" style="transition: transform 0.2s;">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fab fa-google me-2"></i>Google Calendar
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">ربط النظام مع Google Calendar لمزامنة الحصص</p>
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-calendar text-primary me-1"></i>مزامنة تلقائية
                        <i class="fas fa-bell text-warning me-1 ms-2"></i>تذكيرات
                    </small>
                </div>
                <a href="{{ url_for('admin.calendar_settings') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-cog me-1"></i>إدارة التقويم
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Color picker functionality
    document.getElementById('primary_color').addEventListener('input', function() {
        const color = this.value;
        document.getElementById('primary_color_text').value = color;
        document.getElementById('primary_preview').style.backgroundColor = color;
        
        // Apply color to CSS variables for live preview
        document.documentElement.style.setProperty('--primary-color', color);
    });
    
    document.getElementById('secondary_color').addEventListener('input', function() {
        const color = this.value;
        document.getElementById('secondary_color_text').value = color;
        document.getElementById('secondary_preview').style.backgroundColor = color;
        
        // Apply color to CSS variables for live preview
        document.documentElement.style.setProperty('--secondary-color', color);
    });
    
    // Logo upload preview
    document.getElementById('logo_upload').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 2 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 2MB');
                this.value = '';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const currentLogo = document.querySelector('.current-logo');
                currentLogo.innerHTML = `<img src="${e.target.result}" alt="شعار جديد" class="img-thumbnail" style="max-height: 100px;">`;
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Reset form function
    function resetForm() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
            document.getElementById('settingsForm').reset();
            location.reload();
        }
    }
    
    // Preview changes function
    function previewChanges() {
        const academyName = document.getElementById('academy_name').value;
        const primaryColor = document.getElementById('primary_color').value;
        const secondaryColor = document.getElementById('secondary_color').value;
        
        // Create preview modal
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: ${primaryColor}; color: white;">
                        <h5 class="modal-title">معاينة التغييرات</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h4 style="color: ${primaryColor};">${academyName}</h4>
                        <p>هذه معاينة لكيفية ظهور الألوان الجديدة في النظام.</p>
                        <div class="d-flex gap-2">
                            <button class="btn" style="background-color: ${primaryColor}; color: white;">زر أساسي</button>
                            <button class="btn" style="background-color: ${secondaryColor}; color: white;">زر ثانوي</button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Remove modal after hiding
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }
    
    // Form validation
    document.getElementById('settingsForm').addEventListener('submit', function(e) {
        const academyName = document.getElementById('academy_name').value.trim();
        
        if (!academyName) {
            e.preventDefault();
            alert('اسم الأكاديمية مطلوب');
            document.getElementById('academy_name').focus();
            return;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
        
        // Re-enable button after 3 seconds (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Add hover effects to settings cards
    document.querySelectorAll('.card[style*="transition"]').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Add click animation to buttons
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Color preview update functions
    function updateColorPreview(colorType) {
        const colorInput = document.getElementById(colorType + '_color');
        const colorText = document.getElementById(colorType + '_color_text');
        const colorPreview = document.getElementById(colorType + '_preview');

        if (colorInput && colorText && colorPreview) {
            const newColor = colorInput.value;
            colorText.value = newColor;
            colorPreview.style.backgroundColor = newColor;
        }
    }

    // Initialize color inputs
    document.addEventListener('DOMContentLoaded', function() {
        const colorTypes = ['primary', 'secondary', 'success', 'danger', 'warning', 'info'];

        colorTypes.forEach(function(type) {
            const colorInput = document.getElementById(type + '_color');
            const colorText = document.getElementById(type + '_color_text');

            if (colorInput && colorText) {
                // Sync color picker with text input
                colorInput.addEventListener('input', function() {
                    updateColorPreview(type);
                });

                // Allow manual text input
                colorText.addEventListener('input', function() {
                    if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                        colorInput.value = this.value;
                        updateColorPreview(type);
                    }
                });

                // Make text input editable
                colorText.removeAttribute('readonly');
            }
        });
    });
</script>

<style>
    .btn {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .card[style*="transition"]:hover {
        cursor: pointer;
    }
</style>
{% endblock %}
