{% extends "base.html" %}

{% block title %}إدارة الحصص - {{ academy_name }}{% endblock %}
{% block page_title %}إدارة الحصص{% endblock %}



{% block content %}
<!-- Filters and Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">حالة الحصة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="scheduled" {{ 'selected' if status_filter == 'scheduled' }}>مجدولة</option>
                                    <option value="completed" {{ 'selected' if status_filter == 'completed' }}>مكتملة</option>
                                    <option value="cancelled" {{ 'selected' if status_filter == 'cancelled' }}>ملغية</option>
                                    <option value="missed" {{ 'selected' if status_filter == 'missed' }}>فائتة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="type" class="form-label">نوع الحصة</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="trial" {{ 'selected' if type_filter == 'trial' }}>تجريبية</option>
                                    <option value="makeup" {{ 'selected' if type_filter == 'makeup' }}>تعويضية</option>
                                    <option value="scheduled" {{ 'selected' if type_filter == 'scheduled' }}>مجدولة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>تطبيق الفلتر
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="{{ url_for('admin.sessions') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>إزالة الفلاتر
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-lg-4">
                        <div class="d-grid">
                            <a href="{{ url_for('admin.new_session') }}" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>جدولة حصة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-primary text-uppercase mb-1">حصص مجدولة</div>
                        <div class="h5 mb-0">{{ sessions.items|selectattr('status', 'equalto', 'scheduled')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">حصص مكتملة</div>
                        <div class="h5 mb-0">{{ sessions.items|selectattr('status', 'equalto', 'completed')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-danger border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-danger text-uppercase mb-1">حصص ملغية</div>
                        <div class="h5 mb-0">{{ sessions.items|selectattr('status', 'equalto', 'cancelled')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">إجمالي الحصص</div>
                        <div class="h5 mb-0">{{ sessions.total }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-list fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sessions Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>قائمة الحصص
                </h5>
                <span class="badge bg-primary">{{ sessions.total }} حصة</span>
            </div>
            <div class="card-body">
                {% if sessions.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المعلم</th>
                                <th>الطالب</th>
                                <th>النوع</th>
                                <th>التاريخ والوقت</th>
                                <th>المدة</th>
                                <th>الحالة</th>
                                <th>الحضور</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in sessions.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 12px;">
                                            {{ session.teacher.first_name[0] if session.teacher.first_name else 'T' }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ session.teacher.full_name }}</div>
                                            <div class="small text-muted">{{ session.teacher.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 12px;">
                                            {{ session.student.first_name[0] if session.student.first_name else 'S' }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ session.student.full_name }}</div>
                                            <div class="small text-muted">{{ session.student.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {% if session.session_type == 'trial' %}تجريبية
                                        {% elif session.session_type == 'makeup' %}تعويضية
                                        {% else %}مجدولة{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <div class="fw-bold">{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}</div>
                                    <div class="small text-muted">{{ session.scheduled_datetime.strftime('%H:%M') }}</div>
                                </td>
                                <td>{{ session.duration_minutes }} دقيقة</td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                                        {% if session.status == 'completed' %}مكتملة
                                        {% elif session.status == 'scheduled' %}مجدولة
                                        {% elif session.status == 'cancelled' %}ملغية
                                        {% else %}فائتة{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if session.status in ['scheduled', 'completed'] %}
                                        <div class="small">
                                            <div>معلم:
                                                {% if session.teacher_attended %}
                                                    <span class="badge bg-success">نعم</span>
                                                {% elif session.status == 'scheduled' %}
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                {% else %}
                                                    <span class="badge bg-danger">لا</span>
                                                {% endif %}
                                            </div>
                                            <div>طالب:
                                                {% if session.student_attended %}
                                                    <span class="badge bg-success">نعم</span>
                                                {% elif session.status == 'scheduled' %}
                                                    <span class="badge bg-warning">في الانتظار</span>
                                                {% else %}
                                                    <span class="badge bg-danger">لا</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="small">
                                            <div>معلم: <span class="badge bg-secondary">غير متاح</span></div>
                                            <div>طالب: <span class="badge bg-secondary">غير متاح</span></div>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" data-bs-target="#sessionModal{{ session.id }}"
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        {% if session.status == 'scheduled' %}
                                        <button type="button" class="btn btn-sm btn-outline-warning"
                                                onclick="updateSessionStatus({{ session.id }}, 'cancelled')"
                                                title="إلغاء الحصة">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                        
                                        <button type="button" class="btn btn-sm btn-outline-info"
                                                onclick="editSession({{ session.id }})"
                                                title="تعديل الحصة">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        {% if session.status == 'scheduled' %}
                                        <a href="{{ url_for('admin.session_meeting_setup', session_id=session.id) }}"
                                           class="btn btn-sm btn-outline-success"
                                           title="إعداد رابط الحصة">
                                            <i class="fas fa-video"></i>
                                        </a>
                                        {% endif %}

                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="confirmDeleteSession({{ session.id }}, '{{ session.teacher.full_name }}', '{{ session.student.full_name }}', '{{ session.scheduled_datetime.strftime('%Y-%m-%d %H:%M') }}')"
                                                title="حذف الحصة نهائياً">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if sessions.pages > 1 %}
                <nav aria-label="صفحات الحصص">
                    <ul class="pagination justify-content-center">
                        {% if sessions.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.sessions', page=sessions.prev_num, status=status_filter, type=type_filter) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in sessions.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != sessions.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.sessions', page=page_num, status=status_filter, type=type_filter) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if sessions.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.sessions', page=sessions.next_num, status=status_filter, type=type_filter) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد حصص</h5>
                    <p class="text-muted">لم يتم العثور على حصص بالمعايير المحددة.</p>
                    <a href="{{ url_for('admin.new_session') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>جدولة حصة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Session Details Modals -->
{% for session in sessions.items %}
<div class="modal fade" id="sessionModal{{ session.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الحصة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الحصة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المعلم:</strong></td>
                                <td>{{ session.teacher.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الطالب:</strong></td>
                                <td>{{ session.student.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>النوع:</strong></td>
                                <td>
                                    {% if session.session_type == 'trial' %}تجريبية
                                    {% elif session.session_type == 'makeup' %}تعويضية
                                    {% else %}مجدولة{% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>{{ session.scheduled_datetime.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>المدة:</strong></td>
                                <td>{{ session.duration_minutes }} دقيقة</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>حالة الحصة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                                        {% if session.status == 'completed' %}مكتملة
                                        {% elif session.status == 'scheduled' %}مجدولة
                                        {% elif session.status == 'cancelled' %}ملغية
                                        {% else %}فائتة{% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% if session.status == 'completed' %}
                            <tr>
                                <td><strong>حضور المعلم:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.teacher_attended else 'bg-danger' }}">
                                        {{ 'نعم' if session.teacher_attended else 'لا' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حضور الطالب:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.student_attended else 'bg-danger' }}">
                                        {{ 'نعم' if session.student_attended else 'لا' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإكمال:</strong></td>
                                <td>{{ session.completed_at.strftime('%Y-%m-%d %H:%M') if session.completed_at else '-' }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if session.subscription %}
                <hr>
                <h6>معلومات الاشتراك</h6>
                <p><strong>الباقة:</strong> {{ session.subscription.package.name }}</p>
                <p><strong>الحصص المتبقية:</strong> {{ session.subscription.sessions_remaining or 0 }}</p>
                {% endif %}
                
                {% if session.notes %}
                <hr>
                <h6>ملاحظات المعلم</h6>
                <p>{{ session.notes }}</p>
                {% endif %}
                
                {% if session.ratings %}
                <hr>
                <h6>تقييم الطالب</h6>
                {% set rating = session.ratings[0] %}
                <div class="d-flex align-items-center mb-2">
                    <span class="me-2">التقييم:</span>
                    {% for i in range(1, 6) %}
                        <i class="fas fa-star {{ 'text-warning' if i <= rating.rating else 'text-muted' }}"></i>
                    {% endfor %}
                    <span class="ms-2">({{ rating.rating }}/5)</span>
                </div>
                {% if rating.comment %}
                <p><strong>التعليق:</strong> {{ rating.comment }}</p>
                {% endif %}
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                {% if session.status == 'scheduled' %}
                <button type="button" class="btn btn-warning" 
                        onclick="updateSessionStatus({{ session.id }}, 'cancelled')">
                    إلغاء الحصة
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('#status, #type').forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // Update session status
    function updateSessionStatus(sessionId, newStatus) {
        const action = newStatus === 'cancelled' ? 'إلغاء' : 'تحديث';
        
        if (confirm(`هل أنت متأكد من ${action} هذه الحصة؟`)) {
            fetch(`/api/sessions/${sessionId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء تحديث الحصة');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحديث الحصة');
            });
        }
    }
    
    // Edit session function
    function editSession(sessionId) {
        // Redirect to edit page
        window.location.href = `/admin/sessions/${sessionId}/edit`;
    }

    // Delete session function with confirmation
    function confirmDeleteSession(sessionId, teacherName, studentName, scheduledDateTime) {
        // Create confirmation modal
        const modalHtml = `
            <div class="modal fade" id="deleteSessionModal" tabindex="-1" aria-labelledby="deleteSessionModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="deleteSessionModalLabel">
                                <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الحصة
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-warning me-2"></i>
                                <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                            </div>

                            <p class="mb-3">هل أنت متأكد من رغبتك في حذف هذه الحصة نهائياً؟</p>

                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">تفاصيل الحصة:</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>المعلم:</strong> ${teacherName}</li>
                                        <li><strong>الطالب:</strong> ${studentName}</li>
                                        <li><strong>التاريخ والوقت:</strong> ${scheduledDateTime}</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mt-3">
                                <h6>سيتم حذف:</h6>
                                <ul class="text-muted small">
                                    <li>الحصة من جميع لوحات التحكم</li>
                                    <li>جميع التقييمات المرتبطة بالحصة</li>
                                    <li>جميع التذكيرات المرتبطة بالحصة</li>
                                    <li>الحدث من Google Calendar (إن وجد)</li>
                                    <li>سيتم إشعار المعلم والطالب بالحذف</li>
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteSession(${sessionId})">
                                <i class="fas fa-trash me-1"></i>حذف نهائياً
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('deleteSessionModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('deleteSessionModal'));
        modal.show();

        // Clean up modal after hiding
        document.getElementById('deleteSessionModal').addEventListener('hidden.bs.modal', function () {
            this.remove();
        });
    }

    // Actually delete the session
    function deleteSession(sessionId) {
        // Show loading state
        const deleteBtn = document.querySelector('#deleteSessionModal .btn-danger');
        const originalText = deleteBtn.innerHTML;
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحذف...';
        deleteBtn.disabled = true;

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        const headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken.getAttribute('content');
        }

        fetch(`/admin/sessions/${sessionId}/delete`, {
            method: 'POST',
            headers: headers
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteSessionModal'));
                modal.hide();

                // Show success message
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // Add alert to top of page
                const container = document.querySelector('.container-fluid');
                container.insertAdjacentHTML('afterbegin', alertHtml);

                // Reload page after short delay
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                // Show error message
                alert('خطأ: ' + data.message);

                // Reset button
                deleteBtn.innerHTML = originalText;
                deleteBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الحصة');

            // Reset button
            deleteBtn.innerHTML = originalText;
            deleteBtn.disabled = false;
        });
    }
</script>
{% endblock %}
