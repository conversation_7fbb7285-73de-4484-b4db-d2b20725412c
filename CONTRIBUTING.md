# 🤝 دليل المساهمة في Quran LMS

نرحب بمساهماتكم في تطوير نظام إدارة تعلم القرآن الكريم! هذا الدليل سيساعدكم على البدء.

## 📋 كيفية المساهمة

### 1. 🍴 Fork المشروع
```bash
# انقر على زر Fork في GitHub
# ثم استنسخ المشروع المنسوخ
git clone https://github.com/yourusername/quranlms.git
cd quranlms
```

### 2. 🌿 إنشاء Branch جديد
```bash
git checkout -b feature/amazing-feature
# أو
git checkout -b fix/bug-description
```

### 3. 🔧 إعداد بيئة التطوير
```bash
# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# تثبيت المتطلبات
pip install -r requirements.txt

# إعداد متغيرات البيئة
cp .env.example .env
# قم بتحرير .env

# تهيئة قاعدة البيانات
python production_setup.py
```

### 4. ✨ إضافة التحسينات
- اكتب كود نظيف ومفهوم
- أضف تعليقات باللغة العربية
- اتبع معايير Python PEP 8
- اختبر التغييرات محلياً

### 5. 📝 Commit التغييرات
```bash
git add .
git commit -m "feat: إضافة ميزة رائعة جديدة"
# أو
git commit -m "fix: إصلاح خطأ في النظام"
```

### 6. 🚀 Push إلى Branch
```bash
git push origin feature/amazing-feature
```

### 7. 🔄 إنشاء Pull Request
- اذهب إلى GitHub
- اضغط "New Pull Request"
- اكتب وصف واضح للتغييرات
- أضف screenshots إذا كانت التغييرات في الواجهة

## 📏 معايير الكود

### Python
```python
# استخدم أسماء متغيرات واضحة
user_name = "أحمد محمد"  # ✅ جيد
un = "أحمد محمد"         # ❌ سيء

# أضف تعليقات باللغة العربية
def calculate_total_price(package_price, discount=0):
    """حساب السعر الإجمالي بعد الخصم"""
    return package_price * (1 - discount)

# استخدم Type Hints
def get_user_by_id(user_id: int) -> User:
    return User.query.get(user_id)
```

### HTML/CSS
```html
<!-- استخدم أسماء classes واضحة -->
<div class="student-dashboard-card">  <!-- ✅ جيد -->
<div class="sdc">                     <!-- ❌ سيء -->

<!-- أضف تعليقات للأقسام المهمة -->
<!-- قسم إحصائيات الطالب -->
<div class="stats-section">
    ...
</div>
```

### JavaScript
```javascript
// استخدم const/let بدلاً من var
const studentId = 123;  // ✅ جيد
var studentId = 123;    // ❌ سيء

// أضف تعليقات للوظائف المعقدة
/**
 * تحديث إحصائيات الطالب في الوقت الفعلي
 */
function updateStudentStats() {
    // ...
}
```

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ
1. تأكد من أن الخطأ لم يُبلغ عنه مسبقاً
2. جرب إعادة إنتاج الخطأ
3. اجمع معلومات مفيدة

### معلومات مطلوبة
```markdown
**وصف الخطأ:**
وصف واضح ومختصر للخطأ

**خطوات إعادة الإنتاج:**
1. اذهب إلى '...'
2. اضغط على '...'
3. انتقل إلى '...'
4. شاهد الخطأ

**السلوك المتوقع:**
وصف واضح لما كان يجب أن يحدث

**Screenshots:**
إذا كان ممكناً، أضف screenshots

**معلومات البيئة:**
- OS: [e.g. Windows 10]
- Browser: [e.g. Chrome 91]
- Python Version: [e.g. 3.11]
```

## 💡 اقتراح ميزات جديدة

### قبل الاقتراح
1. تأكد من أن الميزة لم تُقترح مسبقاً
2. فكر في الفائدة للمستخدمين
3. اعتبر التعقيد والصيانة

### تنسيق الاقتراح
```markdown
**هل اقتراحك مرتبط بمشكلة؟**
وصف واضح للمشكلة. مثال: أشعر بالإحباط عندما [...]

**وصف الحل المقترح:**
وصف واضح ومختصر لما تريده أن يحدث

**وصف البدائل:**
وصف واضح لأي حلول أو ميزات بديلة فكرت فيها

**سياق إضافي:**
أضف أي سياق أو screenshots أخرى حول طلب الميزة هنا
```

## 🏷️ أنواع المساهمات

### 🐛 إصلاح الأخطاء
- أخطاء في الكود
- مشاكل في الواجهة
- أخطاء في قاعدة البيانات

### ✨ ميزات جديدة
- تحسينات في الواجهة
- وظائف جديدة
- تكاملات خارجية

### 📚 التوثيق
- تحسين README
- إضافة تعليقات
- كتابة أدلة الاستخدام

### 🎨 التصميم
- تحسين UI/UX
- إضافة رموز
- تحسين الألوان

### 🔧 التحسينات
- تحسين الأداء
- تنظيف الكود
- إضافة اختبارات

## 📞 التواصل

- **GitHub Issues**: للأخطاء والاقتراحات
- **GitHub Discussions**: للنقاشات العامة
- **Email**: للاستفسارات الخاصة

## 🙏 شكر وتقدير

شكراً لكم على اهتمامكم بالمساهمة في تطوير نظام تعليم القرآن الكريم. كل مساهمة، مهما كانت صغيرة، تساعد في تحسين التعليم وخدمة المجتمع.

---

**جزاكم الله خيراً على مساهماتكم! 🤲**
