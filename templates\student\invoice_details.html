{% extends "base.html" %}

{% block title %}تفاصيل الفاتورة #{{ payment.id }} - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}تفاصيل الفاتورة #{{ payment.id }}{% endblock %}

{% block extra_css %}
<style>
/* تحسينات متجاوبة لتفاصيل الفاتورة */
@media (max-width: 768px) {
    .btn-group {
        width: 100%;
    }

    .btn-group .btn {
        font-size: 0.875rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .h5 {
        font-size: 1.125rem;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }

    .table-borderless td {
        padding: 0.25rem 0;
    }

    .badge {
        font-size: 0.75rem;
    }

    code {
        font-size: 0.75rem;
        word-break: break-all;
    }
}

/* تحسين الطباعة */
@media print {
    .btn, .dropdown, .navbar, .sidebar, .breadcrumb {
        display: none !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 1rem !important;
    }

    .badge {
        border: 1px solid #000;
        color: #000 !important;
        background-color: transparent !important;
    }

    .text-success {
        color: #000 !important;
    }

    .text-muted {
        color: #666 !important;
    }

    body {
        font-size: 12pt;
    }

    @page {
        margin: 1cm;
    }
}

/* تحسين التخطيط */
.table-borderless td {
    vertical-align: top;
}

.card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- أزرار التنقل والإجراءات -->
    <div class="row mb-4">
        <div class="col-lg-6 col-md-12 mb-2">
            <a href="{{ url_for('student.invoices') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للفواتير
            </a>
        </div>
        <div class="col-lg-6 col-md-12 text-lg-end">
            {% if payment.status == 'completed' %}
            <div class="btn-group w-100 w-lg-auto" role="group">
                <button type="button" class="btn btn-primary" onclick="printInvoice()">
                    <i class="fas fa-print me-2"></i>طباعة الفاتورة
                </button>
                <a href="{{ url_for('student.download_invoice', payment_id=payment.id, format='excel') }}"
                   class="btn btn-success">
                    <i class="fas fa-file-excel me-2"></i>تحميل Excel
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- معلومات الفاتورة الأساسية -->
    <div class="row">
        <div class="col-xl-8 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-invoice-dollar me-2"></i>تفاصيل الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الفاتورة -->
                    <div class="row mb-4">
                        <div class="col-lg-6 col-md-12 mb-3">
                            <h6 class="text-muted">معلومات الفاتورة</h6>
                            <div class="table-responsive">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td class="fw-bold">رقم الفاتورة:</td>
                                        <td>#{{ payment.id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">تاريخ الإنشاء:</td>
                                        <td>{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">الحالة:</td>
                                        <td>
                                            {% if payment.status == 'completed' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>مكتمل
                                                </span>
                                            {% elif payment.status == 'pending' %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>معلق
                                                </span>
                                            {% elif payment.status == 'failed' %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>فشل
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ payment.status }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-12 mb-3">
                            <h6 class="text-muted">معلومات الدفع</h6>
                            <div class="table-responsive">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td class="fw-bold">المبلغ:</td>
                                        <td><span class="h5 text-success">{{ format_currency(payment.amount) }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">وسيلة الدفع:</td>
                                        <td>
                                            <span class="badge bg-info">{{ payment.payment_method }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">رقم المعاملة:</td>
                                        <td>
                                            {% if payment.transaction_id %}
                                                <code class="small">{{ payment.transaction_id }}</code>
                                            {% else %}
                                                <span class="text-muted">غير متوفر</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- تفاصيل الباقة -->
                    {% if payment.subscription and payment.subscription.package %}
                    <div class="mb-4">
                        <h6 class="text-muted">تفاصيل الباقة</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless mb-0">
                                            <tr>
                                                <td><strong>اسم الباقة:</strong></td>
                                                <td>{{ payment.subscription.package.name }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>عدد الحصص:</strong></td>
                                                <td>{{ payment.subscription.package.sessions_count }} حصة</td>
                                            </tr>
                                            <tr>
                                                <td><strong>مدة الباقة:</strong></td>
                                                <td>{{ payment.subscription.package.duration_days }} يوم</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless mb-0">
                                            <tr>
                                                <td><strong>حالة الاشتراك:</strong></td>
                                                <td>
                                                    {% if payment.subscription.status == 'active' %}
                                                        <span class="badge bg-success">نشط</span>
                                                    {% elif payment.subscription.status == 'pending' %}
                                                        <span class="badge bg-warning">معلق</span>
                                                    {% elif payment.subscription.status == 'expired' %}
                                                        <span class="badge bg-danger">منتهي</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">{{ payment.subscription.status }}</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ البداية:</strong></td>
                                                <td>
                                                    {% if payment.subscription.start_date %}
                                                        {{ payment.subscription.start_date.strftime('%Y-%m-%d') }}
                                                    {% else %}
                                                        <span class="text-muted">غير محدد</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ الانتهاء:</strong></td>
                                                <td>
                                                    {% if payment.subscription.end_date %}
                                                        {{ payment.subscription.end_date.strftime('%Y-%m-%d') }}
                                                    {% else %}
                                                        <span class="text-muted">غير محدد</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- معلومات إضافية -->
                    {% if payment.gateway or payment.notes %}
                    <hr>
                    <div class="mb-4">
                        <h6 class="text-muted">معلومات إضافية</h6>
                        <table class="table table-borderless">
                            {% if payment.gateway %}
                            <tr>
                                <td><strong>بوابة الدفع:</strong></td>
                                <td>{{ payment.gateway }}</td>
                            </tr>
                            {% endif %}
                            {% if payment.notes %}
                            <tr>
                                <td><strong>ملاحظات:</strong></td>
                                <td>{{ payment.notes }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-xl-4 col-lg-12">
            <div class="row">
                <!-- معلومات العميل -->
                <div class="col-lg-6 col-xl-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>معلومات العميل
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-user-circle fa-3x text-muted"></i>
                                <h6 class="mt-2 mb-0">{{ payment.user.full_name }}</h6>
                                <small class="text-muted">{{ payment.user.email }}</small>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-borderless table-sm">
                                    {% if payment.user.phone %}
                                    <tr>
                                        <td><i class="fas fa-phone text-muted me-2"></i></td>
                                        <td>{{ payment.user.phone }}</td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td><i class="fas fa-calendar text-muted me-2"></i></td>
                                        <td>عضو منذ {{ payment.user.created_at.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إجراءات سريعة -->
                <div class="col-lg-6 col-xl-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                {% if payment.subscription %}
                                <a href="{{ url_for('student.subscriptions') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-credit-card me-2"></i>عرض الاشتراك
                                </a>
                                {% endif %}

                                <a href="{{ url_for('student.invoices') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-list me-2"></i>جميع الفواتير
                                </a>

                                <a href="{{ url_for('student.packages') }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-shopping-cart me-2"></i>شراء باقة جديدة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printInvoice() {
    // إخفاء العناصر غير المطلوبة للطباعة
    const elementsToHide = [
        '.btn', '.dropdown', '.navbar', '.sidebar', '.top-nav',
        '.breadcrumb', '.alert', '.card-header .btn-group'
    ];

    elementsToHide.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            el.style.display = 'none';
        });
    });

    // إضافة أنماط الطباعة
    const printStyles = `
        <style media="print">
            @page { margin: 1cm; }
            body { font-size: 12pt; }
            .card { border: 1px solid #ddd !important; box-shadow: none !important; }
            .badge { border: 1px solid #000; }
            .text-success { color: #000 !important; }
            .text-muted { color: #666 !important; }
            .btn, .dropdown, .navbar, .sidebar { display: none !important; }
        </style>
    `;

    document.head.insertAdjacentHTML('beforeend', printStyles);

    // طباعة
    window.print();

    // إعادة إظهار العناصر بعد الطباعة
    setTimeout(() => {
        elementsToHide.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.display = '';
            });
        });
    }, 1000);
}

// إذا كان هناك معامل print في URL، اطبع تلقائياً
if (window.location.search.includes('print=1')) {
    setTimeout(printInvoice, 500);
}
</script>
{% endblock %}
