{% extends "base.html" %}

{% block title %}إعدادات الأمان - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}إعدادات الأمان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('admin.settings') }}">الإعدادات</a></li>
                <li class="breadcrumb-item active" aria-current="page">إعدادات الأمان</li>
            </ol>
        </nav>

        <!-- Back Button -->
        <div class="mb-3">
            <a href="{{ url_for('admin.settings') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
            </a>
        </div>

        <form method="POST" action="{{ url_for('admin.security_settings') }}" id="securitySettingsForm">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            
            <!-- Password Policy -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i>سياسة كلمات المرور
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="min_password_length" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                <input type="number" class="form-control" id="min_password_length" name="min_password_length" 
                                       value="{{ security_config.min_password_length }}" min="6" max="20">
                                <div class="form-text">يُنصح بـ 8 أحرف على الأقل</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="require_uppercase" name="require_uppercase" 
                                       {% if security_config.require_uppercase %}checked{% endif %}>
                                <label class="form-check-label" for="require_uppercase">
                                    يجب أن تحتوي على أحرف كبيرة (A-Z)
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="require_lowercase" name="require_lowercase" 
                                       {% if security_config.require_lowercase %}checked{% endif %}>
                                <label class="form-check-label" for="require_lowercase">
                                    يجب أن تحتوي على أحرف صغيرة (a-z)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="require_numbers" name="require_numbers" 
                                       {% if security_config.require_numbers %}checked{% endif %}>
                                <label class="form-check-label" for="require_numbers">
                                    يجب أن تحتوي على أرقام (0-9)
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="require_special_chars" name="require_special_chars" 
                                       {% if security_config.require_special_chars %}checked{% endif %}>
                                <label class="form-check-label" for="require_special_chars">
                                    يجب أن تحتوي على رموز خاصة (!@#$%^&*)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>مثال على كلمة مرور قوية:</strong> MyPassword123!
                    </div>
                </div>
            </div>

            <!-- Session Management -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>إدارة الجلسات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="session_timeout" class="form-label">انتهاء صلاحية الجلسة (بالدقائق)</label>
                                <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                       value="{{ security_config.session_timeout }}" min="5" max="1440">
                                <div class="form-text">المدة قبل تسجيل الخروج التلقائي (30 دقيقة مُنصح بها)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> الجلسات القصيرة جداً قد تؤثر على تجربة المستخدم، بينما الجلسات الطويلة قد تقلل من الأمان.
                    </div>
                </div>
            </div>

            <!-- Login Security -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>أمان تسجيل الدخول
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_login_attempts" class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                       value="{{ security_config.max_login_attempts }}" min="3" max="10">
                                <div class="form-text">عدد المحاولات المسموحة قبل قفل الحساب</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_lockout_duration" class="form-label">مدة قفل الحساب (بالدقائق)</label>
                                <input type="number" class="form-control" id="account_lockout_duration" name="account_lockout_duration" 
                                       value="{{ security_config.account_lockout_duration }}" min="5" max="60">
                                <div class="form-text">المدة التي يبقى فيها الحساب مقفلاً</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Features -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>ميزات الأمان الإضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enable_audit_log" name="enable_audit_log"
                                       {% if security_config.enable_audit_log %}checked{% endif %}>
                                <label class="form-check-label" for="enable_audit_log">
                                    <strong>تسجيل العمليات (Audit Log)</strong>
                                    <div class="text-muted small">تسجيل جميع العمليات المهمة في النظام</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enable_ip_tracking" name="enable_ip_tracking"
                                       {% if security_config.enable_ip_tracking %}checked{% endif %}>
                                <label class="form-check-label" for="enable_ip_tracking">
                                    <strong>تتبع عناوين IP</strong>
                                    <div class="text-muted small">تسجيل عناوين IP لتسجيلات الدخول</div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="enable_email_notifications" name="enable_email_notifications"
                                       {% if security_config.enable_email_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="enable_email_notifications">
                                    <strong>إشعارات الأمان</strong>
                                    <div class="text-muted small">إرسال إشعارات عند الأنشطة المشبوهة</div>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="force_password_change" name="force_password_change"
                                       {% if security_config.force_password_change %}checked{% endif %}>
                                <label class="form-check-label" for="force_password_change">
                                    <strong>إجبار تغيير كلمة المرور</strong>
                                    <div class="text-muted small">إجبار المستخدمين على تغيير كلمة المرور كل 90 يوم</div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>حالة الأمان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="display-6 text-success">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h6 class="mt-2">مستوى الأمان</h6>
                                <span class="badge bg-success">عالي</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="display-6 text-info">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h6 class="mt-2">المستخدمون النشطون</h6>
                                <span class="badge bg-info">{{ active_users_count or 0 }}</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="display-6 text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <h6 class="mt-2">تنبيهات الأمان</h6>
                                <span class="badge bg-warning">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Recommendations -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>توصيات الأمان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    استخدم كلمات مرور قوية ومعقدة
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    فعّل تسجيل العمليات لمراقبة النشاطات
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    راجع سجلات تسجيل الدخول بانتظام
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-exclamation-circle text-warning me-3"></i>
                                    فكر في تفعيل المصادقة الثنائية (قريباً)
                                </li>
                                <li class="list-group-item d-flex align-items-center">
                                    <i class="fas fa-exclamation-circle text-warning me-3"></i>
                                    قم بعمل نسخ احتياطية منتظمة للبيانات
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i>إعدادات ذات صلة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('admin.payment_settings') }}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-credit-card me-2"></i>إعدادات الدفع
                                <div class="small text-muted">لحماية المعاملات</div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ url_for('admin.email_settings') }}" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-envelope me-2"></i>إعدادات البريد
                                <div class="small text-muted">لإشعارات الأمان</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>حفظ إعدادات الأمان
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation
    document.getElementById('securitySettingsForm').addEventListener('submit', function(e) {
        const minLength = parseInt(document.getElementById('min_password_length').value);
        const sessionTimeout = parseInt(document.getElementById('session_timeout').value);
        const maxAttempts = parseInt(document.getElementById('max_login_attempts').value);
        const lockoutDuration = parseInt(document.getElementById('account_lockout_duration').value);
        
        // Validate password length
        if (minLength < 6 || minLength > 20) {
            e.preventDefault();
            alert('طول كلمة المرور يجب أن يكون بين 6 و 20 حرف');
            return;
        }
        
        // Validate session timeout
        if (sessionTimeout < 5 || sessionTimeout > 1440) {
            e.preventDefault();
            alert('مدة انتهاء الجلسة يجب أن تكون بين 5 دقائق و 24 ساعة');
            return;
        }
        
        // Validate login attempts
        if (maxAttempts < 3 || maxAttempts > 10) {
            e.preventDefault();
            alert('عدد محاولات تسجيل الدخول يجب أن يكون بين 3 و 10');
            return;
        }
        
        // Validate lockout duration
        if (lockoutDuration < 5 || lockoutDuration > 60) {
            e.preventDefault();
            alert('مدة قفل الحساب يجب أن تكون بين 5 و 60 دقيقة');
            return;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
        
        // Re-enable button after 3 seconds (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Password strength indicator
    function updatePasswordStrength() {
        const minLength = parseInt(document.getElementById('min_password_length').value);
        const requireUpper = document.getElementById('require_uppercase').checked;
        const requireLower = document.getElementById('require_lowercase').checked;
        const requireNumbers = document.getElementById('require_numbers').checked;
        const requireSpecial = document.getElementById('require_special_chars').checked;
        
        let strength = 0;
        if (minLength >= 8) strength++;
        if (requireUpper) strength++;
        if (requireLower) strength++;
        if (requireNumbers) strength++;
        if (requireSpecial) strength++;
        
        // Update UI based on strength (you can add visual indicators here)
        console.log('Password strength level:', strength);
    }

    // Add event listeners for real-time updates
    document.getElementById('min_password_length').addEventListener('change', updatePasswordStrength);
    document.getElementById('require_uppercase').addEventListener('change', updatePasswordStrength);
    document.getElementById('require_lowercase').addEventListener('change', updatePasswordStrength);
    document.getElementById('require_numbers').addEventListener('change', updatePasswordStrength);
    document.getElementById('require_special_chars').addEventListener('change', updatePasswordStrength);

    // Initialize
    updatePasswordStrength();
</script>
{% endblock %}
