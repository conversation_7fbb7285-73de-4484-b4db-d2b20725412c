#!/bin/bash
set -e

echo "🐍 Python version:"
python --version

echo "📦 Upgrading pip..."
python -m pip install --upgrade pip

echo "🔧 Installing setuptools and wheel..."
pip install setuptools>=65.0.0 wheel>=0.38.0

echo "📋 Installing requirements..."
pip install --no-cache-dir -r requirements.txt

echo "🚀 Running production setup..."
python production_setup.py

echo "✅ Build completed successfully!"
