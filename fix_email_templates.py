#!/usr/bin/env python3
"""
إصلاح وتهيئة جميع قوالب البريد الإلكتروني
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_email_templates():
    """إصلاح وتهيئة جميع قوالب البريد الإلكتروني"""
    
    print("🔧 إصلاح قوالب البريد الإلكتروني...")
    print("=" * 60)
    
    try:
        from app import app, db
        from models import EmailTemplate, EmailSettings
        
        with app.app_context():
            # 1. تهيئة القوالب الأساسية
            print("1️⃣ تهيئة القوالب الأساسية...")
            from utils.init_templates import init_email_templates
            success1, message1 = init_email_templates()
            print(f"   {message1}")
            
            # 2. تهيئة القوالب الإضافية
            print("2️⃣ تهيئة القوالب الإضافية...")
            from utils.init_templates import create_additional_templates
            success2, message2 = create_additional_templates()
            print(f"   {message2}")
            
            # 3. تهيئة قوالب إدارة المستخدمين
            print("3️⃣ تهيئة قوالب إدارة المستخدمين...")
            from utils.init_templates import init_user_management_templates
            success3, message3 = init_user_management_templates()
            print(f"   {message3}")
            
            # 4. تهيئة جميع القوالب من auto_install
            print("4️⃣ تهيئة القوالب التلقائية...")
            try:
                from utils.auto_install_templates import auto_install_all_templates
                auto_install_all_templates()
                print("   ✅ تم تثبيت القوالب التلقائية")
            except Exception as e:
                print(f"   ⚠️ تحذير في القوالب التلقائية: {str(e)}")
            
            # 5. التأكد من إعدادات البريد الإلكتروني
            print("5️⃣ فحص إعدادات البريد الإلكتروني...")
            email_settings = EmailSettings.query.first()
            if not email_settings:
                print("   📮 إنشاء إعدادات البريد الإلكتروني...")
                email_settings = EmailSettings(
                    enabled=True,
                    smtp_server='smtp.gmail.com',
                    smtp_port=587,
                    use_tls=True,
                    use_ssl=False,
                    default_sender='<EMAIL>',
                    welcome_email_enabled=True,
                    session_reminder_enabled=True,
                    payment_confirmation_enabled=True,
                    subscription_approval_enabled=True,
                    trial_session_notification_enabled=True,
                    makeup_session_notification_enabled=True,
                    session_deletion_notification_enabled=True,
                    user_management_notifications_enabled=True,
                    subscription_purchase_enabled=True,
                    subscription_expiry_enabled=True,
                    password_reset_enabled=True
                )
                db.session.add(email_settings)
                db.session.commit()
                print("   ✅ تم إنشاء إعدادات البريد الإلكتروني")
            else:
                print("   ✅ إعدادات البريد الإلكتروني موجودة")
            
            # 6. عرض جميع القوالب
            print("\n📋 جميع القوالب المتاحة:")
            templates = EmailTemplate.query.order_by(EmailTemplate.template_type, EmailTemplate.name).all()
            
            current_type = None
            for template in templates:
                if template.template_type != current_type:
                    current_type = template.template_type
                    print(f"\n📁 {current_type.upper()}:")
                
                status = "🟢 مفعل" if template.is_active else "🔴 معطل"
                system = "🔒 نظام" if template.is_system else "👤 مخصص"
                print(f"   - {template.name}: {template.display_name} {status} {system}")
            
            print(f"\n📊 المجموع الكلي: {len(templates)} قالب")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح القوالب: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_email_system():
    """اختبار نظام البريد الإلكتروني"""
    
    print("\n🧪 اختبار نظام البريد الإلكتروني...")
    print("=" * 60)
    
    try:
        from app import app
        from utils.email_service import EmailService
        
        with app.app_context():
            email_service = EmailService()
            
            # اختبار الاتصال
            print("📡 اختبار الاتصال بخادم البريد...")
            if email_service.smtp_server and email_service.smtp_username:
                print(f"   SMTP Server: {email_service.smtp_server}")
                print(f"   SMTP Username: {email_service.smtp_username}")
                print("   ✅ إعدادات البريد متوفرة")
            else:
                print("   ⚠️ إعدادات البريد غير مكتملة")
                print("   💡 تأكد من إضافة MAIL_USERNAME و MAIL_PASSWORD في متغيرات البيئة")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار البريد: {str(e)}")
        return False

if __name__ == '__main__':
    print("🚀 بدء إصلاح نظام البريد الإلكتروني...")
    
    # إصلاح القوالب
    templates_fixed = fix_email_templates()
    
    # اختبار النظام
    system_tested = test_email_system()
    
    print("\n" + "=" * 60)
    if templates_fixed and system_tested:
        print("🎉 تم إصلاح نظام البريد الإلكتروني بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. تأكد من إضافة MAIL_USERNAME و MAIL_PASSWORD في Render")
        print("2. اذهب إلى إعدادات البريد في لوحة الإدارة")
        print("3. اختبر إرسال بريد تجريبي")
    else:
        print("❌ فشل في إصلاح النظام - راجع الأخطاء أعلاه")
