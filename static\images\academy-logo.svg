<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" stroke="#fff" stroke-width="2"/>
  
  <!-- Quran Book Icon -->
  <g transform="translate(10, 8)">
    <!-- Book Cover -->
    <rect x="2" y="4" width="16" height="20" rx="2" fill="#fff" opacity="0.9"/>
    <rect x="3" y="5" width="14" height="18" rx="1" fill="#f8f9fa"/>
    
    <!-- Book Pages -->
    <line x1="6" y1="9" x2="15" y2="9" stroke="#4CAF50" stroke-width="1"/>
    <line x1="6" y1="12" x2="15" y2="12" stroke="#4CAF50" stroke-width="1"/>
    <line x1="6" y1="15" x2="15" y2="15" stroke="#4CAF50" stroke-width="1"/>
    <line x1="6" y1="18" x2="12" y2="18" stroke="#4CAF50" stroke-width="1"/>
    
    <!-- Arabic Calligraphy Style -->
    <circle cx="10.5" cy="11" r="1.5" fill="#4CAF50" opacity="0.3"/>
  </g>
</svg>
