#!/usr/bin/env python3
"""
Test setup system
"""

import os
import sys

# Remove existing database
if os.path.exists('instance/academy.db'):
    os.remove('instance/academy.db')
    print('✅ Old database removed')

if os.path.exists('instance/quranlms.db'):
    os.remove('instance/quranlms.db')
    print('✅ Old quranlms database removed')

# Test setup system
try:
    from app import app
    from models import db, User, AcademySettings
    
    with app.app_context():
        print('🔄 Creating fresh database...')
        db.create_all()
        print('✅ Database created')
        
        # Check if any data exists
        admin_count = User.query.filter_by(role='admin').count()
        settings_count = AcademySettings.query.count()
        total_users = User.query.count()
        
        print(f'📊 Database status:')
        print(f'   Total users: {total_users}')
        print(f'   Admin users: {admin_count}')
        print(f'   Academy settings: {settings_count}')
        
        # Test setup check
        from routes.setup import is_setup_completed
        setup_completed = is_setup_completed()
        
        print(f'🔍 Setup completed: {setup_completed}')
        
        if setup_completed and (admin_count == 0 and settings_count == 0):
            print('❌ ERROR: Setup shows completed but no data exists!')
        elif not setup_completed and (admin_count == 0 and settings_count == 0):
            print('✅ SUCCESS: Setup system working correctly!')
            print('🌐 Setup wizard will appear on first visit')
        elif setup_completed and (admin_count > 0 or settings_count > 0):
            print('ℹ️ INFO: Setup completed because data exists')
        else:
            print('⚠️ WARNING: Unexpected state')
            
except Exception as e:
    print(f'❌ Error: {str(e)}')
    import traceback
    traceback.print_exc()
