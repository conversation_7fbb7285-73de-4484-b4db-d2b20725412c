{% extends "base.html" %}

{% block title %}إدارة الباقات - {{ academy_name }}{% endblock %}
{% block page_title %}إدارة الباقات{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block content %}
<!-- Header with Add Button -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>إدارة الباقات</h4>
            <a href="{{ url_for('admin.new_package') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة باقة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Packages Grid -->
<div class="row">
    {% for package in packages %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 {{ 'border-success' if package.is_active else 'border-secondary' }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ package.name }}</h5>
                <span class="badge {{ 'bg-success' if package.is_active else 'bg-secondary' }}">
                    {{ 'نشطة' if package.is_active else 'غير نشطة' }}
                </span>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="h2 text-primary">{{ format_currency(package.price) }}</div>
                    <div class="text-muted">{{ package.duration_days }} يوم</div>
                </div>
                
                <p class="card-text">{{ package.description }}</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span><i class="fas fa-calendar-alt me-2"></i>عدد الحصص:</span>
                        <span class="fw-bold">{{ package.sessions_count }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span><i class="fas fa-clock me-2"></i>المدة:</span>
                        <span class="fw-bold">{{ package.duration_days }} يوم</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span><i class="fas fa-hourglass-half me-2"></i>مدة الحصة:</span>
                        <span class="fw-bold">{{ package.session_duration or 60 }} دقيقة</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span><i class="fas fa-users me-2"></i>المشتركين:</span>
                        <span class="fw-bold">{{ package.subscriptions|length }}</span>
                    </div>
                </div>
                
                {% if package.features %}
                <div class="mb-3">
                    <h6>المميزات:</h6>
                    <div class="small">
                        {% for feature in package.features.split('\n') %}
                            {% if feature.strip() %}
                            <div class="mb-1">
                                <i class="fas fa-check text-success me-2"></i>{{ feature.strip() }}
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex gap-2">
                    <a href="{{ url_for('admin.edit_package', package_id=package.id) }}"
                       class="btn btn-outline-primary btn-sm flex-fill">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                    <button type="button" class="btn btn-outline-info btn-sm flex-fill"
                            data-bs-toggle="modal" data-bs-target="#packageModal{{ package.id }}">
                        <i class="fas fa-eye me-1"></i>عرض
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm"
                            onclick="togglePackageStatus({{ package.id }}, {{ 'false' if package.is_active else 'true' }})"
                            title="{{ 'إيقاف الباقة' if package.is_active else 'تفعيل الباقة' }}">
                        <i class="fas fa-{{ 'pause' if package.is_active else 'play' }}"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm"
                            onclick="deletePackage({{ package.id }}, '{{ package.name }}', {{ package.subscriptions|length }})"
                            title="حذف الباقة نهائياً">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    
    {% if not packages %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-box fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد باقات</h5>
            <p class="text-muted">لم يتم إنشاء أي باقات بعد.</p>
            <a href="{{ url_for('admin.new_package') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة باقة جديدة
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Package Details Modals -->
{% for package in packages %}
<div class="modal fade" id="packageModal{{ package.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الباقة: {{ package.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الباقة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>{{ package.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>السعر:</strong></td>
                                <td>${{ "%.2f"|format(package.price) }}</td>
                            </tr>
                            <tr>
                                <td><strong>المدة:</strong></td>
                                <td>{{ package.duration_days }} يوم</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الحصص:</strong></td>
                                <td>{{ package.sessions_count }}</td>
                            </tr>
                            <tr>
                                <td><strong>مدة الحصة:</strong></td>
                                <td>{{ package.session_duration or 60 }} دقيقة</td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if package.is_active else 'bg-secondary' }}">
                                        {{ 'نشطة' if package.is_active else 'غير نشطة' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ package.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>الوصف</h6>
                        <p>{{ package.description or 'لا يوجد وصف' }}</p>
                        
                        {% if package.features %}
                        <h6>المميزات</h6>
                        <ul class="list-unstyled">
                            {% for feature in package.features.split('\n') %}
                                {% if feature.strip() %}
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>{{ feature.strip() }}
                                </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                        {% endif %}
                        
                        <h6>إحصائيات</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>عدد المشتركين:</strong></td>
                                <td>{{ package.subscriptions|length }}</td>
                            </tr>
                            <tr>
                                <td><strong>الاشتراكات النشطة:</strong></td>
                                <td>{{ package.subscriptions|selectattr('status', 'equalto', 'active')|list|length }}</td>
                            </tr>
                            <tr>
                                <td><strong>الاشتراكات المعلقة:</strong></td>
                                <td>{{ package.subscriptions|selectattr('status', 'equalto', 'pending')|list|length }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="{{ url_for('admin.edit_package', package_id=package.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>تعديل الباقة
                </a>
                <button type="button" class="btn btn-danger"
                        onclick="deletePackage({{ package.id }}, '{{ package.name }}', {{ package.subscriptions|length }})"
                        data-bs-dismiss="modal">
                    <i class="fas fa-trash me-2"></i>حذف الباقة
                </button>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
    // إضافة CSRF token مباشرة
    const CSRF_TOKEN = "{{ csrf_token() }}";
    console.log('🔑 CSRF Token تم تحميله:', CSRF_TOKEN ? 'نعم' : 'لا');
    // دوال مساعدة لعرض الرسائل
    function showSuccessMessage(message) {
        // إنشاء عنصر التنبيه
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // إضافة التنبيه للصفحة
        document.body.appendChild(alertDiv);

        // إزالة التنبيه تلقائياً بعد 3 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }

    function showErrorMessage(message) {
        // إنشاء عنصر التنبيه
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // إضافة التنبيه للصفحة
        document.body.appendChild(alertDiv);

        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    function togglePackageStatus(packageId, newStatus) {
        const action = newStatus === 'true' ? 'تفعيل' : 'إلغاء تفعيل';

        if (confirm(`هل أنت متأكد من ${action} هذه الباقة؟`)) {
            console.log('🔄 تبديل حالة الباقة:', packageId);

            // الحصول على CSRF token
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            // إذا لم يوجد في meta tag، استخدم المتغير المباشر
            if (!csrfToken) {
                csrfToken = CSRF_TOKEN;
            }

            console.log('🔑 CSRF Token:', csrfToken ? 'موجود' : 'غير موجود');

            if (!csrfToken) {
                console.error('❌ CSRF token غير موجود');
                showErrorMessage('خطأ: CSRF token غير موجود');
                return;
            }

            // إنشاء FormData مع CSRF token
            const formData = new FormData();
            formData.append('csrf_token', csrfToken);

            // إرسال طلب مع CSRF token
            fetch(`/admin/packages/${packageId}/toggle`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('📊 استجابة الخادم:', response.status, response.statusText);

                if (response.ok) {
                    return response.json();
                } else {
                    return response.text().then(text => {
                        throw new Error(`HTTP ${response.status}: ${text}`);
                    });
                }
            })
            .then(data => {
                console.log('✅ نتيجة التبديل:', data);
                if (data.success) {
                    showSuccessMessage(data.message);
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showErrorMessage(data.message || 'حدث خطأ أثناء تحديث الباقة');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في التبديل:', error);
                showErrorMessage(`حدث خطأ أثناء تحديث الباقة: ${error.message}`);
            });
        }
    }

    function deletePackage(packageId, packageName, subscriptionsCount) {
        // التحقق من وجود اشتراكات
        if (subscriptionsCount > 0) {
            alert(`⚠️ لا يمكن حذف الباقة "${packageName}" لأنها تحتوي على ${subscriptionsCount} اشتراك نشط.\n\nيجب حذف جميع الاشتراكات المرتبطة بهذه الباقة أولاً.`);
            return;
        }

        // تأكيد الحذف
        const confirmMessage = `⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!\n\nهل أنت متأكد من حذف الباقة "${packageName}" نهائياً؟\n\nسيتم حذف:\n- الباقة نفسها\n- جميع البيانات المرتبطة بها\n\nاكتب "حذف" للتأكيد:`;

        const userInput = prompt(confirmMessage);

        if (userInput === 'حذف' || userInput === 'حذف الباقة' || userInput.toLowerCase() === 'delete') {
            // إظهار رسالة التحميل
            const deleteBtn = event.target.closest('button');
            const originalContent = deleteBtn.innerHTML;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحذف...';
            deleteBtn.disabled = true;

            // الحصول على CSRF token
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            // إذا لم يوجد في meta tag، استخدم المتغير المباشر
            if (!csrfToken) {
                csrfToken = CSRF_TOKEN;
            }

            if (!csrfToken) {
                showErrorMessage('خطأ: CSRF token غير موجود');
                deleteBtn.innerHTML = originalContent;
                deleteBtn.disabled = false;
                return;
            }

            // إنشاء FormData مع CSRF token
            const formData = new FormData();
            formData.append('csrf_token', csrfToken);

            // إرسال طلب الحذف
            fetch(`/admin/packages/${packageId}/delete`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('📊 استجابة حذف الباقة:', response.status, response.statusText);

                if (response.ok) {
                    return response.json();
                } else {
                    return response.text().then(text => {
                        throw new Error(`HTTP ${response.status}: ${text}`);
                    });
                }
            })
            .then(data => {
                console.log('✅ نتيجة الحذف:', data);

                if (data.success) {
                    showSuccessMessage(data.message);
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showErrorMessage(data.message || 'فشل في حذف الباقة');
                    // إعادة تفعيل الزر
                    deleteBtn.innerHTML = originalContent;
                    deleteBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('❌ خطأ في حذف الباقة:', error);
                showErrorMessage(`حدث خطأ أثناء حذف الباقة: ${error.message}`);

                // إعادة تفعيل الزر
                deleteBtn.innerHTML = originalContent;
                deleteBtn.disabled = false;
            });
        } else if (userInput !== null) {
            alert('❌ لم يتم تأكيد الحذف. تم إلغاء العملية.');
        }
    }
    
    // Add hover effects to cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
{% endblock %}
