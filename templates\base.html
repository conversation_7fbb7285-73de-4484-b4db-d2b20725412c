<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{{ academy_name }}{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ academy_favicon }}">
    <link rel="shortcut icon" href="{{ academy_favicon }}">
    
    <!-- Google Fonts - IBM Plex Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome - Latest Version -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Bootstrap Icons as Fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">

    <!-- Tajawal Custom Styles -->
    <link href="{{ url_for('static', filename='css/tajawal-font.css') }}" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: {{ primary_color or '#007bff' }};
            --secondary-color: {{ secondary_color or '#6c757d' }};
            --success-color: {{ success_color or '#28a745' }};
            --danger-color: {{ danger_color or '#dc3545' }};
            --warning-color: {{ warning_color or '#ffc107' }};
            --info-color: {{ info_color or '#17a2b8' }};
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 70px;
        }

        [data-theme="dark"] {
            --bs-body-bg: #1a1a1a;
            --bs-body-color: #ffffff;
            --bs-card-bg: #2d2d2d;
            --bs-border-color: #404040;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background-color: var(--bs-body-bg, #ffffff);
            color: var(--bs-body-color, #000000);
            transition: all 0.3s ease;
            direction: rtl;
            text-align: right;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            transform: translateX(0);
            transition: all 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .sidebar-header .logo-image {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .sidebar-header .logo-content {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .sidebar-header .logo-text {
            font-size: 1.2rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 0.2rem;
        }

        .sidebar-header .logo-slogan {
            font-size: 0.75rem;
            font-weight: 400;
            opacity: 0.8;
            line-height: 1.1;
            max-width: 150px;
            text-align: right;
        }

        .sidebar.collapsed .sidebar-header .logo-content {
            display: none;
        }

        .sidebar.collapsed .sidebar-header .logo {
            justify-content: center;
        }

        .sidebar.collapsed .sidebar-header .logo-image {
            width: 35px;
            height: 35px;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }

        .sidebar-nav .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
            text-align: center;
        }

        .sidebar.collapsed .nav-link-text {
            display: none;
        }

        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: var(--sidebar-collapsed-width);
        }

        /* Top Navigation */
        .top-nav {
            background-color: var(--bs-card-bg, white);
            border-bottom: 1px solid var(--bs-border-color, #dee2e6);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                z-index: 999;
                display: none;
            }
            
            .sidebar-overlay.show {
                display: block;
            }
        }

        /* Cards */
        .card {
            background-color: var(--bs-card-bg, white);
            border: 1px solid var(--bs-border-color, #dee2e6);
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        }

        /* Buttons */
        .btn {
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        /* Theme Toggle */
        .theme-toggle {
            background: none;
            border: none;
            color: inherit;
            font-size: 1.25rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* Responsive Typography */
        @media (max-width: 576px) {
            h1 { font-size: 1.75rem; }
            h2 { font-size: 1.5rem; }
            h3 { font-size: 1.25rem; }
            .btn { font-size: 0.875rem; }

            .sidebar-header .logo-text {
                font-size: 1rem;
            }

            .sidebar-header .logo-slogan {
                font-size: 0.7rem;
                max-width: 120px;
            }

            .sidebar-header .logo-image {
                width: 35px;
                height: 35px;
            }
        }

        /* Loading Spinner */
        .spinner-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        /* Tajawal Font Application */
        * {
            font-family: 'Tajawal', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 700;
        }

        .navbar-brand {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 800;
        }

        .btn {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 500;
        }

        .form-control, .form-select, .form-check-label {
            font-family: 'Tajawal', sans-serif !important;
        }

        .table, .table th, .table td {
            font-family: 'Tajawal', sans-serif !important;
        }

        .card-title, .card-text {
            font-family: 'Tajawal', sans-serif !important;
        }

        .nav-link {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 500;
        }

        .badge {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 500;
        }

        .alert {
            font-family: 'Tajawal', sans-serif !important;
        }

        .modal-title, .modal-body {
            font-family: 'Tajawal', sans-serif !important;
        }

        .breadcrumb {
            font-family: 'Tajawal', sans-serif !important;
        }

        .page-link {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 500;
        }

        .list-group-item {
            font-family: 'Tajawal', sans-serif !important;
        }

        .form-label {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 500;
        }

        .dropdown-menu {
            font-family: 'Tajawal', sans-serif !important;
        }

        .tooltip, .popover {
            font-family: 'Tajawal', sans-serif !important;
        }

        .text-muted, .small, small {
            font-family: 'Tajawal', sans-serif !important;
        }

        .display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 800;
        }

        .lead {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 400;
        }

        input, textarea, select {
            font-family: 'Tajawal', sans-serif !important;
        }

        .sidebar-nav .nav-link {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 500;
        }

        .notification-badge {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 700;
        }

        /* Icon Fixes */
        .fas, .far, .fab, .fal, .fad, .fass, .fasr, .fasl {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands" !important;
            font-weight: 900;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .fa-solid {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900;
        }

        .fa-regular {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 400;
        }

        .fa-brands {
            font-family: "Font Awesome 6 Brands" !important;
            font-weight: 400;
        }

        /* Bootstrap Icons as Fallback */
        .bi {
            font-family: "bootstrap-icons" !important;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Ensure icons don't inherit Tajawal font */
        i[class*="fa-"],
        i[class*="bi-"],
        .fa,
        .fas,
        .far,
        .fab,
        .fal,
        .fad,
        .fass,
        .fasr,
        .fasl {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands", "bootstrap-icons" !important;
        }

        /* Fix for common icons that might show as squares */
        .fa-user::before { content: "\f007"; }
        .fa-home::before { content: "\f015"; }
        .fa-dashboard::before, .fa-tachometer-alt::before { content: "\f3fd"; }
        .fa-users::before { content: "\f0c0"; }
        .fa-book::before { content: "\f02d"; }
        .fa-calendar::before { content: "\f073"; }
        .fa-cog::before, .fa-gear::before { content: "\f013"; }
        .fa-chart-bar::before { content: "\f080"; }
        .fa-bell::before { content: "\f0f3"; }
        .fa-envelope::before { content: "\f0e0"; }
        .fa-sign-out-alt::before { content: "\f2f5"; }
        .fa-plus::before { content: "\f067"; }
        .fa-edit::before { content: "\f044"; }
        .fa-trash::before { content: "\f1f8"; }
        .fa-eye::before { content: "\f06e"; }
        .fa-check::before { content: "\f00c"; }
        .fa-times::before { content: "\f00d"; }
        .fa-search::before { content: "\f002"; }
        .fa-filter::before { content: "\f0b0"; }

        /* Ensure danger zone alerts are always visible and never auto-hide */
        .alert:not(.alert-dismissible) {
            opacity: 1 !important;
            visibility: visible !important;
            display: block !important;
        }

        /* Prevent auto-hiding of danger zone alerts */
        .alert.alert-danger:not(.alert-dismissible),
        .alert.alert-warning:not(.alert-dismissible) {
            animation: none !important;
            transition: none !important;
            position: relative !important;
        }

        /* Make danger zone alerts more prominent */
        .alert.alert-danger:not(.alert-dismissible) {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da !important;
        }

        .alert.alert-warning:not(.alert-dismissible) {
            border-left: 4px solid #ffc107;
            background-color: #fff3cd !important;
        }

        /* Notification Button Styling */
        #notificationDropdown {
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #notificationDropdown:hover {
            background-color: #f8f9fa !important;
            border-color: #6c757d !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        #notificationDropdown:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        #notificationBadge {
            min-width: 18px !important;
            height: 18px !important;
            font-size: 0.7rem !important;
            line-height: 1.2 !important;
            padding: 2px 6px !important;
            font-weight: bold !important;
        }

        .notification-dropdown {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
            border-radius: 0.5rem;
        }

        .notification-item {
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s ease;
        }

        .notification-item:hover {
            background-color: #f8f9fa !important;
            cursor: pointer;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item .text-truncate {
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .notification-item .badge {
            font-weight: 500;
        }

        .dropdown-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
        }

        .dropdown-footer {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-top: 1px solid #dee2e6;
        }

        /* Simple animation for notification badge */
        #notificationBadge {
            animation: none;
        }

        /* Simple hover effect for notification button */
        #notificationDropdown .fa-bell {
            transition: color 0.3s ease;
        }

        #notificationDropdown:hover .fa-bell {
            color: #007bff !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
    {% block extra_head %}{% endblock %}
</head>
<body data-theme="light">
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    
    <!-- Sidebar -->
    {% if current_user.is_authenticated %}
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                {% if academy_logo and academy_logo != '/static/images/logo.png' %}
                    <img src="{{ academy_logo }}" alt="{{ academy_name }}" class="logo-image">
                {% else %}
                    <i class="fas fa-quran-book"></i>
                {% endif %}
                <div class="logo-content">
                    <span class="logo-text">{{ academy_name }}</span>
                    {% if academy_slogan %}
                        <span class="logo-slogan">{{ academy_slogan }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <ul class="sidebar-nav nav flex-column">
            {% if current_user.role == 'admin' %}
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="nav-link-text">لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('admin.users') }}">
                        <i class="fas fa-users"></i>
                        <span class="nav-link-text">إدارة المستخدمين</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('admin.packages') }}">
                        <i class="fas fa-box"></i>
                        <span class="nav-link-text">إدارة الباقات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('admin.sessions') }}">
                        <i class="fas fa-calendar-alt"></i>
                        <span class="nav-link-text">إدارة الحصص</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('admin.subscriptions') }}">
                        <i class="fas fa-credit-card"></i>
                        <span class="nav-link-text">إدارة الاشتراكات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('admin.reports') }}">
                        <i class="fas fa-chart-bar"></i>
                        <span class="nav-link-text">التقارير</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('admin.settings') }}">
                        <i class="fas fa-cog"></i>
                        <span class="nav-link-text">الإعدادات</span>
                    </a>
                    <!-- Settings Submenu -->
                    <ul class="nav flex-column ms-3" style="font-size: 0.9em;">
                        <li class="nav-item">
                            <a class="nav-link py-1" href="{{ url_for('admin.payment_settings') }}">
                                <i class="fas fa-credit-card me-2"></i>
                                <span class="nav-link-text">الدفع</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-1" href="{{ url_for('admin.session_settings') }}">
                                <i class="fas fa-video me-2"></i>
                                <span class="nav-link-text">الحصص</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-1" href="{{ url_for('admin.email_settings') }}">
                                <i class="fas fa-envelope me-2"></i>
                                <span class="nav-link-text">البريد</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link py-1" href="{{ url_for('admin.security_settings') }}">
                                <i class="fas fa-shield-alt me-2"></i>
                                <span class="nav-link-text">الأمان</span>
                            </a>
                        </li>
                    </ul>
                </li>
            {% elif current_user.role == 'teacher' %}
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('teacher.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="nav-link-text">لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('teacher.sessions') }}">
                        <i class="fas fa-calendar-alt"></i>
                        <span class="nav-link-text">حصصي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('teacher.students') }}">
                        <i class="fas fa-user-graduate"></i>
                        <span class="nav-link-text">طلابي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('teacher.reports') }}">
                        <i class="fas fa-chart-line"></i>
                        <span class="nav-link-text">تقاريري</span>
                    </a>
                </li>
            {% elif current_user.role == 'student' %}
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('student.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="nav-link-text">لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('student.sessions') }}">
                        <i class="fas fa-calendar-alt"></i>
                        <span class="nav-link-text">حصصي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('student.packages') }}">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="nav-link-text">شراء باقة</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('student.subscriptions') }}">
                        <i class="fas fa-credit-card"></i>
                        <span class="nav-link-text">اشتراكاتي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('student.invoices') }}">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span class="nav-link-text">فواتيري</span>
                    </a>
                </li>
            {% endif %}
            
            <li class="nav-item mt-auto">
                <a class="nav-link" href="{{ url_for('auth.logout') }}">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="nav-link-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        {% if current_user.is_authenticated %}
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-decoration-none me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h4 class="mb-0">{% block page_title %}لوحة التحكم{% endblock %}</h4>
            </div>

            <div class="d-flex align-items-center">
                <!-- Notifications -->
                <div class="dropdown me-3">
                    <button class="btn btn-light position-relative" type="button" data-bs-toggle="dropdown" id="notificationDropdown"
                            style="border: 1px solid #dee2e6; padding: 8px 12px;">
                        <i class="fas fa-bell text-dark" style="font-size: 16px;"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                              id="notificationBadge" style="display: none; font-size: 0.7rem; min-width: 16px; height: 16px; line-height: 1;">
                            0
                        </span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end" style="width: 350px; max-height: 400px; overflow-y: auto;">
                        <div class="dropdown-header">
                            <h6 class="mb-0">الإشعارات</h6>
                        </div>
                        <div id="notificationsList">
                            <div class="text-center py-3">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                            </div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="text-center p-2">
                            <a href="{{ url_for('notifications.index') }}" class="btn btn-sm btn-primary">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    </div>

                </div>

                <!-- Theme Toggle -->
                <button class="theme-toggle me-3" id="themeToggle">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>

                <!-- User Menu -->
                <div class="dropdown">
                    <button class="btn btn-link text-decoration-none d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                        <div class="rounded-circle me-2 d-flex align-items-center justify-content-center bg-primary text-white" style="width: 32px; height: 32px; font-size: 14px;">
                            {{ current_user.full_name[0] if current_user.full_name else 'U' }}
                        </div>
                        <span>{{ current_user.full_name }}</span>
                        <i class="fas fa-chevron-down ms-2"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        {% if current_user.role == 'admin' %}
                        <li><a class="dropdown-item" href="{{ url_for('admin.profile') }}"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.admin_settings') }}"><i class="fas fa-user-cog me-2"></i>الإعدادات الشخصية</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.settings') }}"><i class="fas fa-cog me-2"></i>الإعدادات العامة</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">إعدادات النظام</h6></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.payment_settings') }}"><i class="fas fa-credit-card me-2"></i>إعدادات الدفع</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.session_settings') }}"><i class="fas fa-video me-2"></i>إعدادات الحصص</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.email_settings') }}"><i class="fas fa-envelope me-2"></i>إعدادات البريد</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.security_settings') }}"><i class="fas fa-shield-alt me-2"></i>إعدادات الأمان</a></li>
                        {% elif current_user.role == 'teacher' %}
                        <li><a class="dropdown-item" href="{{ url_for('teacher.profile') }}"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('teacher.settings') }}"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        {% elif current_user.role == 'student' %}
                        <li><a class="dropdown-item" href="{{ url_for('student.profile') }}"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('student.settings') }}"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </nav>
        {% endif %}

        <!-- Page Content -->
        <div class="container-fluid p-4">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Loading Spinner -->
    <div class="spinner-overlay d-none" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // Sidebar Toggle
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const overlay = document.getElementById('sidebarOverlay');

            if (window.innerWidth <= 768) {
                // Mobile: Show/hide sidebar with overlay
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
            } else {
                // Desktop: Collapse/expand sidebar
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        });

        // Close sidebar on overlay click (mobile)
        document.getElementById('sidebarOverlay')?.addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.remove('show');
            overlay.classList.remove('show');
        });

        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const body = document.body;

        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        body.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme);

        themeToggle?.addEventListener('click', function() {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';

            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        });

        function updateThemeIcon(theme) {
            if (themeIcon) {
                themeIcon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
            }
        }

        // Active Navigation Link
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');

        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });

        // Loading Spinner Functions
        function showLoading() {
            document.getElementById('loadingSpinner').classList.remove('d-none');
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').classList.add('d-none');
        }

        // Auto-hide flash messages only (not danger zone alerts)
        setTimeout(function() {
            // Only target flash messages with dismissible class and close button
            const flashAlerts = document.querySelectorAll('.alert.alert-dismissible[role="alert"]');
            flashAlerts.forEach(alert => {
                // Double check it has a close button and is not a danger zone alert
                if (alert.querySelector('.btn-close') && !alert.closest('.mb-4')) {
                    try {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    } catch (e) {
                        // Ignore errors if alert is already closed
                    }
                }
            });
        }, 5000);

        // Ensure danger zone alerts are never hidden
        const dangerZoneAlerts = document.querySelectorAll('.alert:not(.alert-dismissible)');
        dangerZoneAlerts.forEach(alert => {
            // Remove any event listeners that might hide these alerts
            alert.style.opacity = '1';
            alert.style.visibility = 'visible';
            alert.style.display = 'block';

            // Prevent Bootstrap from auto-hiding these
            alert.addEventListener('close.bs.alert', function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            });
        });

        // Responsive sidebar handling
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const overlay = document.getElementById('sidebarOverlay');

            if (window.innerWidth > 768) {
                // Desktop: Remove mobile classes
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            } else {
                // Mobile: Remove desktop classes
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        });

        // Notification System
        function loadNotifications() {
            fetch('/notifications/api/recent?limit=5')
            .then(response => response.json())
            .then(data => {
                updateNotificationBadge(data.unread_count);
                updateNotificationsList(data.notifications);
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
            });
        }

        function updateNotificationBadge(count) {
            const badge = document.getElementById('notificationBadge');
            if (badge) {
                if (count > 0) {
                    badge.textContent = count;
                    badge.style.display = 'inline';
                } else {
                    badge.style.display = 'none';
                }
            }
        }

        function updateNotificationsList(notifications) {
            const container = document.getElementById('notificationsList');
            if (!container) return;

            if (notifications.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-3 text-muted">
                        <i class="fas fa-bell-slash mb-2"></i>
                        <div>لا توجد إشعارات</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = notifications.map(notification => `
                <div class="dropdown-item ${!notification.is_read ? 'bg-light' : ''}" onclick="viewNotification(${notification.id})">
                    <div class="d-flex">
                        <div class="me-2">
                            <i class="${notification.icon || 'fas fa-bell'} text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 ${!notification.is_read ? 'fw-bold' : ''}">${notification.title}</h6>
                            <p class="mb-1 small text-muted">${notification.message}</p>
                            <small class="text-muted">${notification.time_ago}</small>
                        </div>
                    </div>
                </div>
            `).join('');
        }



        function viewNotification(notificationId) {
            // Mark as read and redirect
            fetch(`/notifications/view/${notificationId}`)
            .then(response => {
                if (response.redirected) {
                    window.location.href = response.url;
                } else {
                    // Reload notifications if no redirect
                    loadNotifications();
                }
            });
        }

        function markAllNotificationsAsRead() {
            // سيتم تطبيقها لاحقاً
            console.log('Mark all as read');
        }

        // Load notifications on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();

            // Refresh notifications every 30 seconds
            setInterval(loadNotifications, 30000);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
