{% extends "base.html" %}

{% block title %}جدولة حصة جديدة - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}جدولة حصة جديدة{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-plus me-2"></i>جدولة حصة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.new_session') }}" id="sessionForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="teacher_id" class="form-label">المعلم <span class="text-danger">*</span></label>
                                <select class="form-select" id="teacher_id" name="teacher_id" required>
                                    <option value="">اختر المعلم</option>
                                    {% for teacher in teachers %}
                                    <option value="{{ teacher.id }}">{{ teacher.full_name }} ({{ teacher.email }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="student_id" class="form-label">الطالب <span class="text-danger">*</span></label>
                                <select class="form-select" id="student_id" name="student_id" required>
                                    <option value="">اختر الطالب</option>
                                    {% for student in students %}
                                    <option value="{{ student.id }}">{{ student.full_name }} ({{ student.email }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="session_type" class="form-label">نوع الحصة <span class="text-danger">*</span></label>
                                <select class="form-select" id="session_type" name="session_type" required>
                                    <option value="trial">حصة تجريبية</option>
                                    <option value="makeup">حصة تعويضية</option>
                                    <option value="scheduled" selected>حصة مجدولة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration_minutes" class="form-label">مدة الحصة (بالدقائق) <span class="text-danger">*</span></label>
                                <select class="form-select" id="duration_minutes" name="duration_minutes" required>
                                    <option value="30">30 دقيقة</option>
                                    <option value="45">45 دقيقة</option>
                                    <option value="60" selected>60 دقيقة</option>
                                    <option value="90">90 دقيقة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="scheduled_date" class="form-label">تاريخ الحصة <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="scheduled_date" name="scheduled_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="scheduled_time" class="form-label">وقت الحصة <span class="text-danger">*</span></label>
                                <input type="time" class="form-control" id="scheduled_time" name="scheduled_time" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="subscriptionSection" style="display: block;">
                        <label for="subscription_id" class="form-label">الاشتراك</label>
                        <select class="form-select" id="subscription_id" name="subscription_id">
                            <option value="">بدون اشتراك (حصة مستقلة)</option>
                            {% for subscription in subscriptions %}
                            <option value="{{ subscription.id }}" data-student="{{ subscription.user_id }}" data-sessions-remaining="{{ subscription.sessions_remaining or 0 }}">
                                {{ subscription.user.full_name }} - {{ subscription.package.name }}
                                ({{ subscription.sessions_remaining or 0 }} حصة متبقية)
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">اختر الاشتراك إذا كانت الحصة جزءاً من باقة مدفوعة</div>

                        <!-- Bulk scheduling option -->
                        <div class="mt-3" id="bulkSchedulingOption" style="display: none;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="bulk_schedule" name="bulk_schedule">
                                <label class="form-check-label" for="bulk_schedule">
                                    <strong>جدولة جميع حصص الاشتراك مرة واحدة</strong>
                                </label>
                            </div>
                            <div class="form-text text-info">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم إنشاء <span id="remainingSessionsCount">0</span> حصة بناءً على الحصص المتبقية في الاشتراك
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meeting_provider" class="form-label">مزود خدمة الحصة</label>
                        <select class="form-select" id="meeting_provider" name="meeting_provider">
                            <option value="google_meet">
                                <i class="fab fa-google"></i> Google Calendar + Jitsi Meeting
                            </option>
                            <option value="jitsi">
                                <i class="fas fa-video"></i> Jitsi Meet
                            </option>
                        </select>
                        <div class="form-text">سيتم توليد رابط الحصة تلقائياً حسب المزود المختار</div>
                    </div>

                    <!-- Bulk Scheduling Section -->
                    <div id="bulkSchedulingSection" style="display: none;">
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-calendar-plus me-2"></i>جدولة حصص متعددة
                                    <span class="badge bg-light text-dark ms-2" id="bulkSessionsCountBadge">0 حصة</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>سيتم إنشاء <span id="totalSessionsToCreate">0</span> حصة منفصلة</strong><br>
                                    كل حصة ستحصل على رابط فريد ومستقل
                                </div>

                                <div id="bulkSessionsList" class="row">
                                    <!-- Sessions will be added dynamically -->
                                </div>

                                <div class="d-flex gap-2 mt-3 justify-content-center">
                                    <button type="button" class="btn btn-success btn-sm" onclick="generateAllSessions()">
                                        <i class="fas fa-magic me-1"></i>إنشاء جميع الحقول تلقائياً
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addBulkSession()">
                                        <i class="fas fa-plus me-1"></i>إضافة حصة يدوياً
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="generateWeeklySchedule()">
                                        <i class="fas fa-calendar-week me-1"></i>جدولة أسبوعية
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllSessions()">
                                        <i class="fas fa-trash me-1"></i>مسح الكل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="أي ملاحظات خاصة بالحصة..."></textarea>
                    </div>
                    
                    <!-- Preview Section -->
                    <div class="mb-4">
                        <h6>معاينة الحصة</h6>
                        <div class="card border-primary" id="sessionPreview">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>المعلم:</strong> <span id="previewTeacher">لم يتم الاختيار</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>الطالب:</strong> <span id="previewStudent">لم يتم الاختيار</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>النوع:</strong> <span id="previewType">حصة مجدولة</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>التاريخ:</strong> <span id="previewDate">لم يتم تحديده</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>الوقت:</strong> <span id="previewTime">لم يتم تحديده</span>
                                        </div>
                                        <div class="mb-2">
                                            <strong>المدة:</strong> <span id="previewDuration">60 دقيقة</span>
                                        </div>
                                    </div>
                                </div>
                                <div id="previewSubscription" style="display: none;">
                                    <hr>
                                    <div class="alert alert-info mb-0">
                                        <strong>الاشتراك:</strong> <span id="previewSubscriptionText"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Sessions Preview -->
                        <div class="card border-success mt-3" id="bulkSessionsPreview" style="display: none;">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>معاينة الحصص المتعددة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="bulkPreviewList">
                                    <!-- Bulk sessions preview will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i><span id="submitText">جدولة الحصة</span>
                        </button>
                        <a href="{{ url_for('admin.sessions') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>إلغاء
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="checkAvailability()">
                            <i class="fas fa-search me-2"></i>فحص التوفر
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Availability Check Modal -->
<div class="modal fade" id="availabilityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">فحص توفر المعلم والطالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="availabilityResult">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري الفحص...</span>
                        </div>
                        <p class="mt-2">جاري فحص التوفر...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .bulk-session-item {
        transition: all 0.3s ease;
        border: 2px solid #007bff !important;
    }

    .bulk-session-item:hover {
        box-shadow: 0 6px 12px rgba(0,123,255,0.15);
        transform: translateY(-2px);
    }

    .bulk-session-item .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #007bff;
    }

    .bulk-session-item .form-control:focus,
    .bulk-session-item .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    .session-card {
        border-left: 4px solid #007bff;
    }

    .bulk-sessions-preview table {
        font-size: 0.9rem;
    }

    .form-check-label {
        cursor: pointer;
    }

    .alert-info {
        border-left: 4px solid #17a2b8;
    }

    .card-header.bg-info {
        background-color: #17a2b8 !important;
    }

    .card-header.bg-success {
        background-color: #28a745 !important;
    }

    .btn-outline-danger:hover {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0,0,0,.02);
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* إصلاح مشكلة انتقال المودال */
    .modal-dialog {
        margin: 1.75rem auto !important;
        max-width: 500px !important;
    }

    .modal {
        padding-right: 0 !important;
    }

    .modal-backdrop {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        z-index: 1040 !important;
        width: 100vw !important;
        height: 100vh !important;
    }

    /* تحسين تصميم النموذج */
    .row {
        margin-right: 0 !important;
        margin-left: 0 !important;
    }

    .col-md-6, .col-lg-6 {
        padding-right: 0.75rem !important;
        padding-left: 0.75rem !important;
    }

    /* منع تحرك العناصر */
    .container, .container-fluid {
        padding-right: 15px !important;
        padding-left: 15px !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // معالج الأخطاء العام
    window.addEventListener('error', function(e) {
        console.error('❌ خطأ JavaScript:', e.error);
        console.error('📍 في الملف:', e.filename, 'السطر:', e.lineno);
    });

    // متغيرات عامة
    let bulkSessionCounter = 0;

    // دالة للحصول على اسم اليوم
    function getDayName(dayIndex) {
        const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        return days[dayIndex] || 'غير محدد';
    }

    // Live preview functionality
    function updatePreview() {
        const teacherSelect = document.getElementById('teacher_id');
        const studentSelect = document.getElementById('student_id');
        const typeSelect = document.getElementById('session_type');
        const dateInput = document.getElementById('scheduled_date');
        const timeInput = document.getElementById('scheduled_time');
        const durationSelect = document.getElementById('duration_minutes');
        const subscriptionSelect = document.getElementById('subscription_id');
        
        // Update preview text
        document.getElementById('previewTeacher').textContent = 
            teacherSelect.selectedOptions[0]?.text || 'لم يتم الاختيار';
        document.getElementById('previewStudent').textContent = 
            studentSelect.selectedOptions[0]?.text || 'لم يتم الاختيار';
        
        const typeText = {
            'trial': 'حصة تجريبية',
            'makeup': 'حصة تعويضية',
            'scheduled': 'حصة مجدولة'
        };
        document.getElementById('previewType').textContent = typeText[typeSelect.value] || 'حصة مجدولة';
        
        document.getElementById('previewDate').textContent = dateInput.value || 'لم يتم تحديده';
        document.getElementById('previewTime').textContent = timeInput.value || 'لم يتم تحديده';
        document.getElementById('previewDuration').textContent = (durationSelect.value || '60') + ' دقيقة';
        
        // Update subscription preview
        const subscriptionPreview = document.getElementById('previewSubscription');
        const subscriptionText = document.getElementById('previewSubscriptionText');
        
        if (subscriptionSelect.value) {
            subscriptionText.textContent = subscriptionSelect.selectedOptions[0].text;
            subscriptionPreview.style.display = 'block';
        } else {
            subscriptionPreview.style.display = 'none';
        }
    }
    
    // Add event listeners for live preview
    document.querySelectorAll('#teacher_id, #student_id, #session_type, #scheduled_date, #scheduled_time, #duration_minutes, #subscription_id').forEach(input => {
        input.addEventListener('change', function() {
            updatePreview();
            updateBulkPreview();
        });
    });
    
    // Show/hide subscription section based on session type
    document.getElementById('session_type').addEventListener('change', function() {
        const subscriptionSection = document.getElementById('subscriptionSection');
        if (this.value === 'scheduled') {
            subscriptionSection.style.display = 'block';
        } else {
            subscriptionSection.style.display = 'none';
            document.getElementById('subscription_id').value = '';
            // Hide bulk scheduling when not scheduled
            document.getElementById('bulk_schedule').checked = false;
            hideBulkSchedulingForm();
        }
        updatePreview();
        updateBulkSchedulingOption();
    });

    // Initialize subscription section visibility on page load
    document.addEventListener('DOMContentLoaded', function() {
        const sessionType = document.getElementById('session_type').value;
        const subscriptionSection = document.getElementById('subscriptionSection');

        if (sessionType === 'scheduled') {
            subscriptionSection.style.display = 'block';
        } else {
            subscriptionSection.style.display = 'none';
        }

        // Initialize other elements
        updatePreview();
        updateBulkSchedulingOption();
    });
    
    // Filter subscriptions by selected student
    document.getElementById('student_id').addEventListener('change', function() {
        const studentId = this.value;
        const subscriptionSelect = document.getElementById('subscription_id');
        const options = subscriptionSelect.querySelectorAll('option[data-student]');

        options.forEach(option => {
            if (studentId && option.dataset.student !== studentId) {
                option.style.display = 'none';
            } else {
                option.style.display = 'block';
            }
        });

        // Reset subscription selection if current selection is not valid for new student
        const currentOption = subscriptionSelect.selectedOptions[0];
        if (currentOption && currentOption.dataset.student && currentOption.dataset.student !== studentId) {
            subscriptionSelect.value = '';
        }

        updatePreview();
        updateBulkSchedulingOption();
    });

    // Handle subscription selection change
    document.getElementById('subscription_id').addEventListener('change', function() {
        console.log('🔄 تغيير الاشتراك:', this.value);
        updateBulkSchedulingOption();
        updatePreview();
    });

    // Update bulk scheduling option visibility and info
    function updateBulkSchedulingOption() {
        const subscriptionSelect = document.getElementById('subscription_id');
        const bulkOption = document.getElementById('bulkSchedulingOption');
        const remainingCount = document.getElementById('remainingSessionsCount');
        const bulkCheckbox = document.getElementById('bulk_schedule');
        const sessionType = document.getElementById('session_type').value;

        console.log('🔄 تحديث خيارات الجدولة المتعددة:', {
            sessionType: sessionType,
            subscriptionValue: subscriptionSelect.value
        });

        // Only show bulk option for scheduled sessions
        if (sessionType !== 'scheduled') {
            console.log('❌ نوع الحصة ليس مجدولة');
            bulkOption.style.display = 'none';
            bulkCheckbox.checked = false;
            toggleBulkSchedulingFields();
            return;
        }

        if (subscriptionSelect.value && subscriptionSelect.value !== '') {
            const selectedOption = subscriptionSelect.selectedOptions[0];
            const sessionsRemaining = parseInt(selectedOption.dataset.sessionsRemaining) || 0;

            console.log('📊 الحصص المتبقية:', sessionsRemaining);

            if (sessionsRemaining > 0) {
                bulkOption.style.display = 'block';
                remainingCount.textContent = sessionsRemaining;
                console.log('✅ إظهار خيار الجدولة المتعددة');
            } else {
                console.log('❌ لا توجد حصص متبقية');
                bulkOption.style.display = 'none';
                bulkCheckbox.checked = false;
            }
        } else {
            console.log('❌ لم يتم اختيار اشتراك');
            bulkOption.style.display = 'none';
            bulkCheckbox.checked = false;
        }

        // Only toggle if checkbox state changed
        if (!bulkCheckbox.checked) {
            toggleBulkSchedulingFields();
        }
    }

    // Handle bulk scheduling checkbox
    document.getElementById('bulk_schedule').addEventListener('change', function() {
        toggleBulkSchedulingFields();
    });

    // Toggle fields based on bulk scheduling selection
    function toggleBulkSchedulingFields() {
        const bulkCheckbox = document.getElementById('bulk_schedule');
        const submitText = document.getElementById('submitText');

        // Find the date/time section by looking for the parent row of scheduled_date
        const scheduledDateInput = document.getElementById('scheduled_date');
        const scheduledTimeInput = document.getElementById('scheduled_time');
        const durationInput = document.getElementById('duration_minutes');
        const singleDateSection = scheduledDateInput ? scheduledDateInput.closest('.row') : null;

        console.log('🔄 تبديل حقول الجدولة:', {
            bulkChecked: bulkCheckbox.checked,
            singleDateSection: !!singleDateSection
        });

        if (bulkCheckbox.checked) {
            console.log('✅ تفعيل الجدولة المتعددة');

            // Hide single date/time fields
            if (singleDateSection) {
                singleDateSection.style.display = 'none';
            }

            // Remove required attribute from hidden fields
            if (scheduledDateInput) {
                scheduledDateInput.removeAttribute('required');
                scheduledDateInput.value = ''; // Clear value
            }
            if (scheduledTimeInput) {
                scheduledTimeInput.removeAttribute('required');
                scheduledTimeInput.value = ''; // Clear value
            }
            if (durationInput) {
                durationInput.removeAttribute('required');
            }

            // Show bulk scheduling form
            showBulkSchedulingForm();

            // Update submit button text
            if (submitText) {
                submitText.textContent = 'جدولة الحصص المتعددة';
            }

            console.log('🔧 تم إزالة خاصية required من الحقول المخفية');

        } else {
            console.log('❌ إلغاء الجدولة المتعددة');

            // Show single date/time fields
            if (singleDateSection) {
                singleDateSection.style.display = 'block';
            }

            // Add required attribute back to visible fields
            if (scheduledDateInput) {
                scheduledDateInput.setAttribute('required', 'required');
            }
            if (scheduledTimeInput) {
                scheduledTimeInput.setAttribute('required', 'required');
            }
            if (durationInput) {
                durationInput.setAttribute('required', 'required');
            }

            // Hide bulk scheduling form
            hideBulkSchedulingForm();

            // Update submit button text
            if (submitText) {
                submitText.textContent = 'جدولة الحصة';
            }

            console.log('🔧 تم إضافة خاصية required للحقول المرئية');
        }
    }
    
    // Set minimum date to today
    document.getElementById('scheduled_date').min = new Date().toISOString().split('T')[0];

    // Initial setup when page loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 تحميل الصفحة مكتمل');

        try {
            // تحديث المعاينة
            updatePreview();

            // تحديث خيارات الجدولة المتعددة
            updateBulkSchedulingOption();

            // التأكد من إظهار قسم الاشتراك للحصص المجدولة
            const sessionType = document.getElementById('session_type');
            const subscriptionSection = document.getElementById('subscriptionSection');

            if (sessionType && subscriptionSection) {
                if (sessionType.value === 'scheduled') {
                    subscriptionSection.style.display = 'block';
                }
            }

            console.log('✅ تم تهيئة الصفحة بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تهيئة الصفحة:', error);
        }
    });

    // التأكد من تحميل كل شيء
    window.addEventListener('load', function() {
        console.log('🎯 تحميل النافذة مكتمل');

        // إعادة تهيئة في حالة وجود مشاكل
        setTimeout(() => {
            try {
                updateBulkSchedulingOption();
                console.log('🔄 إعادة تهيئة مكتملة');
            } catch (error) {
                console.error('❌ خطأ في إعادة التهيئة:', error);
            }
        }, 500);
    });
    
    // Check availability function
    function checkAvailability() {
        const teacherId = document.getElementById('teacher_id').value;
        const studentId = document.getElementById('student_id').value;
        const bulkSchedule = document.getElementById('bulk_schedule').checked;

        console.log('🔍 فحص التوفر:', {
            teacherId: teacherId,
            studentId: studentId,
            bulkSchedule: bulkSchedule
        });

        if (!teacherId || !studentId) {
            alert('يرجى اختيار المعلم والطالب أولاً');
            return;
        }

        if (bulkSchedule) {
            // فحص التوفر للحصص المتعددة
            checkBulkAvailability();
        } else {
            // فحص التوفر للحصة الواحدة
            checkSingleAvailability();
        }
    }

    function checkSingleAvailability() {
        const date = document.getElementById('scheduled_date').value;
        const time = document.getElementById('scheduled_time').value;

        if (!date || !time) {
            alert('يرجى ملء التاريخ والوقت أولاً');
            return;
        }

        showAvailabilityModal('فحص التوفر للحصة الواحدة', [
            {
                date: date,
                time: time,
                duration: document.getElementById('duration_minutes').value
            }
        ]);
    }

    function checkBulkAvailability() {
        const bulkSessions = document.querySelectorAll('.bulk-session-item');

        if (bulkSessions.length === 0) {
            alert('يرجى إضافة حصص أولاً');
            return;
        }

        const sessions = [];
        let hasEmptyFields = false;

        bulkSessions.forEach((session, index) => {
            const date = session.querySelector('.bulk-date').value;
            const time = session.querySelector('.bulk-time').value;
            const duration = session.querySelector('.bulk-duration').value;

            if (!date || !time) {
                hasEmptyFields = true;
                return;
            }

            sessions.push({
                index: index + 1,
                date: date,
                time: time,
                duration: duration
            });
        });

        if (hasEmptyFields) {
            alert('يرجى ملء التاريخ والوقت لجميع الحصص');
            return;
        }

        if (sessions.length === 0) {
            alert('لا توجد حصص صالحة للفحص');
            return;
        }

        showAvailabilityModal(`فحص التوفر لـ ${sessions.length} حصة`, sessions);
    }

    function showAvailabilityModal(title, sessions) {
        const modal = new bootstrap.Modal(document.getElementById('availabilityModal'));
        const modalTitle = document.querySelector('#availabilityModal .modal-title');
        const result = document.getElementById('availabilityResult');

        modalTitle.textContent = title;
        result.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري فحص التوفر...</div>';

        modal.show();

        // Simulate availability check
        setTimeout(() => {
            let resultHTML = '';

            sessions.forEach((session, index) => {
                const isAvailable = Math.random() > 0.2; // 80% chance of availability
                const statusClass = isAvailable ? 'success' : 'warning';
                const statusIcon = isAvailable ? 'check-circle' : 'exclamation-triangle';
                const statusText = isAvailable ? 'متاح' : 'قد يكون هناك تعارض';

                resultHTML += `
                    <div class="alert alert-${statusClass}">
                        <h6><i class="fas fa-${statusIcon} me-2"></i>الحصة ${session.index || index + 1}</h6>
                        <p class="mb-1"><strong>التاريخ:</strong> ${session.date}</p>
                        <p class="mb-1"><strong>الوقت:</strong> ${session.time}</p>
                        <p class="mb-0"><strong>الحالة:</strong> ${statusText}</p>
                    </div>
                `;
            });

            result.innerHTML = resultHTML;
        }, 2000);
    }
    
    // Bulk scheduling functions

    function showBulkSchedulingForm() {
        console.log('🔄 إظهار نموذج الجدولة المتعددة');

        try {
            // التحقق من وجود العناصر
            const bulkSection = document.getElementById('bulkSchedulingSection');
            const sessionPreview = document.getElementById('sessionPreview');
            const bulkPreview = document.getElementById('bulkSessionsPreview');

            if (!bulkSection || !sessionPreview || !bulkPreview) {
                console.error('❌ عناصر مفقودة في الصفحة');
                return;
            }

            // إخفاء النموذج العادي وإظهار نموذج الجدولة المتعددة
            bulkSection.style.display = 'block';
            sessionPreview.style.display = 'none';
            bulkPreview.style.display = 'block';

            // تهيئة الحصص
            initializeBulkSessions();

            // التمرير إلى القسم الجديد بعد تأخير قصير
            setTimeout(() => {
                bulkSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);

            console.log('✅ تم إظهار نموذج الجدولة المتعددة بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إظهار نموذج الجدولة المتعددة:', error);
        }
    }

    function hideBulkSchedulingForm() {
        console.log('🔄 إخفاء نموذج الجدولة المتعددة');

        try {
            const bulkSection = document.getElementById('bulkSchedulingSection');
            const sessionPreview = document.getElementById('sessionPreview');
            const bulkPreview = document.getElementById('bulkSessionsPreview');
            const bulkList = document.getElementById('bulkSessionsList');

            if (bulkSection) bulkSection.style.display = 'none';
            if (sessionPreview) sessionPreview.style.display = 'block';
            if (bulkPreview) bulkPreview.style.display = 'none';
            if (bulkList) bulkList.innerHTML = '';

            bulkSessionCounter = 0;

            // إعادة تعيين العدادات
            updateSessionCounters(0);

            console.log('✅ تم إخفاء نموذج الجدولة المتعددة بنجاح');

        } catch (error) {
            console.error('❌ خطأ في إخفاء نموذج الجدولة المتعددة:', error);
        }
    }

    function initializeBulkSessions() {
        console.log('🔄 تهيئة الحصص المتعددة');

        try {
            const subscriptionSelect = document.getElementById('subscription_id');
            const bulkList = document.getElementById('bulkSessionsList');

            if (!subscriptionSelect || !bulkList) {
                console.error('❌ عناصر مفقودة للتهيئة');
                return;
            }

            if (!subscriptionSelect.value || subscriptionSelect.value === '') {
                console.log('❌ لم يتم اختيار اشتراك');
                return;
            }

            const selectedOption = subscriptionSelect.selectedOptions[0];
            const sessionsRemaining = parseInt(selectedOption.dataset.sessionsRemaining) || 0;

            console.log('📊 الحصص المتبقية للتهيئة:', sessionsRemaining);

            // Clear existing sessions
            bulkList.innerHTML = '';
            bulkSessionCounter = 0;

            // Update counters
            updateSessionCounters(sessionsRemaining);

            // Show message to user
            if (sessionsRemaining > 0) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info text-center mb-3 col-12';
                alertDiv.innerHTML = `
                    <h6><i class="fas fa-info-circle me-2"></i>جاهز لجدولة ${sessionsRemaining} حصة</h6>
                    <p class="mb-2">اضغط على "إنشاء جميع الحقول تلقائياً" لإنشاء ${sessionsRemaining} حقل فارغ</p>
                    <p class="mb-0 small text-muted">أو استخدم "إضافة حصة يدوياً" لإضافة حصة واحدة في كل مرة</p>
                `;
                bulkList.appendChild(alertDiv);
                console.log('✅ تم إظهار رسالة التوجيه');
            } else {
                console.log('❌ لا توجد حصص متبقية');
            }

        } catch (error) {
            console.error('❌ خطأ في تهيئة الحصص المتعددة:', error);
        }
    }

    function generateAllSessions() {
        console.log('🚀 بدء إنشاء جميع الحقول تلقائياً');

        const subscriptionSelect = document.getElementById('subscription_id');
        if (!subscriptionSelect || !subscriptionSelect.value) {
            alert('يرجى اختيار اشتراك أولاً');
            console.log('❌ لم يتم اختيار اشتراك');
            return;
        }

        const selectedOption = subscriptionSelect.selectedOptions[0];
        const sessionsRemaining = parseInt(selectedOption.dataset.sessionsRemaining) || 0;

        console.log('📊 الحصص المتبقية في الاشتراك:', sessionsRemaining);

        if (sessionsRemaining === 0) {
            alert('لا توجد حصص متبقية في هذا الاشتراك');
            console.log('❌ لا توجد حصص متبقية');
            return;
        }

        if (sessionsRemaining > 20) {
            if (!confirm(`سيتم إنشاء ${sessionsRemaining} حقل. هل أنت متأكد؟`)) {
                return;
            }
        }

        // Clear existing sessions
        const bulkList = document.getElementById('bulkSessionsList');
        bulkList.innerHTML = '';
        bulkSessionCounter = 0;

        console.log(`🔄 إنشاء ${sessionsRemaining} حقل...`);

        // Create all sessions
        for (let i = 0; i < sessionsRemaining; i++) {
            addBulkSession();
        }

        updateSessionCounters(sessionsRemaining);
        updateBulkPreview();

        console.log(`✅ تم إنشاء ${sessionsRemaining} حقل بنجاح`);

        // Show success message
        showSuccessMessage(`تم إنشاء ${sessionsRemaining} حقل فارغ. يرجى ملء التاريخ والوقت لكل حصة.`);
    }

    function updateSessionCounters(count) {
        const badge = document.getElementById('bulkSessionsCountBadge');
        const total = document.getElementById('totalSessionsToCreate');
        const remaining = document.getElementById('remainingSessionsCount');

        if (badge) badge.textContent = `${count} حصة`;
        if (total) total.textContent = count;
        if (remaining) remaining.textContent = count;
    }

    function showSuccessMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.card-body');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    function showWarningMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.card-body');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto remove after 7 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 7000);
    }

    function showErrorMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-times-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.card-body');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto remove after 10 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 10000);
    }

    function clearAllSessions() {
        const currentCount = document.querySelectorAll('.bulk-session-item').length;

        if (currentCount === 0) {
            showWarningMessage('لا توجد حصص لمسحها');
            return;
        }

        if (confirm(`هل أنت متأكد من مسح جميع الحصص (${currentCount} حصة)؟`)) {
            console.log(`🗑️ مسح ${currentCount} حصة`);

            const bulkList = document.getElementById('bulkSessionsList');
            bulkList.innerHTML = '';
            bulkSessionCounter = 0;
            updateSessionCounters(0);
            updateBulkPreview();

            // Show empty state message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-info text-center mb-3 col-12';
            alertDiv.innerHTML = `
                <h6><i class="fas fa-info-circle me-2"></i>لا توجد حصص مجدولة</h6>
                <p class="mb-0">اضغط على "إنشاء جميع الحقول تلقائياً" أو "إضافة حصة يدوياً" لبدء الجدولة</p>
            `;
            bulkList.appendChild(alertDiv);

            showSuccessMessage(`تم مسح ${currentCount} حصة بنجاح`);
            console.log('✅ تم مسح جميع الحصص');
        }
    }

    function addBulkSession() {
        console.log('➕ إضافة حصة جديدة');

        // Check subscription limit
        const subscriptionSelect = document.getElementById('subscription_id');
        if (!subscriptionSelect || !subscriptionSelect.value) {
            alert('يرجى اختيار اشتراك أولاً');
            return;
        }

        const selectedOption = subscriptionSelect.selectedOptions[0];
        const sessionsRemaining = parseInt(selectedOption.dataset.sessionsRemaining) || 0;
        const currentCount = document.querySelectorAll('.bulk-session-item').length;

        console.log('📊 فحص الحدود:', {
            sessionsRemaining: sessionsRemaining,
            currentCount: currentCount
        });

        if (currentCount >= sessionsRemaining) {
            alert(`لا يمكن إضافة المزيد من الحصص. الحد الأقصى هو ${sessionsRemaining} حصة حسب الاشتراك المختار.`);
            console.log('❌ تم الوصول للحد الأقصى');
            return;
        }

        const sessionsList = document.getElementById('bulkSessionsList');
        const sessionIndex = bulkSessionCounter++;

        // Remove any info alerts
        const existingAlerts = sessionsList.querySelectorAll('.alert-info, .alert-warning');
        existingAlerts.forEach(alert => alert.remove());

        const sessionDiv = document.createElement('div');
        sessionDiv.className = 'col-lg-6 mb-3';
        sessionDiv.innerHTML = `
            <div class="card bulk-session-item border-primary">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-primary">
                        <i class="fas fa-calendar-alt me-2"></i>الحصة ${sessionIndex + 1}
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeBulkSession(this)" title="حذف هذه الحصة">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label"><i class="fas fa-calendar me-1"></i>التاريخ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control bulk-date" name="bulk_dates[]" data-bulk-field="true">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label"><i class="fas fa-clock me-1"></i>الوقت <span class="text-danger">*</span></label>
                            <input type="time" class="form-control bulk-time" name="bulk_times[]" data-bulk-field="true">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label"><i class="fas fa-hourglass-half me-1"></i>المدة</label>
                            <select class="form-select bulk-duration" name="bulk_durations[]" required>
                                <option value="30">30 دقيقة</option>
                                <option value="45">45 دقيقة</option>
                                <option value="60" selected>60 دقيقة</option>
                                <option value="90">90 دقيقة</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label"><i class="fas fa-sticky-note me-1"></i>ملاحظات خاصة</label>
                            <textarea class="form-control bulk-notes" name="bulk_notes[]" rows="2" placeholder="ملاحظات اختيارية لهذه الحصة..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        `;

        sessionsList.appendChild(sessionDiv);

        // Set minimum date to today
        const dateInput = sessionDiv.querySelector('.bulk-date');
        dateInput.min = new Date().toISOString().split('T')[0];

        // Add event listeners for preview update
        sessionDiv.querySelectorAll('.bulk-date, .bulk-time, .bulk-duration').forEach(input => {
            input.addEventListener('change', updateBulkPreview);
        });

        // Update counters
        const newCount = document.querySelectorAll('.bulk-session-item').length;
        updateSessionCounters(newCount);
        updateBulkPreview();

        console.log(`✅ تم إضافة الحصة ${sessionIndex + 1}. العدد الحالي: ${newCount}`);

        // Show limit warning if approaching max
        if (newCount >= sessionsRemaining - 1) {
            showWarningMessage(`تم إضافة ${newCount} من ${sessionsRemaining} حصة. ${sessionsRemaining - newCount} حصة متبقية.`);
        }
    }

    function removeBulkSession(button) {
        const sessionItem = button.closest('.col-lg-6');
        sessionItem.remove();
        updateBulkSessionNumbers();

        // Update counters
        const currentCount = document.querySelectorAll('.bulk-session-item').length;
        updateSessionCounters(currentCount);
        updateBulkPreview();

        // Show message if no sessions left
        if (currentCount === 0) {
            const sessionsList = document.getElementById('bulkSessionsList');
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning text-center mb-3';
            alertDiv.innerHTML = `
                <h6><i class="fas fa-exclamation-triangle me-2"></i>لا توجد حصص مجدولة</h6>
                <p class="mb-0">اضغط على "إنشاء جميع الحقول تلقائياً" أو "إضافة حصة يدوياً" لبدء الجدولة</p>
            `;
            sessionsList.appendChild(alertDiv);
        }
    }

    function updateBulkSessionNumbers() {
        const sessions = document.querySelectorAll('.bulk-session-item');
        sessions.forEach((session, index) => {
            const header = session.querySelector('h6');
            if (header) {
                header.innerHTML = `<i class="fas fa-calendar-alt me-2"></i>الحصة ${index + 1}`;
            }
        });
    }

    function updateBulkPreview() {
        const teacherSelect = document.getElementById('teacher_id');
        const studentSelect = document.getElementById('student_id');
        const typeSelect = document.getElementById('session_type');
        const subscriptionSelect = document.getElementById('subscription_id');
        const bulkSessions = document.querySelectorAll('.bulk-session-item');
        const previewList = document.getElementById('bulkPreviewList');

        if (!teacherSelect.value || !studentSelect.value || bulkSessions.length === 0) {
            previewList.innerHTML = '<p class="text-muted">يرجى ملء البيانات الأساسية وإضافة حصص</p>';
            return;
        }

        const teacherName = teacherSelect.selectedOptions[0]?.text || 'غير محدد';
        const studentName = studentSelect.selectedOptions[0]?.text || 'غير محدد';
        const sessionType = {
            'trial': 'حصة تجريبية',
            'makeup': 'حصة تعويضية',
            'scheduled': 'حصة مجدولة'
        }[typeSelect.value] || 'حصة مجدولة';

        let previewHTML = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>المعلم:</strong> ${teacherName}
                </div>
                <div class="col-md-6">
                    <strong>الطالب:</strong> ${studentName}
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>نوع الحصة:</strong> ${sessionType}
                </div>
                <div class="col-md-6">
                    <strong>عدد الحصص:</strong> ${bulkSessions.length}
                </div>
            </div>
        `;

        if (subscriptionSelect.value) {
            const subscriptionText = subscriptionSelect.selectedOptions[0]?.text || '';
            previewHTML += `
                <div class="alert alert-info mb-3">
                    <strong>الاشتراك:</strong> ${subscriptionText}
                </div>
            `;
        }

        previewHTML += '<h6>جدول الحصص:</h6><div class="table-responsive"><table class="table table-sm table-striped"><thead><tr><th>الحصة</th><th>التاريخ</th><th>الوقت</th><th>المدة</th></tr></thead><tbody>';

        bulkSessions.forEach((session, index) => {
            const date = session.querySelector('.bulk-date').value || 'غير محدد';
            const time = session.querySelector('.bulk-time').value || 'غير محدد';
            const duration = session.querySelector('.bulk-duration').value || '60';

            previewHTML += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${date}</td>
                    <td>${time}</td>
                    <td>${duration} دقيقة</td>
                </tr>
            `;
        });

        previewHTML += '</tbody></table></div>';
        previewList.innerHTML = previewHTML;
    }

    function generateWeeklySchedule() {
        console.log('📅 فتح نافذة الجدولة الأسبوعية');

        // Check if subscription is selected
        const subscriptionSelect = document.getElementById('subscription_id');
        if (!subscriptionSelect || !subscriptionSelect.value) {
            alert('يرجى اختيار اشتراك أولاً');
            return;
        }

        const selectedOption = subscriptionSelect.selectedOptions[0];
        const sessionsRemaining = parseInt(selectedOption.dataset.sessionsRemaining) || 0;

        if (sessionsRemaining === 0) {
            alert('لا توجد حصص متبقية في هذا الاشتراك');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-calendar-week me-2"></i>جدولة أسبوعية
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            سيتم إنشاء حصص تلقائياً حسب الأيام المختارة (الحد الأقصى: ${sessionsRemaining} حصة)
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label"><i class="fas fa-calendar-day me-1"></i>تاريخ البداية</label>
                                <input type="date" class="form-control" id="weeklyStartDate" min="${new Date().toISOString().split('T')[0]}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label"><i class="fas fa-calendar-day me-1"></i>تاريخ النهاية</label>
                                <input type="date" class="form-control" id="weeklyEndDate" min="${new Date().toISOString().split('T')[0]}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-clock me-1"></i>الوقت الثابت</label>
                            <input type="time" class="form-control" id="weeklyTime" value="10:00">
                            <div class="form-text">سيتم استخدام هذا الوقت لجميع الحصص</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-hourglass-half me-1"></i>مدة كل حصة</label>
                            <select class="form-select" id="weeklyDuration">
                                <option value="30">30 دقيقة</option>
                                <option value="45">45 دقيقة</option>
                                <option value="60" selected>60 دقيقة</option>
                                <option value="90">90 دقيقة</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-calendar-check me-1"></i>أيام الأسبوع</label>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day_0" value="0">
                                        <label class="form-check-label" for="day_0">الأحد</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day_1" value="1">
                                        <label class="form-check-label" for="day_1">الاثنين</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day_2" value="2">
                                        <label class="form-check-label" for="day_2">الثلاثاء</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day_3" value="3">
                                        <label class="form-check-label" for="day_3">الأربعاء</label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day_4" value="4">
                                        <label class="form-check-label" for="day_4">الخميس</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day_5" value="5">
                                        <label class="form-check-label" for="day_5">الجمعة</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="day_6" value="6">
                                        <label class="form-check-label" for="day_6">السبت</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-text">اختر الأيام التي تريد جدولة الحصص فيها</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-sticky-note me-1"></i>ملاحظات عامة</label>
                            <textarea class="form-control" id="weeklyNotes" rows="2" placeholder="ملاحظات ستطبق على جميع الحصص..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" onclick="applyWeeklySchedule()">
                            <i class="fas fa-magic me-1"></i>إنشاء الجدول
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();

        // Add event listener for start date change
        const startDateInput = modal.querySelector('#weeklyStartDate');
        const endDateInput = modal.querySelector('#weeklyEndDate');

        startDateInput.addEventListener('change', function() {
            if (this.value && !endDateInput.value) {
                // Set end date to 30 days after start date
                const startDate = new Date(this.value);
                const endDate = new Date(startDate);
                endDate.setDate(startDate.getDate() + 30);
                endDateInput.value = endDate.toISOString().split('T')[0];
            }
            // Update minimum end date
            endDateInput.min = this.value;
        });

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    function applyWeeklySchedule() {
        console.log('📅 تطبيق الجدولة الأسبوعية');

        const startDate = document.getElementById('weeklyStartDate').value;
        const endDate = document.getElementById('weeklyEndDate').value;
        const time = document.getElementById('weeklyTime').value;
        const duration = document.getElementById('weeklyDuration').value;
        const notes = document.getElementById('weeklyNotes').value;
        const selectedDays = [];

        for (let i = 0; i <= 6; i++) {
            if (document.getElementById(`day_${i}`).checked) {
                selectedDays.push(i);
            }
        }

        console.log('📊 بيانات الجدولة الأسبوعية:', {
            startDate: startDate,
            endDate: endDate,
            time: time,
            duration: duration,
            selectedDays: selectedDays
        });

        if (!startDate || !endDate || !time || selectedDays.length === 0) {
            alert('يرجى ملء جميع الحقول المطلوبة واختيار يوم واحد على الأقل');
            return;
        }

        // التحقق من أن تاريخ النهاية بعد تاريخ البداية
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (end <= start) {
            alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            return;
        }

        // Get remaining sessions count
        const subscriptionSelect = document.getElementById('subscription_id');
        const selectedOption = subscriptionSelect.selectedOptions[0];
        const sessionsRemaining = parseInt(selectedOption.dataset.sessionsRemaining) || 0;

        if (sessionsRemaining === 0) {
            alert('لا توجد حصص متبقية في الاشتراك');
            return;
        }

        // Clear existing sessions
        const bulkList = document.getElementById('bulkSessionsList');
        bulkList.innerHTML = '';
        bulkSessionCounter = 0;

        // Generate all possible dates within the range
        const possibleDates = [];
        let currentDate = new Date(start);

        console.log(`🔄 جمع التواريخ المتاحة من ${startDate} إلى ${endDate}`);

        while (currentDate <= end) {
            const dayOfWeek = currentDate.getDay();

            if (selectedDays.includes(dayOfWeek)) {
                possibleDates.push(new Date(currentDate));
            }

            currentDate.setDate(currentDate.getDate() + 1);
        }

        console.log(`📅 تم العثور على ${possibleDates.length} تاريخ متاح`);

        if (possibleDates.length === 0) {
            alert('لا توجد تواريخ متاحة في الفترة المحددة للأيام المختارة');
            return;
        }

        // Shuffle the dates randomly
        for (let i = possibleDates.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [possibleDates[i], possibleDates[j]] = [possibleDates[j], possibleDates[i]];
        }

        console.log(`🎲 تم خلط التواريخ عشوائياً`);

        // Take only the number of sessions we need
        const selectedDates = possibleDates.slice(0, Math.min(sessionsRemaining, possibleDates.length));

        console.log(`🔄 بدء إنشاء ${selectedDates.length} حصة من أصل ${sessionsRemaining} مطلوبة`);

        let sessionsCreated = 0;

        for (const sessionDate of selectedDates) {
            // Create session manually to avoid limit checks
            const sessionIndex = bulkSessionCounter++;
            const sessionDiv = document.createElement('div');
            sessionDiv.className = 'col-lg-6 mb-3';
            sessionDiv.innerHTML = `
                <div class="card bulk-session-item border-primary">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 text-primary">
                            <i class="fas fa-calendar-alt me-2"></i>الحصة ${sessionIndex + 1}
                            <small class="text-muted ms-2">(${getDayName(sessionDate.getDay())})</small>
                        </h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeBulkSession(this)" title="حذف هذه الحصة">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label"><i class="fas fa-calendar me-1"></i>التاريخ <span class="text-danger">*</span></label>
                                <input type="date" class="form-control bulk-date" name="bulk_dates[]" value="${sessionDate.toISOString().split('T')[0]}" required>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label"><i class="fas fa-clock me-1"></i>الوقت <span class="text-danger">*</span></label>
                                <input type="time" class="form-control bulk-time" name="bulk_times[]" value="${time}" required>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label"><i class="fas fa-hourglass-half me-1"></i>المدة</label>
                                <select class="form-select bulk-duration" name="bulk_durations[]" required>
                                    <option value="30" ${duration === '30' ? 'selected' : ''}>30 دقيقة</option>
                                    <option value="45" ${duration === '45' ? 'selected' : ''}>45 دقيقة</option>
                                    <option value="60" ${duration === '60' ? 'selected' : ''}>60 دقيقة</option>
                                    <option value="90" ${duration === '90' ? 'selected' : ''}>90 دقيقة</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label"><i class="fas fa-sticky-note me-1"></i>ملاحظات خاصة</label>
                                <textarea class="form-control bulk-notes" name="bulk_notes[]" rows="2" placeholder="ملاحظات اختيارية لهذه الحصة...">${notes}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            bulkList.appendChild(sessionDiv);

            // Add event listeners
            sessionDiv.querySelectorAll('.bulk-date, .bulk-time, .bulk-duration').forEach(input => {
                input.addEventListener('change', updateBulkPreview);
            });

            sessionsCreated++;
            console.log(`✅ تم إنشاء الحصة ${sessionsCreated}: ${sessionDate.toISOString().split('T')[0]} (${getDayName(sessionDate.getDay())})`);
        }

        // Update counters and preview
        updateSessionCounters(sessionsCreated);
        updateBulkPreview();

        console.log(`🎉 تم إنشاء ${sessionsCreated} حصة بنجاح`);

        // Close modal
        const modal = document.querySelector('.modal.show');
        const modalInstance = bootstrap.Modal.getInstance(modal);
        modalInstance.hide();

        // Show success message
        const dayNames = selectedDays.map(day => getDayName(day)).join('، ');
        showSuccessMessage(`تم إنشاء ${sessionsCreated} حصة عشوائية من ${startDate} إلى ${endDate} في أيام: ${dayNames}. يرجى مراجعة المواعيد قبل الحفظ.`);
    }

    // Form validation
    document.getElementById('sessionForm').addEventListener('submit', function(e) {
        console.log('📝 بدء إرسال النموذج');

        const teacherId = document.getElementById('teacher_id').value;
        const studentId = document.getElementById('student_id').value;
        const bulkSchedule = document.getElementById('bulk_schedule').checked;

        console.log('🔍 بيانات النموذج الأساسية:', {
            teacherId: teacherId,
            studentId: studentId,
            bulkSchedule: bulkSchedule,
            formAction: this.action,
            formMethod: this.method
        });

        if (!teacherId || !studentId || teacherId.trim() === '' || studentId.trim() === '') {
            e.preventDefault();
            showErrorMessage('يرجى اختيار المعلم والطالب');
            console.log('❌ بيانات أساسية مفقودة');
            return;
        }

        // Additional validation for numeric IDs
        if (isNaN(parseInt(teacherId)) || isNaN(parseInt(studentId))) {
            e.preventDefault();
            showErrorMessage('خطأ في معرفات المعلم أو الطالب');
            console.log('❌ معرفات غير صالحة');
            return;
        }

        if (bulkSchedule) {
            console.log('🔍 التحقق من صحة الحصص المتعددة');

            // Validate bulk sessions
            const bulkSessions = document.querySelectorAll('.bulk-session-item');
            console.log(`📊 عدد الحصص المتعددة: ${bulkSessions.length}`);

            if (bulkSessions.length === 0) {
                e.preventDefault();
                showErrorMessage('يرجى إضافة حصة واحدة على الأقل');
                console.log('❌ لا توجد حصص متعددة');
                return;
            }

            // Log all bulk session data
            const formData = new FormData(this);
            console.log('📋 بيانات النموذج المرسلة:');
            for (let [key, value] of formData.entries()) {
                console.log(`   ${key}: ${value}`);
            }

            // Check subscription limit
            const subscriptionSelect = document.getElementById('subscription_id');
            const subscriptionId = subscriptionSelect.value;

            // Validate subscription for scheduled sessions
            const sessionType = document.getElementById('session_type').value;
            if (sessionType === 'scheduled' && (!subscriptionId || subscriptionId.trim() === '')) {
                e.preventDefault();
                showErrorMessage('يرجى اختيار اشتراك للحصص المجدولة');
                console.log('❌ لم يتم اختيار اشتراك للحصة المجدولة');
                return;
            }

            const selectedOption = subscriptionSelect.selectedOptions[0];
            const sessionsRemaining = selectedOption ? parseInt(selectedOption.dataset.sessionsRemaining) || 0 : 0;

            if (bulkSessions.length > sessionsRemaining) {
                e.preventDefault();
                showErrorMessage(`عدد الحصص المجدولة (${bulkSessions.length}) يتجاوز الحصص المتبقية في الاشتراك (${sessionsRemaining})`);
                return;
            }

            let hasError = false;
            const usedTimes = new Set();
            const errors = [];

            // Add required attribute to bulk fields
            const bulkDateFields = document.querySelectorAll('[data-bulk-field="true"][name="bulk_dates[]"]');
            const bulkTimeFields = document.querySelectorAll('[data-bulk-field="true"][name="bulk_times[]"]');

            bulkDateFields.forEach(field => field.setAttribute('required', 'required'));
            bulkTimeFields.forEach(field => field.setAttribute('required', 'required'));

            bulkSessions.forEach((session, index) => {
                const date = session.querySelector('.bulk-date').value;
                const time = session.querySelector('.bulk-time').value;

                if (!date || !time) {
                    hasError = true;
                    errors.push(`الحصة ${index + 1}: يرجى ملء التاريخ والوقت`);
                    return;
                }

                // Check for duplicate times
                const dateTimeKey = `${date}_${time}`;
                if (usedTimes.has(dateTimeKey)) {
                    hasError = true;
                    errors.push(`الحصة ${index + 1}: يوجد تكرار في الموعد`);
                    return;
                }
                usedTimes.add(dateTimeKey);

                // Check if date is not in the past
                const selectedDateTime = new Date(date + 'T' + time);
                const now = new Date();

                if (selectedDateTime <= now) {
                    hasError = true;
                    errors.push(`الحصة ${index + 1}: لا يمكن جدولة حصة في الماضي`);
                    return;
                }
            });

            if (hasError) {
                e.preventDefault();
                showErrorMessage('يوجد أخطاء في البيانات:\n' + errors.join('\n'));
                console.log('❌ أخطاء في التحقق:', errors);
                return;
            }

            console.log(`✅ تم التحقق من ${bulkSessions.length} حصة بنجاح`);
        } else {
            console.log('🔍 التحقق من صحة الحصة الواحدة');

            // Validate single session
            const date = document.getElementById('scheduled_date').value;
            const time = document.getElementById('scheduled_time').value;

            console.log('📅 بيانات الحصة الواحدة:', {
                date: date,
                time: time
            });

            if (!date || !time) {
                e.preventDefault();
                showErrorMessage('يرجى ملء التاريخ والوقت للحصة');
                console.log('❌ بيانات الحصة الواحدة مفقودة');
                return;
            }

            // Check if date is not in the past
            const selectedDateTime = new Date(date + 'T' + time);
            const now = new Date();

            if (selectedDateTime <= now) {
                e.preventDefault();
                showErrorMessage('لا يمكن جدولة حصة في الماضي');
                console.log('❌ تاريخ الحصة في الماضي');
                return;
            }

            console.log('✅ تم التحقق من الحصة الواحدة بنجاح');
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        const sessionsCount = bulkSchedule ? document.querySelectorAll('.bulk-session-item').length : 1;

        if (bulkSchedule) {
            submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>جاري جدولة ${sessionsCount} حصة...`;
            showSuccessMessage(`بدء جدولة ${sessionsCount} حصة. يرجى الانتظار...`);
        } else {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الجدولة...';
        }

        submitBtn.disabled = true;

        console.log(`🚀 بدء إرسال النموذج - ${sessionsCount} حصة`);
        console.log(`📍 إرسال إلى: ${this.action}`);
        console.log(`📋 طريقة الإرسال: ${this.method}`);

        // Re-enable button after timeout (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            console.log('⏰ انتهت مهلة الانتظار - إعادة تفعيل الزر');
        }, 15000);
    });
</script>
{% endblock %}
