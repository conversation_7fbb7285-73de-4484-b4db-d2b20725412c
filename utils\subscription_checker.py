#!/usr/bin/env python3
"""
نظام فحص انتهاء الاشتراكات وإرسال تذكيرات
"""

from datetime import datetime, timedelta
from models import Subscription, EmailSettings, db
from utils.email_service import EmailService
from utils.user_management import get_academy_variables
from flask import url_for

def check_expiring_subscriptions():
    """فحص الاشتراكات التي على وشك الانتهاء وإرسال تذكيرات"""
    
    # التحقق من تفعيل إشعارات انتهاء الاشتراك
    email_settings = EmailSettings.query.first()
    if not email_settings or not email_settings.subscription_expiry_enabled:
        print("إشعارات انتهاء الاشتراك معطلة")
        return
    
    # البحث عن الاشتراكات النشطة
    active_subscriptions = Subscription.query.filter_by(status='active').all()
    
    if not active_subscriptions:
        print("لا توجد اشتراكات نشطة")
        return
    
    email_service = EmailService()
    academy_vars = get_academy_variables()
    
    notifications_sent = 0
    
    for subscription in active_subscriptions:
        try:
            # فحص انتهاء المدة (3 أيام قبل الانتهاء)
            days_until_expiry = None
            if subscription.end_date:
                days_until_expiry = (subscription.end_date - datetime.utcnow()).days
                
                if days_until_expiry <= 3 and days_until_expiry > 0:
                    # إرسال تذكير انتهاء المدة
                    send_expiry_notification(
                        subscription, 
                        email_service, 
                        academy_vars,
                        days_remaining=days_until_expiry,
                        reason='time'
                    )
                    notifications_sent += 1
                    continue
            
            # فحص انتهاء الحصص (حصتان أو أقل متبقية)
            if subscription.sessions_remaining <= 2 and subscription.sessions_remaining > 0:
                # إرسال تذكير انتهاء الحصص
                send_expiry_notification(
                    subscription, 
                    email_service, 
                    academy_vars,
                    sessions_remaining=subscription.sessions_remaining,
                    reason='sessions'
                )
                notifications_sent += 1
                
        except Exception as e:
            print(f"خطأ في فحص اشتراك {subscription.id}: {str(e)}")
    
    print(f"تم إرسال {notifications_sent} تذكير انتهاء اشتراك")
    return notifications_sent

def send_expiry_notification(subscription, email_service, academy_vars, days_remaining=None, sessions_remaining=None, reason='time'):
    """إرسال إشعار انتهاء الاشتراك"""
    
    try:
        # تحديد سبب الانتهاء
        if reason == 'time' and days_remaining:
            end_date = subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else 'غير محدد'
        elif reason == 'sessions' and sessions_remaining:
            end_date = f"متبقي {sessions_remaining} حصة فقط"
        else:
            end_date = 'قريباً'
        
        # إعداد متغيرات الإيميل
        email_vars = {
            'user_name': subscription.user.full_name,
            'package_name': subscription.package.name,
            'days_remaining': days_remaining,
            'sessions_remaining': sessions_remaining,
            'end_date': end_date,
            'renewal_url': url_for('student.packages', _external=True),
            **academy_vars
        }
        
        # إرسال الإشعار
        success, message = email_service.send_template_email(
            subscription.user.email, 
            'subscription_expiring', 
            email_vars
        )
        
        if success:
            print(f"تم إرسال تذكير انتهاء اشتراك إلى {subscription.user.email}")
            
            # تسجيل أن التذكير تم إرساله (يمكن إضافة حقل في قاعدة البيانات لتجنب الإرسال المتكرر)
            subscription.expiry_reminder_sent = True
            db.session.commit()
            
        else:
            print(f"فشل إرسال تذكير انتهاء اشتراك إلى {subscription.user.email}: {message}")
            
    except Exception as e:
        print(f"خطأ في إرسال تذكير انتهاء اشتراك: {str(e)}")

def check_expired_subscriptions():
    """فحص الاشتراكات المنتهية وتحديث حالتها"""
    
    # البحث عن الاشتراكات النشطة المنتهية
    expired_subscriptions = Subscription.query.filter(
        Subscription.status == 'active',
        Subscription.end_date <= datetime.utcnow()
    ).all()
    
    expired_count = 0
    
    for subscription in expired_subscriptions:
        try:
            subscription.status = 'expired'
            db.session.commit()
            expired_count += 1
            print(f"تم تحديث حالة اشتراك {subscription.user.email} إلى منتهي")
            
        except Exception as e:
            print(f"خطأ في تحديث حالة اشتراك {subscription.id}: {str(e)}")
            db.session.rollback()
    
    print(f"تم تحديث {expired_count} اشتراك منتهي")
    return expired_count

def run_subscription_maintenance():
    """تشغيل صيانة الاشتراكات (فحص الانتهاء + التذكيرات)"""
    
    print("🔄 بدء صيانة الاشتراكات...")
    print("=" * 50)
    
    # فحص الاشتراكات المنتهية
    print("1️⃣ فحص الاشتراكات المنتهية...")
    expired_count = check_expired_subscriptions()
    
    # فحص الاشتراكات التي على وشك الانتهاء
    print("\n2️⃣ فحص الاشتراكات التي على وشك الانتهاء...")
    notifications_sent = check_expiring_subscriptions()
    
    print("\n" + "=" * 50)
    print("📊 نتائج صيانة الاشتراكات:")
    print(f"   🔄 اشتراكات تم تحديثها إلى منتهية: {expired_count}")
    print(f"   📧 تذكيرات انتهاء تم إرسالها: {notifications_sent}")
    print("✅ انتهت صيانة الاشتراكات")
    
    return {
        'expired_updated': expired_count,
        'notifications_sent': notifications_sent
    }

if __name__ == "__main__":
    # يمكن تشغيل هذا السكريبت كمهمة مجدولة (cron job)
    from app import app
    
    with app.app_context():
        run_subscription_maintenance()
