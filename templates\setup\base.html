<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}إعداد النظام - نظام إدارة الأكاديميات{% endblock %}</title>
    
    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome - Multiple CDN sources for reliability -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Fallback Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css">
    <!-- Another fallback -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">

    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Tajawal', sans-serif !important;
        }

        /* Fallback for Font Awesome icons if CDN fails */
        .fas, .far, .fab, .fal, .fad, .fa {
            font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome", sans-serif !important;
            font-weight: 900;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }

        /* Specific icon fallbacks */
        .fa-rocket:before { content: "🚀"; }
        .fa-quran:before { content: "📖"; }
        .fa-graduation-cap:before { content: "🎓"; }
        .fa-credit-card:before { content: "💳"; }
        .fa-chart-line:before { content: "📈"; }
        .fa-mobile-alt:before { content: "📱"; }
        .fa-info-circle:before { content: "ℹ️"; }
        .fa-clock:before { content: "🕐"; }
        .fa-arrow-left:before { content: "←"; }
        .fa-forward:before { content: "⏩"; }
        .fa-redo:before { content: "🔄"; }
        .fa-check:before { content: "✅"; }
        .fa-times:before { content: "❌"; }
        .fa-user:before { content: "👤"; }
        .fa-envelope:before { content: "✉️"; }
        .fa-lock:before { content: "🔒"; }
        .fa-cog:before { content: "⚙️"; }
        .fa-home:before { content: "🏠"; }
        .fa-phone:before { content: "📞"; }
        .fa-globe:before { content: "🌐"; }
        .fa-save:before { content: "💾"; }
        .fa-edit:before { content: "✏️"; }
        .fa-trash:before { content: "🗑️"; }
        .fa-plus:before { content: "+"; }
        .fa-minus:before { content: "-"; }
        .fa-search:before { content: "🔍"; }
        .fa-star:before { content: "⭐"; }
        .fa-heart:before { content: "❤️"; }
        .fa-warning:before, .fa-exclamation-triangle:before { content: "⚠️"; }
        .fa-shield-alt:before { content: "🛡️"; }
        
        /* Company name styling */
        .company-name {
            color: #dc3545 !important; /* أحمر بسيط بدون تأثيرات */
            font-weight: 700;
        }

        /* Responsive company name */
        @media (max-width: 768px) {
            .company-name {
                font-size: 0.9em;
                display: block;
                margin: 0.5rem 0;
            }
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .setup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
            margin: 20px;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .setup-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .setup-content {
            padding: 2rem;
        }
        
        .setup-progress {
            margin-bottom: 2rem;
        }
        
        .progress {
            height: 8px;
            border-radius: 10px;
            background-color: #e9ecef;
        }
        
        .progress-bar {
            border-radius: 10px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
            font-size: 0.85rem;
        }
        
        .step {
            text-align: center;
            flex: 1;
        }
        
        .step.active {
            color: #667eea;
            font-weight: 600;
        }
        
        .step.completed {
            color: #28a745;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-outline-secondary {
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .marketation-brand {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .marketation-brand .brand-logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        @media (max-width: 768px) {
            .setup-card {
                margin: 10px;
            }
            
            .setup-header {
                padding: 1.5rem;
            }
            
            .setup-header h1 {
                font-size: 1.5rem;
            }
            
            .setup-content {
                padding: 1.5rem;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <h1>{% block header_title %}إعداد النظام{% endblock %}</h1>
                <p>{% block header_subtitle %}نظام إدارة الأكاديميات التعليمية{% endblock %}</p>
            </div>
            
            <div class="setup-content">
                {% block progress %}{% endblock %}
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
                
                <!-- Marketation Branding -->
                <div class="marketation-brand">
                    <div class="brand-logo">
                        <i class="fas fa-rocket me-2"></i>Marketation
                    </div>
                    <p class="mb-1">نظام إدارة الأكاديميات التعليمية</p>
                    <p class="mb-0">تم التطوير بواسطة <strong>شركة ماركتيشن</strong> للتسويق الإلكتروني والحلول البرمجية</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Font Awesome Check Script -->
    <script>
        // Check if Font Awesome loaded properly
        document.addEventListener('DOMContentLoaded', function() {
            // Create a test element to check if Font Awesome is working
            var testElement = document.createElement('i');
            testElement.className = 'fas fa-check';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            document.body.appendChild(testElement);

            // Check if the icon has proper font family
            var computedStyle = window.getComputedStyle(testElement);
            var fontFamily = computedStyle.getPropertyValue('font-family');

            // If Font Awesome didn't load, show a notice
            if (!fontFamily.includes('Font Awesome')) {
                console.log('Font Awesome fallback icons are being used');

                // Add a small notice for debugging
                var notice = document.createElement('div');
                notice.style.cssText = 'position:fixed;bottom:10px;right:10px;background:#f8f9fa;padding:5px 10px;border-radius:5px;font-size:12px;color:#6c757d;z-index:9999;';
                notice.innerHTML = 'استخدام أيقونات احتياطية';
                document.body.appendChild(notice);

                // Remove notice after 3 seconds
                setTimeout(function() {
                    if (notice.parentNode) {
                        notice.parentNode.removeChild(notice);
                    }
                }, 3000);
            }

            // Clean up test element
            document.body.removeChild(testElement);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
