{% extends "base.html" %}

{% block title %}إدارة الاشتراكات - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}إدارة الاشتراكات{% endblock %}

{% block content %}
<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">حالة الاشتراك</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {{ 'selected' if status_filter == 'pending' }}>معلق</option>
                            <option value="paid_pending_approval" {{ 'selected' if status_filter == 'paid_pending_approval' }}>مدفوع - في انتظار الموافقة</option>
                            <option value="active" {{ 'selected' if status_filter == 'active' }}>نشط</option>
                            <option value="expired" {{ 'selected' if status_filter == 'expired' }}>منتهي</option>
                            <option value="cancelled" {{ 'selected' if status_filter == 'cancelled' }}>ملغي</option>
                            <option value="rejected" {{ 'selected' if status_filter == 'rejected' }}>مرفوض</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ url_for('admin.subscriptions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إزالة الفلاتر
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">اشتراكات معلقة</div>
                        <div class="h5 mb-0">{{ subscriptions.items|selectattr('status', 'equalto', 'pending')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">اشتراكات نشطة</div>
                        <div class="h5 mb-0">{{ subscriptions.items|selectattr('status', 'equalto', 'active')|list|length }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">إجمالي الاشتراكات</div>
                        <div class="h5 mb-0">{{ subscriptions.total }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-credit-card fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-primary text-uppercase mb-1">إيرادات الشهر</div>
                        <div class="h5 mb-0">{{ format_currency(subscriptions.items|selectattr('status', 'equalto', 'active')|map(attribute='package.price')|sum) }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscriptions Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>قائمة الاشتراكات
                </h5>
                <span class="badge bg-primary">{{ subscriptions.total }} اشتراك</span>
            </div>
            <div class="card-body">
                {% if subscriptions.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الطالب</th>
                                <th>الباقة</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>تاريخ الشراء</th>
                                <th>تاريخ التفعيل</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحصص المتبقية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for subscription in subscriptions.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                             style="width: 32px; height: 32px; font-size: 14px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ subscription.user.full_name }}</div>
                                            <div class="small text-muted">{{ subscription.user.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-bold">{{ subscription.package.name }}</div>
                                    <div class="small text-muted">{{ subscription.package.sessions_count }} حصة</div>
                                </td>
                                <td class="fw-bold text-success">${{ "%.2f"|format(subscription.package.price) }}</td>
                                <td>
                                    {% if subscription.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% elif subscription.status == 'paid_pending_approval' %}
                                        <span class="badge bg-info">مدفوع - في انتظار الموافقة</span>
                                    {% elif subscription.status == 'active' %}
                                        <span class="badge bg-success">نشط</span>
                                    {% elif subscription.status == 'expired' %}
                                        <span class="badge bg-danger">منتهي</span>
                                    {% elif subscription.status == 'cancelled' %}
                                        <span class="badge bg-secondary">ملغي</span>
                                    {% elif subscription.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>{{ subscription.purchase_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ subscription.approval_date.strftime('%Y-%m-%d') if subscription.approval_date else '-' }}</td>
                                <td>{{ subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else '-' }}</td>
                                <td>
                                    {% if subscription.status == 'active' %}
                                        <span class="badge bg-info">{{ subscription.sessions_remaining or 0 }}</span>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if subscription.status == 'pending' %}
                                            <button type="button" class="btn btn-sm btn-success"
                                                    onclick="approveManualPayment({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                    title="الموافقة على الاشتراك">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    onclick="rejectManualPayment({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                    title="رفض الاشتراك">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        {% elif subscription.status == 'paid_pending_approval' %}
                                            <button type="button" class="btn btn-sm btn-success"
                                                    onclick="approvePayment({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                    title="الموافقة على الدفع">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    onclick="rejectPayment({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                    title="رفض الدفع">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        {% endif %}
                                        
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" data-bs-target="#subscriptionModal{{ subscription.id }}"
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        {% if subscription.status == 'active' %}
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                    onclick="suspendSubscription({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                    title="إيقاف مؤقت">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        {% endif %}

                                        {% if subscription.status == 'suspended' %}
                                            <button type="button" class="btn btn-sm btn-outline-success"
                                                    onclick="reactivateSubscription({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                    title="إعادة تفعيل">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        {% endif %}

                                        {% if subscription.status in ['active', 'suspended', 'pending'] %}
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="cancelSubscription({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                    title="إلغاء الاشتراك">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        {% endif %}

                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="deleteSubscription({{ subscription.id }}, '{{ subscription.user.full_name }}', '{{ subscription.package.name }}')"
                                                title="حذف الاشتراك"
                                                {% if subscription.status == 'active' %}
                                                data-bs-toggle="tooltip"
                                                data-bs-placement="top"
                                                data-bs-title="تحذير: الاشتراك نشط حالياً"
                                                {% endif %}>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if subscriptions.pages > 1 %}
                <nav aria-label="صفحات الاشتراكات">
                    <ul class="pagination justify-content-center">
                        {% if subscriptions.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.subscriptions', page=subscriptions.prev_num, status=status_filter) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in subscriptions.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != subscriptions.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.subscriptions', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if subscriptions.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.subscriptions', page=subscriptions.next_num, status=status_filter) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد اشتراكات</h5>
                    <p class="text-muted">لم يتم العثور على اشتراكات بالمعايير المحددة.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Subscription Details Modals -->
{% for subscription in subscriptions.items %}
<div class="modal fade" id="subscriptionModal{{ subscription.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الاشتراك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الطالب</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>{{ subscription.user.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>{{ subscription.user.email }}</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>{{ subscription.user.phone or '-' }}</td>
                            </tr>
                        </table>
                        
                        <h6>معلومات الباقة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>اسم الباقة:</strong></td>
                                <td>{{ subscription.package.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>السعر:</strong></td>
                                <td>{{ format_currency(subscription.package.price) }}</td>
                            </tr>
                            <tr>
                                <td><strong>عدد الحصص:</strong></td>
                                <td>{{ subscription.package.sessions_count }}</td>
                            </tr>
                            <tr>
                                <td><strong>مدة الباقة:</strong></td>
                                <td>{{ subscription.package.duration_days }} يوم</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات الاشتراك</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'warning' if subscription.status == 'pending' else 'success' if subscription.status == 'active' else 'danger' }}">
                                        {{ subscription.status }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الشراء:</strong></td>
                                <td>{{ subscription.purchase_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التفعيل:</strong></td>
                                <td>{{ subscription.approval_date.strftime('%Y-%m-%d %H:%M') if subscription.approval_date else 'لم يتم التفعيل' }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ البداية:</strong></td>
                                <td>{{ subscription.start_date.strftime('%Y-%m-%d') if subscription.start_date else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الانتهاء:</strong></td>
                                <td>{{ subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else '-' }}</td>
                            </tr>
                        </table>
                        
                        {% if subscription.status == 'active' %}
                        <h6>إحصائيات الاستخدام</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الحصص المستخدمة:</strong></td>
                                <td>{{ subscription.sessions_used or 0 }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحصص المتبقية:</strong></td>
                                <td>{{ subscription.sessions_remaining or 0 }}</td>
                            </tr>
                            <tr>
                                <td><strong>نسبة الاستخدام:</strong></td>
                                <td>
                                    {% set usage_percent = (subscription.sessions_used / subscription.package.sessions_count * 100) if subscription.package.sessions_count > 0 else 0 %}
                                    {{ "%.1f"|format(usage_percent) }}%
                                </td>
                            </tr>
                        </table>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Payment Information -->
                {% if subscription.payments %}
                <hr>
                <h6>معلومات الدفع</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in subscription.payments %}
                            <tr>
                                <td>${{ "%.2f"|format(payment.amount) }}</td>
                                <td>{{ payment.payment_method or '-' }}</td>
                                <td>
                                    {% if payment.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% elif payment.status == 'pending_approval' %}
                                        <span class="badge bg-info">في انتظار الموافقة</span>
                                    {% elif payment.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                    {% elif payment.status == 'failed' %}
                                        <span class="badge bg-danger">فاشل</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ payment.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.payment_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                {% if subscription.status == 'pending' %}
                    <form method="POST" action="{{ url_for('admin.approve_subscription', subscription_id=subscription.id) }}" class="d-inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <button type="submit" class="btn btn-success" 
                                onclick="return confirm('هل أنت متأكد من تفعيل هذا الاشتراك؟')">
                            <i class="fas fa-check me-2"></i>تفعيل الاشتراك
                        </button>
                    </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endfor %}

<!-- Payment Approval Modal -->
<div class="modal fade" id="paymentApprovalModal" tabindex="-1" aria-labelledby="paymentApprovalModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="paymentApprovalModalLabel">
                    <i class="fas fa-check-circle me-2"></i>الموافقة على الدفع
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل أنت متأكد من الموافقة على هذا الدفع وتفعيل الاشتراك؟</p>

                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-4"><strong>الطالب:</strong></div>
                            <div class="col-sm-8"><span id="approveStudentName"></span></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>الباقة:</strong></div>
                            <div class="col-sm-8"><span id="approvePackageName"></span></div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <label for="approvalMessage" class="form-label">رسالة للطالب (اختيارية):</label>
                    <textarea class="form-control" id="approvalMessage" rows="3"
                              placeholder="مثال: تم قبول دفعتك وتفعيل اشتراكك بنجاح. مرحباً بك في الأكاديمية!"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-success" id="confirmApprovalBtn">
                    <i class="fas fa-check me-2"></i>الموافقة وتفعيل الاشتراك
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Payment Rejection Modal -->
<div class="modal fade" id="paymentRejectionModal" tabindex="-1" aria-labelledby="paymentRejectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="paymentRejectionModalLabel">
                    <i class="fas fa-times-circle me-2"></i>رفض الدفع
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> رفض الدفع سيؤدي إلى إلغاء الاشتراك نهائياً!
                </div>

                <p class="mb-3">هل أنت متأكد من رفض هذا الدفع؟</p>

                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-4"><strong>الطالب:</strong></div>
                            <div class="col-sm-8"><span id="rejectStudentName"></span></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>الباقة:</strong></div>
                            <div class="col-sm-8"><span id="rejectPackageName"></span></div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <label for="rejectionReason" class="form-label">سبب الرفض <span class="text-danger">*</span>:</label>
                    <select class="form-select" id="rejectionReason" required>
                        <option value="">اختر سبب الرفض</option>
                        <option value="payment_failed">فشل في عملية الدفع</option>
                        <option value="invalid_payment">دفعة غير صحيحة</option>
                        <option value="duplicate_payment">دفعة مكررة</option>
                        <option value="policy_violation">مخالفة للسياسات</option>
                        <option value="other">سبب آخر</option>
                    </select>
                </div>

                <div class="mt-3">
                    <label for="rejectionMessage" class="form-label">رسالة للطالب <span class="text-danger">*</span>:</label>
                    <textarea class="form-control" id="rejectionMessage" rows="3" required
                              placeholder="يرجى توضيح سبب رفض الدفع للطالب..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmRejectionBtn">
                    <i class="fas fa-times me-2"></i>رفض الدفع
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteConfirmModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الاشتراك
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>

                <p class="mb-3">هل أنت متأكد من حذف اشتراك الطالب؟</p>

                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-4"><strong>الطالب:</strong></div>
                            <div class="col-sm-8"><span id="deleteStudentName"></span></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>الباقة:</strong></div>
                            <div class="col-sm-8"><span id="deletePackageName"></span></div>
                        </div>
                        <div class="row">
                            <div class="col-sm-4"><strong>الحالة:</strong></div>
                            <div class="col-sm-8"><span id="deleteSubscriptionStatus"></span></div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <h6 class="text-danger">سيتم حذف:</h6>
                    <ul class="text-muted">
                        <li>بيانات الاشتراك</li>
                        <li>الحصص المرتبطة بالاشتراك</li>
                        <li>سجل المدفوعات المرتبط</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>حذف الاشتراك
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.getElementById('status').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Extend subscription function
    function extendSubscription(subscriptionId) {
        const days = prompt('كم يوماً تريد إضافتها للاشتراك؟', '30');
        if (days && !isNaN(days) && parseInt(days) > 0) {
            if (confirm(`هل أنت متأكد من إضافة ${days} يوم للاشتراك؟`)) {
                // Here you would make an AJAX call to extend the subscription
                alert('سيتم إضافة هذه الميزة قريباً');
            }
        }
    }

    // Delete subscription function
    function deleteSubscription(subscriptionId, studentName, packageName) {
        // Get subscription status from the row
        const row = event.target.closest('tr');
        const statusBadge = row.querySelector('.badge');
        const status = statusBadge ? statusBadge.textContent.trim() : 'غير محدد';

        // Show confirmation modal
        const modal = document.getElementById('deleteConfirmModal');
        const studentNameSpan = document.getElementById('deleteStudentName');
        const packageNameSpan = document.getElementById('deletePackageName');
        const statusSpan = document.getElementById('deleteSubscriptionStatus');
        const confirmBtn = document.getElementById('confirmDeleteBtn');

        studentNameSpan.textContent = studentName;
        packageNameSpan.textContent = packageName;
        statusSpan.textContent = status;

        // Change button color based on status
        if (status === 'نشط') {
            statusSpan.className = 'badge bg-success';
            confirmBtn.className = 'btn btn-danger';
            confirmBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>حذف الاشتراك النشط';
        } else {
            statusSpan.className = 'badge bg-secondary';
            confirmBtn.className = 'btn btn-danger';
            confirmBtn.innerHTML = '<i class="fas fa-trash me-2"></i>حذف الاشتراك';
        }

        // Set the subscription ID for deletion
        confirmBtn.onclick = function() {
            performDelete(subscriptionId, status);
        };

        // Show modal
        const deleteModal = new bootstrap.Modal(modal);
        deleteModal.show();
    }

    // Perform the actual deletion
    function performDelete(subscriptionId, status) {
        // Extra confirmation for active subscriptions
        if (status === 'نشط') {
            const extraConfirm = confirm('تحذير إضافي: هذا الاشتراك نشط حالياً!\n\nحذف الاشتراك النشط سيؤثر على:\n- الحصص المجدولة\n- تقدم الطالب\n- المدفوعات المرتبطة\n\nهل أنت متأكد تماماً من المتابعة؟');
            if (!extraConfirm) {
                return;
            }
        }

        // Hide modal first
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();

        // Show loading state
        const loadingToast = document.createElement('div');
        loadingToast.className = 'toast position-fixed top-0 end-0 m-3';
        loadingToast.innerHTML = `
            <div class="toast-body bg-info text-white">
                <i class="fas fa-spinner fa-spin me-2"></i>جاري حذف الاشتراك...
            </div>
        `;
        document.body.appendChild(loadingToast);
        const toast = new bootstrap.Toast(loadingToast);
        toast.show();

        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/subscriptions/${subscriptionId}/delete`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add to body and submit
        document.body.appendChild(form);
        form.submit();
    }

    // Payment approval function
    function approvePayment(subscriptionId, studentName, packageName) {
        const modal = document.getElementById('paymentApprovalModal');
        const studentNameSpan = document.getElementById('approveStudentName');
        const packageNameSpan = document.getElementById('approvePackageName');
        const confirmBtn = document.getElementById('confirmApprovalBtn');

        studentNameSpan.textContent = studentName;
        packageNameSpan.textContent = packageName;

        // Set the subscription ID for approval
        confirmBtn.onclick = function() {
            performApproval(subscriptionId);
        };

        // Show modal
        const approvalModal = new bootstrap.Modal(modal);
        approvalModal.show();
    }

    // Payment rejection function
    function rejectPayment(subscriptionId, studentName, packageName) {
        const modal = document.getElementById('paymentRejectionModal');
        const studentNameSpan = document.getElementById('rejectStudentName');
        const packageNameSpan = document.getElementById('rejectPackageName');
        const confirmBtn = document.getElementById('confirmRejectionBtn');

        studentNameSpan.textContent = studentName;
        packageNameSpan.textContent = packageName;

        // Set the subscription ID for rejection
        confirmBtn.onclick = function() {
            performRejection(subscriptionId);
        };

        // Show modal
        const rejectionModal = new bootstrap.Modal(modal);
        rejectionModal.show();
    }

    // Perform payment approval
    function performApproval(subscriptionId) {
        const message = document.getElementById('approvalMessage').value;

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/subscriptions/${subscriptionId}/approve-payment`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add message
        if (message) {
            const messageInput = document.createElement('input');
            messageInput.type = 'hidden';
            messageInput.name = 'message';
            messageInput.value = message;
            form.appendChild(messageInput);
        }

        // Hide modal and submit
        const modal = bootstrap.Modal.getInstance(document.getElementById('paymentApprovalModal'));
        modal.hide();

        document.body.appendChild(form);
        form.submit();
    }

    // Perform payment rejection
    function performRejection(subscriptionId) {
        const reason = document.getElementById('rejectionReason').value;
        const message = document.getElementById('rejectionMessage').value;

        if (!reason || !message) {
            alert('يرجى تحديد سبب الرفض وكتابة رسالة للطالب');
            return;
        }

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/subscriptions/${subscriptionId}/reject-payment`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add reason
        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'reason';
        reasonInput.value = reason;
        form.appendChild(reasonInput);

        // Add message
        const messageInput = document.createElement('input');
        messageInput.type = 'hidden';
        messageInput.name = 'message';
        messageInput.value = message;
        form.appendChild(messageInput);

        // Hide modal and submit
        const modal = bootstrap.Modal.getInstance(document.getElementById('paymentRejectionModal'));
        modal.hide();

        document.body.appendChild(form);
        form.submit();
    }

    // Cancel subscription function
    function cancelSubscription(subscriptionId, userName, packageName) {
        const reason = prompt(`إلغاء اشتراك ${userName} في ${packageName}\n\nيرجى إدخال سبب الإلغاء:`);
        if (!reason) return;

        const adminMessage = prompt('رسالة إضافية للطالب (اختياري):') || '';
        const refundInfo = prompt('معلومات الاسترداد (اختياري):') || '';

        if (confirm(`هل أنت متأكد من إلغاء اشتراك ${userName}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/subscriptions/${subscriptionId}/cancel`;

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrf_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add fields
            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'cancellation_reason';
            reasonInput.value = reason;
            form.appendChild(reasonInput);

            const messageInput = document.createElement('input');
            messageInput.type = 'hidden';
            messageInput.name = 'admin_message';
            messageInput.value = adminMessage;
            form.appendChild(messageInput);

            const refundInput = document.createElement('input');
            refundInput.type = 'hidden';
            refundInput.name = 'refund_info';
            refundInput.value = refundInfo;
            form.appendChild(refundInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Suspend subscription function
    function suspendSubscription(subscriptionId, userName, packageName) {
        const reason = prompt(`إيقاف اشتراك ${userName} في ${packageName} مؤقتاً\n\nيرجى إدخال سبب الإيقاف:`);
        if (!reason) return;

        const adminMessage = prompt('رسالة إضافية للطالب (اختياري):') || '';
        const reactivationSteps = prompt('خطوات إعادة التفعيل (اختياري):') || '';

        if (confirm(`هل أنت متأكد من إيقاف اشتراك ${userName} مؤقتاً؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/subscriptions/${subscriptionId}/suspend`;

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrf_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add fields
            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'suspension_reason';
            reasonInput.value = reason;
            form.appendChild(reasonInput);

            const messageInput = document.createElement('input');
            messageInput.type = 'hidden';
            messageInput.name = 'admin_message';
            messageInput.value = adminMessage;
            form.appendChild(messageInput);

            const stepsInput = document.createElement('input');
            stepsInput.type = 'hidden';
            stepsInput.name = 'reactivation_steps';
            stepsInput.value = reactivationSteps;
            form.appendChild(stepsInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Reactivate subscription function
    function reactivateSubscription(subscriptionId, userName, packageName) {
        const adminMessage = prompt(`إعادة تفعيل اشتراك ${userName} في ${packageName}\n\nرسالة ترحيب للطالب (اختياري):`) || '';

        if (confirm(`هل أنت متأكد من إعادة تفعيل اشتراك ${userName}؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/subscriptions/${subscriptionId}/reactivate`;

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrf_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add admin message
            const messageInput = document.createElement('input');
            messageInput.type = 'hidden';
            messageInput.name = 'admin_message';
            messageInput.value = adminMessage;
            form.appendChild(messageInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Approve manual payment function
    function approveManualPayment(subscriptionId, userName, packageName) {
        const customMessage = prompt(`الموافقة على اشتراك ${userName} في ${packageName}\n\nرسالة ترحيب للطالب (اختياري):`) || '';

        if (confirm(`هل أنت متأكد من الموافقة على اشتراك ${userName}؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/subscriptions/${subscriptionId}/approve`;

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrf_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add custom message
            const messageInput = document.createElement('input');
            messageInput.type = 'hidden';
            messageInput.name = 'message';
            messageInput.value = customMessage;
            form.appendChild(messageInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Reject manual payment function
    function rejectManualPayment(subscriptionId, userName, packageName) {
        const reason = prompt(`رفض اشتراك ${userName} في ${packageName}\n\nسبب الرفض:`) || '';
        if (!reason.trim()) {
            alert('يجب إدخال سبب الرفض');
            return;
        }

        const customMessage = prompt(`رسالة للطالب (اختياري):`) || '';

        if (confirm(`هل أنت متأكد من رفض اشتراك ${userName}؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/subscriptions/${subscriptionId}/reject-manual`;

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrf_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add reason
            const reasonInput = document.createElement('input');
            reasonInput.type = 'hidden';
            reasonInput.name = 'reason';
            reasonInput.value = reason;
            form.appendChild(reasonInput);

            // Add custom message
            const messageInput = document.createElement('input');
            messageInput.type = 'hidden';
            messageInput.name = 'message';
            messageInput.value = customMessage;
            form.appendChild(messageInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
