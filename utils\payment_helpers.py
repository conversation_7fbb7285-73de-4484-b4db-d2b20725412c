"""
Payment helper functions for the Quran LMS system
"""

from models import PaymentGateway, AcademySettings

def get_active_payment_gateways():
    """Get all active payment gateways"""
    return PaymentGateway.query.filter_by(is_active=True).all()

def get_payment_gateway(name):
    """Get specific payment gateway by name"""
    return PaymentGateway.query.filter_by(name=name, is_active=True).first()

def is_payment_gateway_active(name):
    """Check if a specific payment gateway is active"""
    gateway = PaymentGateway.query.filter_by(name=name).first()
    return gateway and gateway.is_active

def get_default_currency():
    """Get the default currency from academy settings"""
    settings = AcademySettings.query.first()
    return settings.currency if settings else 'USD'

def format_price(amount, currency=None):
    """Format price with currency symbol"""
    if currency is None:
        currency = get_default_currency()
    
    currency_symbols = {
        'USD': '$',
        'EUR': '€',
        'SAR': 'ر.س',
        'AED': 'د.إ',
        'EGP': 'ج.م'
    }
    
    symbol = currency_symbols.get(currency, currency)
    return f"{amount:.2f} {symbol}"

def validate_payment_amount(amount):
    """Validate payment amount"""
    try:
        amount = float(amount)
        return amount > 0
    except (ValueError, TypeError):
        return False

def get_payment_methods_for_display():
    """Get payment methods formatted for display in templates"""
    gateways = get_active_payment_gateways()
    methods = []
    
    for gateway in gateways:
        method_info = {
            'name': gateway.name,
            'display_name': get_gateway_display_name(gateway.name),
            'icon': get_gateway_icon(gateway.name),
            'description': get_gateway_description(gateway.name)
        }
        methods.append(method_info)
    
    return methods

def get_gateway_display_name(gateway_name):
    """Get display name for payment gateway"""
    display_names = {
        'stripe': 'بطاقة ائتمان (Stripe)',
        'paypal': 'PayPal',
        'bank_transfer': 'تحويل بنكي'
    }
    return display_names.get(gateway_name, gateway_name.title())

def get_gateway_icon(gateway_name):
    """Get icon class for payment gateway"""
    icons = {
        'stripe': 'fab fa-cc-stripe',
        'paypal': 'fab fa-paypal',
        'bank_transfer': 'fas fa-university'
    }
    return icons.get(gateway_name, 'fas fa-credit-card')

def get_gateway_description(gateway_name):
    """Get description for payment gateway"""
    descriptions = {
        'stripe': 'ادفع بأمان باستخدام بطاقتك الائتمانية',
        'paypal': 'ادفع باستخدام حساب PayPal الخاص بك',
        'bank_transfer': 'تحويل مباشر إلى الحساب البنكي'
    }
    return descriptions.get(gateway_name, 'طريقة دفع آمنة ومضمونة')
