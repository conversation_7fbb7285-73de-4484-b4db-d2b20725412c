{% extends "base.html" %}

{% block title %}تعديل الحصة - {{ academy_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h3 mb-1">تعديل الحصة</h2>
                    <p class="text-muted">تعديل تفاصيل الحصة المجدولة</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('admin.sessions') }}">الحصص</a></li>
                        <li class="breadcrumb-item active">تعديل الحصة</li>
                    </ol>
                </nav>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>تفاصيل الحصة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="sessionEditForm">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                
                                <div class="row">
                                    <!-- Teacher Selection -->
                                    <div class="col-md-6 mb-3">
                                        <label for="teacher_id" class="form-label fw-bold">المعلم</label>
                                        <select class="form-select" id="teacher_id" name="teacher_id" required>
                                            <option value="">اختر المعلم</option>
                                            {% for teacher in teachers %}
                                            <option value="{{ teacher.id }}" {{ 'selected' if teacher.id == session.teacher_id }}>
                                                {{ teacher.full_name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <!-- Student Selection -->
                                    <div class="col-md-6 mb-3">
                                        <label for="student_id" class="form-label fw-bold">الطالب</label>
                                        <select class="form-select" id="student_id" name="student_id" required>
                                            <option value="">اختر الطالب</option>
                                            {% for student in students %}
                                            <option value="{{ student.id }}" {{ 'selected' if student.id == session.student_id }}>
                                                {{ student.full_name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Session Type -->
                                    <div class="col-md-6 mb-3">
                                        <label for="session_type" class="form-label fw-bold">نوع الحصة</label>
                                        <select class="form-select" id="session_type" name="session_type" required>
                                            <option value="trial" {{ 'selected' if session.session_type == 'trial' }}>تجريبية</option>
                                            <option value="scheduled" {{ 'selected' if session.session_type == 'scheduled' }}>مجدولة</option>
                                            <option value="makeup" {{ 'selected' if session.session_type == 'makeup' }}>تعويضية</option>
                                        </select>
                                    </div>

                                    <!-- Subscription -->
                                    <div class="col-md-6 mb-3">
                                        <label for="subscription_id" class="form-label fw-bold">الاشتراك</label>
                                        <select class="form-select" id="subscription_id" name="subscription_id">
                                            <option value="">بدون اشتراك</option>
                                            {% for subscription in subscriptions %}
                                            <option value="{{ subscription.id }}" {{ 'selected' if subscription.id == session.subscription_id }}>
                                                {{ subscription.package.name }} - {{ subscription.user.full_name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Date -->
                                    <div class="col-md-4 mb-3">
                                        <label for="scheduled_date" class="form-label fw-bold">التاريخ</label>
                                        <input type="date" class="form-control" id="scheduled_date" name="scheduled_date" 
                                               value="{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}" required>
                                    </div>

                                    <!-- Time -->
                                    <div class="col-md-4 mb-3">
                                        <label for="scheduled_time" class="form-label fw-bold">الوقت</label>
                                        <input type="time" class="form-control" id="scheduled_time" name="scheduled_time" 
                                               value="{{ session.scheduled_datetime.strftime('%H:%M') }}" required>
                                    </div>

                                    <!-- Duration -->
                                    <div class="col-md-4 mb-3">
                                        <label for="duration_minutes" class="form-label fw-bold">المدة (دقيقة)</label>
                                        <select class="form-select" id="duration_minutes" name="duration_minutes" required>
                                            <option value="30" {{ 'selected' if session.duration_minutes == 30 }}>30 دقيقة</option>
                                            <option value="45" {{ 'selected' if session.duration_minutes == 45 }}>45 دقيقة</option>
                                            <option value="60" {{ 'selected' if session.duration_minutes == 60 }}>60 دقيقة</option>
                                            <option value="90" {{ 'selected' if session.duration_minutes == 90 }}>90 دقيقة</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Meeting Provider -->
                                <div class="mb-3">
                                    <label for="meeting_provider" class="form-label fw-bold">مزود الحصة</label>
                                    <select class="form-select" id="meeting_provider" name="meeting_provider">
                                        <option value="google_meet" {{ 'selected' if session.meeting_provider == 'google_meet' }}>Google Meet (via Calendar)</option>
                                        <option value="jitsi" {{ 'selected' if session.meeting_provider == 'jitsi' }}>Jitsi Meet</option>
                                    </select>
                                </div>

                                <!-- Notes -->
                                <div class="mb-3">
                                    <label for="notes" class="form-label fw-bold">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="ملاحظات إضافية حول الحصة">{{ session.notes or '' }}</textarea>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('admin.sessions') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-right me-2"></i>العودة للحصص
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Current Session Info -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الحصة الحالية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>المعلم:</strong> {{ session.teacher.full_name }}
                            </div>
                            <div class="mb-2">
                                <strong>الطالب:</strong> {{ session.student.full_name }}
                            </div>
                            <div class="mb-2">
                                <strong>التاريخ:</strong> {{ session.scheduled_datetime.strftime('%Y-%m-%d') }}
                            </div>
                            <div class="mb-2">
                                <strong>الوقت:</strong> {{ session.scheduled_datetime.strftime('%H:%M') }}
                            </div>
                            <div class="mb-2">
                                <strong>المدة:</strong> {{ session.duration_minutes }} دقيقة
                            </div>
                            <div class="mb-2">
                                <strong>الحالة:</strong> 
                                <span class="badge bg-{{ 'success' if session.status == 'completed' else 'primary' if session.status == 'scheduled' else 'warning' }}">
                                    {{ session.get_status_display() }}
                                </span>
                            </div>
                            {% if session.calendar_event_id %}
                            <div class="mb-2">
                                <strong>Google Calendar:</strong> 
                                <span class="badge bg-success">
                                    <i class="fab fa-google me-1"></i>متزامن
                                </span>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Warning Card -->
                    <div class="card shadow-sm border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>تنبيه
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="small mb-2">
                                <i class="fas fa-calendar text-primary me-1"></i>
                                سيتم تحديث Google Calendar تلقائياً عند تغيير التاريخ أو الوقت أو المشاركين.
                            </p>
                            <p class="small mb-0">
                                <i class="fas fa-bell text-warning me-1"></i>
                                سيتم إرسال إشعارات للمعلم والطالب عند حفظ التغييرات.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation
    document.getElementById('sessionEditForm').addEventListener('submit', function(e) {
        const teacherId = document.getElementById('teacher_id').value;
        const studentId = document.getElementById('student_id').value;
        const date = document.getElementById('scheduled_date').value;
        const time = document.getElementById('scheduled_time').value;
        
        if (!teacherId || !studentId || !date || !time) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }
        
        // Check if date is not in the past
        const selectedDateTime = new Date(date + 'T' + time);
        const now = new Date();
        
        if (selectedDateTime <= now) {
            e.preventDefault();
            alert('لا يمكن جدولة حصة في الماضي');
            return;
        }
        
        // Confirm changes
        if (!confirm('هل أنت متأكد من حفظ التغييرات؟ سيتم تحديث Google Calendar تلقائياً.')) {
            e.preventDefault();
            return;
        }
    });
</script>
{% endblock %}
