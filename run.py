#!/usr/bin/env python3
"""
Main application runner for Quran LMS
"""

import os
from app import app

if __name__ == '__main__':
    # Get port from environment variable or default to 8080
    port = int(os.environ.get('PORT', 8080))

    # Get debug mode from environment variable
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    print(f"🚀 Starting Quran LMS server...")
    print(f"📱 Access from computer: http://localhost:{port}")
    print(f"📱 Access from mobile: http://0.0.0.0:{port}")
    print(f"🌐 Or use your computer's IP address: http://[YOUR_IP]:{port}")
    print(f"🔧 Debug mode: {'ON' if debug else 'OFF'}")
    print("-" * 50)

    # Run the application
    app.run(host='0.0.0.0', port=port, debug=debug)
