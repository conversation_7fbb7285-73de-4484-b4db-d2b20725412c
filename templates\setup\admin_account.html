{% extends "setup/base.html" %}

{% block title %}إنشاء حساب المدير - {{ super() }}{% endblock %}

{% block header_title %}إنشاء حساب المدير{% endblock %}
{% block header_subtitle %}قم بإنشاء حساب المدير الرئيسي للنظام{% endblock %}

{% block progress %}
<div class="setup-progress">
    <div class="progress">
        <div class="progress-bar" role="progressbar" style="width: 70%" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
    <div class="step-indicator">
        <div class="step completed">التعريف</div>
        <div class="step active">حساب المدير</div>
        <div class="step">إعدادات الأكاديمية</div>
        <div class="step">بوابات الدفع</div>
        <div class="step">الإكمال</div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="text-center mb-4">
    <div class="feature-icon">
        <i class="fas fa-user-shield"></i>
    </div>
    <h3 class="mb-3">إنشاء حساب المدير الرئيسي</h3>
    <p class="text-muted">
        هذا الحساب سيكون له صلاحيات كاملة لإدارة النظام
    </p>
</div>

<form method="POST" id="adminForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="first_name" class="form-label">
                <i class="fas fa-user me-2"></i>الاسم الأول
            </label>
            <input type="text" class="form-control" id="first_name" name="first_name" required>
        </div>
        
        <div class="col-md-6 mb-3">
            <label for="last_name" class="form-label">
                <i class="fas fa-user me-2"></i>الاسم الأخير
            </label>
            <input type="text" class="form-control" id="last_name" name="last_name" required>
        </div>
    </div>
    
    <div class="mb-3">
        <label for="email" class="form-label">
            <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
        </label>
        <input type="email" class="form-control" id="email" name="email" required>
        <div class="form-text">سيتم استخدام هذا البريد لتسجيل الدخول</div>
    </div>
    
    <div class="mb-3">
        <label for="phone" class="form-label">
            <i class="fas fa-phone me-2"></i>رقم الهاتف (اختياري)
        </label>
        <input type="tel" class="form-control" id="phone" name="phone" placeholder="+20xxxxxxxxxx">
    </div>
    
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="password" class="form-label">
                <i class="fas fa-lock me-2"></i>كلمة المرور
            </label>
            <input type="password" class="form-control" id="password" name="password" required minlength="8">
            <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
        </div>
        
        <div class="col-md-6 mb-3">
            <label for="confirm_password" class="form-label">
                <i class="fas fa-lock me-2"></i>تأكيد كلمة المرور
            </label>
            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="8">
        </div>
    </div>
    
    <!-- Password Strength Indicator -->
    <div class="mb-3">
        <div class="password-strength" id="passwordStrength"></div>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>ملاحظة مهمة:</strong> احتفظ بهذه البيانات في مكان آمن. ستحتاجها لتسجيل الدخول إلى النظام.
    </div>
    
    <div class="d-flex justify-content-between align-items-center mt-4">
        <a href="{{ url_for('setup.developer') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>السابق
        </a>
        
        <div class="text-center">
            <a href="{{ url_for('setup.skip_all') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-forward me-2"></i>تخطي الكل
            </a>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-arrow-left me-2"></i>التالي
            </button>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_css %}
<style>
.password-strength {
    height: 5px;
    border-radius: 3px;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.strength-weak { background-color: #dc3545; }
.strength-medium { background-color: #ffc107; }
.strength-strong { background-color: #28a745; }

.form-control.is-invalid {
    border-color: #dc3545;
}

.form-control.is-valid {
    border-color: #28a745;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const passwordStrength = document.getElementById('passwordStrength');
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('adminForm');
    
    // Password strength checker
    function checkPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        return strength;
    }
    
    password.addEventListener('input', function() {
        const strength = checkPasswordStrength(this.value);
        
        if (this.value.length === 0) {
            passwordStrength.style.width = '0%';
            passwordStrength.className = 'password-strength';
        } else if (strength <= 2) {
            passwordStrength.style.width = '33%';
            passwordStrength.className = 'password-strength strength-weak';
        } else if (strength <= 3) {
            passwordStrength.style.width = '66%';
            passwordStrength.className = 'password-strength strength-medium';
        } else {
            passwordStrength.style.width = '100%';
            passwordStrength.className = 'password-strength strength-strong';
        }
    });
    
    // Password confirmation checker
    function checkPasswordMatch() {
        if (confirmPassword.value && password.value !== confirmPassword.value) {
            confirmPassword.classList.add('is-invalid');
            confirmPassword.classList.remove('is-valid');
            return false;
        } else if (confirmPassword.value) {
            confirmPassword.classList.add('is-valid');
            confirmPassword.classList.remove('is-invalid');
            return true;
        }
        return true;
    }
    
    confirmPassword.addEventListener('input', checkPasswordMatch);
    password.addEventListener('input', checkPasswordMatch);
    
    // Form validation
    form.addEventListener('submit', function(e) {
        if (password.value !== confirmPassword.value) {
            e.preventDefault();
            alert('كلمات المرور غير متطابقة');
            confirmPassword.focus();
            return false;
        }
        
        if (password.value.length < 8) {
            e.preventDefault();
            alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
            password.focus();
            return false;
        }
        
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}
