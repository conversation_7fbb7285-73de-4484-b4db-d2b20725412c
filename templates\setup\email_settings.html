{% extends "setup/base.html" %}

{% block title %}إعدادات البريد الإلكتروني - {{ super() }}{% endblock %}

{% block header_title %}إعدادات البريد الإلكتروني{% endblock %}
{% block header_subtitle %}قم بإعداد SMTP لإرسال الإشعارات والرسائل{% endblock %}

{% block content %}
<div class="text-center mb-4">
    <div class="feature-icon">
        <i class="fas fa-envelope"></i>
    </div>
    <h3 class="mb-3">إعدادات البريد الإلكتروني</h3>
    <p class="text-muted">
        إعداد SMTP لإرسال الإشعارات والرسائل للمستخدمين
    </p>
</div>

<form method="POST" id="emailForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-server me-2"></i>إعدادات SMTP</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <label for="smtp_server" class="form-label">
                        <i class="fas fa-server me-2"></i>خادم SMTP
                    </label>
                    <input type="text" class="form-control" id="smtp_server" name="smtp_server" 
                           value="{{ email_settings.smtp_server if email_settings else '' }}" 
                           placeholder="smtp.gmail.com">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="smtp_port" class="form-label">
                        <i class="fas fa-plug me-2"></i>المنفذ
                    </label>
                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                           value="{{ email_settings.smtp_port if email_settings else '587' }}" 
                           placeholder="587">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="smtp_username" class="form-label">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                    <input type="email" class="form-control" id="smtp_username" name="smtp_username" 
                           value="{{ email_settings.smtp_username if email_settings else '' }}" 
                           placeholder="<EMAIL>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="smtp_password" class="form-label">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                    <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                           value="{{ email_settings.smtp_password if email_settings else '' }}">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="use_tls" name="use_tls" 
                               {{ 'checked' if email_settings and email_settings.use_tls else 'checked' }}>
                        <label class="form-check-label" for="use_tls">
                            <i class="fas fa-shield-alt me-2"></i>استخدام TLS
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="use_ssl" name="use_ssl" 
                               {{ 'checked' if email_settings and email_settings.use_ssl else '' }}>
                        <label class="form-check-label" for="use_ssl">
                            <i class="fas fa-lock me-2"></i>استخدام SSL
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-paper-plane me-2"></i>معلومات المرسل</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="sender_name" class="form-label">
                        <i class="fas fa-signature me-2"></i>اسم المرسل
                    </label>
                    <input type="text" class="form-control" id="sender_name" name="sender_name" 
                           value="{{ email_settings.sender_name if email_settings else '' }}" 
                           placeholder="أكاديمية القرآن الكريم">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="sender_email" class="form-label">
                        <i class="fas fa-at me-2"></i>بريد المرسل
                    </label>
                    <input type="email" class="form-control" id="sender_email" name="sender_email" 
                           value="{{ email_settings.sender_email if email_settings else '' }}" 
                           placeholder="<EMAIL>">
                </div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info">
        <h6><i class="fas fa-lightbulb me-2"></i>نصائح للإعداد:</h6>
        <ul class="mb-0">
            <li><strong>Gmail:</strong> smtp.gmail.com, Port: 587, TLS: نعم</li>
            <li><strong>Outlook:</strong> smtp-mail.outlook.com, Port: 587, TLS: نعم</li>
            <li><strong>Yahoo:</strong> smtp.mail.yahoo.com, Port: 587, TLS: نعم</li>
        </ul>
    </div>
    
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه:</strong> يمكنك تخطي هذه الخطوة وإعدادها لاحقاً. بدون إعدادات SMTP لن يتم إرسال الإشعارات عبر البريد الإلكتروني.
    </div>
    
    <div class="d-flex justify-content-between align-items-center mt-4">
        <a href="{{ url_for('setup.payment_settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>السابق
        </a>
        
        <div class="text-center">
            <button type="submit" name="skip_step" value="1" class="btn btn-outline-secondary me-2">
                <i class="fas fa-forward me-2"></i>تخطي
            </button>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-arrow-left me-2"></i>التالي
            </button>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('emailForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
        if (e.submitter === submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        }
    });
});
</script>
{% endblock %}
