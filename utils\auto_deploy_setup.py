"""
نظام الإعداد التلقائي عند كل deployment
يتم تشغيله تلقائياً عند بدء التطبيق في الإنتاج
"""

import os
from datetime import datetime


def auto_setup_on_deploy():
    """الإعداد التلقائي عند كل deployment"""
    
    print("\n" + "="*80)
    print("🚀 بدء الإعداد التلقائي للنظام في الإنتاج")
    print("="*80)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌍 البيئة: {'Production' if os.getenv('FLASK_ENV') == 'production' else 'Development'}")
    
    try:
        from app import app, db
        
        with app.app_context():
            # 1. التأكد من إنشاء الجداول
            print("\n📊 التحقق من قاعدة البيانات...")
            try:
                db.create_all()
                print("✅ تم التأكد من إنشاء جميع الجداول")
            except Exception as e:
                print(f"⚠️ تحذير في قاعدة البيانات: {str(e)}")
            
            # 2. تنصيب جميع قوالب البريد الإلكتروني
            print("\n📧 تنصيب قوالب البريد الإلكتروني...")
            try:
                from utils.production_email_templates import auto_install_all_templates, check_templates_installation
                
                # فحص القوالب الموجودة
                templates_ok = check_templates_installation()
                
                if not templates_ok:
                    print("📦 تنصيب القوالب المفقودة...")
                    success = auto_install_all_templates()
                    if success:
                        print("✅ تم تنصيب جميع قوالب البريد الإلكتروني (24 قالب)")
                    else:
                        print("⚠️ تم التنصيب مع بعض الأخطاء")
                else:
                    print("✅ جميع قوالب البريد الإلكتروني موجودة (24 قالب)")
                    
                    # تحديث القوالب للتأكد من أحدث إصدار
                    print("🔄 تحديث القوالب للتأكد من أحدث إصدار...")
                    auto_install_all_templates()
                    print("✅ تم تحديث جميع القوالب")
                    
            except Exception as e:
                print(f"⚠️ تحذير في قوالب البريد الإلكتروني: {str(e)}")
            
            # 3. التحقق من إعدادات النظام الأساسية
            print("\n⚙️ التحقق من إعدادات النظام...")
            try:
                from models import AcademySettings, EmailSettings, SessionSettings
                
                # فحص إعدادات الأكاديمية
                academy_settings = AcademySettings.query.first()
                if academy_settings:
                    print("✅ إعدادات الأكاديمية موجودة")
                else:
                    print("⚠️ إعدادات الأكاديمية مفقودة")
                
                # فحص إعدادات البريد
                email_settings = EmailSettings.query.first()
                if email_settings:
                    print("✅ إعدادات البريد الإلكتروني موجودة")
                else:
                    print("⚠️ إعدادات البريد الإلكتروني مفقودة")
                
                # فحص إعدادات الحصص
                session_settings = SessionSettings.query.first()
                if session_settings:
                    print("✅ إعدادات الحصص موجودة")
                else:
                    print("⚠️ إعدادات الحصص مفقودة")
                    
            except Exception as e:
                print(f"⚠️ تحذير في فحص الإعدادات: {str(e)}")
            
            # 4. التحقق من وجود مستخدم إدمن
            print("\n👤 التحقق من مستخدم الإدمن...")
            try:
                from models import User
                
                admin_user = User.query.filter_by(role='admin').first()
                if admin_user:
                    print(f"✅ مستخدم الإدمن موجود: {admin_user.email}")
                else:
                    print("⚠️ مستخدم الإدمن مفقود")
                    
            except Exception as e:
                print(f"⚠️ تحذير في فحص مستخدم الإدمن: {str(e)}")
            
            # 5. إحصائيات النظام
            print("\n📊 إحصائيات النظام:")
            try:
                from models import User, EmailTemplate, Package, Session
                
                users_count = User.query.count()
                templates_count = EmailTemplate.query.count()
                packages_count = Package.query.count()
                sessions_count = Session.query.count()
                
                print(f"   👥 المستخدمين: {users_count}")
                print(f"   📧 قوالب البريد: {templates_count}")
                print(f"   📦 الباقات: {packages_count}")
                print(f"   📚 الحصص: {sessions_count}")
                
            except Exception as e:
                print(f"⚠️ تحذير في الإحصائيات: {str(e)}")
        
        print("\n" + "="*80)
        print("🎉 تم الانتهاء من الإعداد التلقائي بنجاح")
        print("="*80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد التلقائي: {str(e)}")
        print("="*80)
        return False


def check_production_readiness():
    """فحص جاهزية النظام للإنتاج"""
    
    print("\n🔍 فحص جاهزية النظام للإنتاج...")
    
    try:
        from app import app
        from models import User, AcademySettings, EmailSettings, EmailTemplate, SessionSettings, Package
        
        with app.app_context():
            issues = []
            
            # فحص مستخدم الإدمن
            admin_user = User.query.filter_by(role='admin').first()
            if not admin_user:
                issues.append("❌ مستخدم الإدمن مفقود")
            
            # فحص إعدادات الأكاديمية
            academy_settings = AcademySettings.query.first()
            if not academy_settings:
                issues.append("❌ إعدادات الأكاديمية مفقودة")
            
            # فحص إعدادات البريد
            email_settings = EmailSettings.query.first()
            if not email_settings:
                issues.append("❌ إعدادات البريد الإلكتروني مفقودة")
            
            # فحص قوالب البريد
            templates_count = EmailTemplate.query.count()
            if templates_count < 20:  # يجب أن يكون لدينا على الأقل 20 قالب
                issues.append(f"❌ قوالب البريد غير كاملة ({templates_count}/24)")
            
            # فحص إعدادات الحصص
            session_settings = SessionSettings.query.first()
            if not session_settings:
                issues.append("❌ إعدادات الحصص مفقودة")
            
            # فحص الباقات
            packages_count = Package.query.count()
            if packages_count == 0:
                issues.append("❌ لا توجد باقات مُعرَّفة")
            
            if issues:
                print("⚠️ مشاكل في جاهزية النظام:")
                for issue in issues:
                    print(f"   {issue}")
                return False
            else:
                print("✅ النظام جاهز للإنتاج")
                return True
                
    except Exception as e:
        print(f"❌ خطأ في فحص الجاهزية: {str(e)}")
        return False


def run_auto_setup():
    """تشغيل الإعداد التلقائي"""
    
    # التحقق من البيئة
    is_production = os.getenv('FLASK_ENV') == 'production' or os.getenv('RENDER')
    
    if is_production:
        print("🌍 تم اكتشاف بيئة الإنتاج - تشغيل الإعداد التلقائي...")
        
        # تشغيل الإعداد التلقائي
        setup_success = auto_setup_on_deploy()
        
        if setup_success:
            # فحص الجاهزية
            readiness_ok = check_production_readiness()
            
            if readiness_ok:
                print("🎉 النظام جاهز ويعمل بشكل صحيح في الإنتاج")
            else:
                print("⚠️ النظام يعمل ولكن هناك بعض المشاكل")
        else:
            print("❌ فشل في الإعداد التلقائي")
    else:
        print("🏠 بيئة التطوير - تخطي الإعداد التلقائي")


if __name__ == "__main__":
    run_auto_setup()
