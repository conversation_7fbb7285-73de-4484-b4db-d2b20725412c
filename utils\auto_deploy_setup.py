"""
نظام الإعداد التلقائي عند كل deployment
يتم تشغيله تلقائياً عند بدء التطبيق في الإنتاج
"""

import os
from datetime import datetime


def auto_setup_on_deploy():
    """الإعداد التلقائي عند كل deployment"""
    
    print("\n" + "="*80)
    print("🚀 بدء الإعداد التلقائي للنظام في الإنتاج")
    print("="*80)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌍 البيئة: {'Production' if os.getenv('FLASK_ENV') == 'production' else 'Development'}")
    
    try:
        from app import app, db
        
        with app.app_context():
            # 1. التأكد من إنشاء الجداول
            print("\n📊 التحقق من قاعدة البيانات...")
            try:
                db.create_all()
                print("✅ تم التأكد من إنشاء جميع الجداول")
            except Exception as e:
                print(f"⚠️ تحذير في قاعدة البيانات: {str(e)}")
            
            # 2. تنصيب جميع قوالب البريد الإلكتروني
            print("\n📧 تنصيب قوالب البريد الإلكتروني...")
            try:
                from utils.production_email_templates import auto_install_all_templates, check_templates_installation
                
                # فحص القوالب الموجودة
                templates_ok = check_templates_installation()
                
                if not templates_ok:
                    print("📦 تنصيب القوالب المفقودة...")
                    success = auto_install_all_templates()
                    if success:
                        print("✅ تم تنصيب جميع قوالب البريد الإلكتروني (24 قالب)")
                    else:
                        print("⚠️ تم التنصيب مع بعض الأخطاء")
                else:
                    print("✅ جميع قوالب البريد الإلكتروني موجودة (24 قالب)")
                    
                    # تحديث القوالب للتأكد من أحدث إصدار
                    print("🔄 تحديث القوالب للتأكد من أحدث إصدار...")
                    auto_install_all_templates()
                    print("✅ تم تحديث جميع القوالب")
                    
            except Exception as e:
                print(f"⚠️ تحذير في قوالب البريد الإلكتروني: {str(e)}")
            
            # 3. التحقق من إعدادات النظام الأساسية وتفعيل الإشعارات
            print("\n⚙️ التحقق من إعدادات النظام وتفعيل الإشعارات...")
            try:
                from models import AcademySettings, EmailSettings, SessionSettings

                # فحص إعدادات الأكاديمية
                academy_settings = AcademySettings.query.first()
                if academy_settings:
                    print("✅ إعدادات الأكاديمية موجودة")
                else:
                    print("⚠️ إعدادات الأكاديمية مفقودة")

                # فحص وتحديث إعدادات البريد الإلكتروني للإنتاج
                email_settings = EmailSettings.query.first()
                if email_settings:
                    print("📧 تحديث إعدادات البريد الإلكتروني للإنتاج...")

                    # تفعيل جميع الإشعارات للإنتاج
                    email_settings.enabled = True
                    email_settings.welcome_email_enabled = True
                    email_settings.session_reminder_enabled = True
                    email_settings.payment_confirmation_enabled = True
                    email_settings.subscription_approval_enabled = True
                    email_settings.user_management_notifications_enabled = True
                    email_settings.subscription_purchase_enabled = True
                    email_settings.subscription_expiry_enabled = True
                    email_settings.password_reset_enabled = True

                    # تفعيل الإعدادات الإضافية إذا كانت موجودة
                    if hasattr(email_settings, 'admin_payment_notification_enabled'):
                        email_settings.admin_payment_notification_enabled = True
                    if hasattr(email_settings, 'admin_new_user_notification_enabled'):
                        email_settings.admin_new_user_notification_enabled = True
                    if hasattr(email_settings, 'trial_session_notification_enabled'):
                        email_settings.trial_session_notification_enabled = True
                    if hasattr(email_settings, 'makeup_session_notification_enabled'):
                        email_settings.makeup_session_notification_enabled = True
                    if hasattr(email_settings, 'session_deletion_notification_enabled'):
                        email_settings.session_deletion_notification_enabled = True

                    db.session.commit()
                    print("✅ تم تفعيل جميع إشعارات البريد الإلكتروني للإنتاج")
                else:
                    print("📧 إنشاء إعدادات البريد الإلكتروني للإنتاج...")
                    email_settings = EmailSettings(
                        enabled=True,
                        smtp_server='smtp.gmail.com',
                        smtp_port=587,
                        use_tls=True,
                        welcome_email_enabled=True,
                        session_reminder_enabled=True,
                        payment_confirmation_enabled=True,
                        subscription_approval_enabled=True,
                        user_management_notifications_enabled=True,
                        subscription_purchase_enabled=True,
                        subscription_expiry_enabled=True,
                        password_reset_enabled=True
                    )
                    db.session.add(email_settings)
                    db.session.commit()
                    print("✅ تم إنشاء إعدادات البريد الإلكتروني مع جميع الإشعارات مفعلة")

                # فحص إعدادات الحصص
                session_settings = SessionSettings.query.first()
                if session_settings:
                    print("✅ إعدادات الحصص موجودة")
                else:
                    print("⚠️ إعدادات الحصص مفقودة")

            except Exception as e:
                print(f"⚠️ تحذير في فحص الإعدادات: {str(e)}")
            
            # 4. التحقق من وجود مستخدم إدمن
            print("\n👤 التحقق من مستخدم الإدمن...")
            try:
                from models import User
                
                admin_user = User.query.filter_by(role='admin').first()
                if admin_user:
                    print(f"✅ مستخدم الإدمن موجود: {admin_user.email}")
                else:
                    print("⚠️ مستخدم الإدمن مفقود")
                    
            except Exception as e:
                print(f"⚠️ تحذير في فحص مستخدم الإدمن: {str(e)}")
            
            # 5. إحصائيات النظام
            print("\n📊 إحصائيات النظام:")
            try:
                from models import User, EmailTemplate, Package, Session
                
                users_count = User.query.count()
                templates_count = EmailTemplate.query.count()
                packages_count = Package.query.count()
                sessions_count = Session.query.count()
                
                print(f"   👥 المستخدمين: {users_count}")
                print(f"   📧 قوالب البريد: {templates_count}")
                print(f"   📦 الباقات: {packages_count}")
                print(f"   📚 الحصص: {sessions_count}")

            except Exception as e:
                print(f"⚠️ تحذير في الإحصائيات: {str(e)}")

            # 6. فحص شامل لحالة الإشعارات
            print("\n📧 فحص شامل لحالة الإشعارات:")
            try:
                from models import EmailSettings

                email_settings = EmailSettings.query.first()
                if email_settings:
                    notifications_status = {
                        'نظام البريد': email_settings.enabled,
                        'ترحيب المستخدمين': email_settings.welcome_email_enabled,
                        'تذكيرات الحصص': email_settings.session_reminder_enabled,
                        'تأكيد الدفع': email_settings.payment_confirmation_enabled,
                        'موافقة الاشتراك': email_settings.subscription_approval_enabled,
                        'إدارة المستخدمين': email_settings.user_management_notifications_enabled,
                        'شراء الباقات': email_settings.subscription_purchase_enabled,
                        'انتهاء الاشتراك': email_settings.subscription_expiry_enabled,
                        'استعادة كلمة المرور': email_settings.password_reset_enabled
                    }

                    # إضافة الإعدادات الإضافية إذا كانت موجودة
                    if hasattr(email_settings, 'admin_payment_notification_enabled'):
                        notifications_status['إشعار الإدمن بالدفعات'] = email_settings.admin_payment_notification_enabled
                    if hasattr(email_settings, 'admin_new_user_notification_enabled'):
                        notifications_status['إشعار الإدمن بالمستخدمين'] = email_settings.admin_new_user_notification_enabled

                    enabled_count = sum(1 for enabled in notifications_status.values() if enabled)
                    total_count = len(notifications_status)

                    print(f"   📊 الإشعارات المفعلة: {enabled_count}/{total_count}")

                    for notification_name, enabled in notifications_status.items():
                        status = "✅ مفعل" if enabled else "❌ معطل"
                        print(f"   • {notification_name}: {status}")

                    if enabled_count == total_count:
                        print("   🎉 جميع الإشعارات مفعلة ومجهزة للإنتاج!")
                    else:
                        print(f"   ⚠️ {total_count - enabled_count} إشعار معطل")
                else:
                    print("   ❌ إعدادات البريد الإلكتروني غير موجودة")

            except Exception as e:
                print(f"⚠️ تحذير في فحص الإشعارات: {str(e)}")

            # 7. فحص متغيرات البيئة للإنتاج
            print("\n🔍 فحص متغيرات البيئة:")
            try:
                mail_username = os.getenv('MAIL_USERNAME')
                mail_password = os.getenv('MAIL_PASSWORD')
                secret_key = os.getenv('SECRET_KEY')

                print(f"   🔑 SECRET_KEY: {'✅ موجود' if secret_key else '❌ مفقود'}")
                print(f"   📧 MAIL_USERNAME: {'✅ موجود' if mail_username else '❌ مفقود'}")
                print(f"   🔐 MAIL_PASSWORD: {'✅ موجود' if mail_password else '❌ مفقود'}")

                ready_for_production = all([secret_key, mail_username, mail_password])
                if ready_for_production:
                    print("   🎉 متغيرات البيئة مُعدة بشكل صحيح!")
                else:
                    print("   ⚠️ بعض متغيرات البيئة مفقودة")

            except Exception as e:
                print(f"⚠️ تحذير في فحص متغيرات البيئة: {str(e)}")
        
        print("\n" + "="*80)
        print("🎉 تم الانتهاء من الإعداد التلقائي بنجاح")
        print("="*80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد التلقائي: {str(e)}")
        print("="*80)
        return False


def check_production_readiness():
    """فحص جاهزية النظام للإنتاج"""
    
    print("\n🔍 فحص جاهزية النظام للإنتاج...")
    
    try:
        from app import app
        from models import User, AcademySettings, EmailSettings, EmailTemplate, SessionSettings, Package
        
        with app.app_context():
            issues = []
            
            # فحص مستخدم الإدمن
            admin_user = User.query.filter_by(role='admin').first()
            if not admin_user:
                issues.append("❌ مستخدم الإدمن مفقود")
            
            # فحص إعدادات الأكاديمية
            academy_settings = AcademySettings.query.first()
            if not academy_settings:
                issues.append("❌ إعدادات الأكاديمية مفقودة")
            
            # فحص إعدادات البريد
            email_settings = EmailSettings.query.first()
            if not email_settings:
                issues.append("❌ إعدادات البريد الإلكتروني مفقودة")
            
            # فحص قوالب البريد
            templates_count = EmailTemplate.query.count()
            if templates_count < 20:  # يجب أن يكون لدينا على الأقل 20 قالب
                issues.append(f"❌ قوالب البريد غير كاملة ({templates_count}/24)")
            
            # فحص إعدادات الحصص
            session_settings = SessionSettings.query.first()
            if not session_settings:
                issues.append("❌ إعدادات الحصص مفقودة")
            
            # فحص الباقات
            packages_count = Package.query.count()
            if packages_count == 0:
                issues.append("❌ لا توجد باقات مُعرَّفة")
            
            if issues:
                print("⚠️ مشاكل في جاهزية النظام:")
                for issue in issues:
                    print(f"   {issue}")
                return False
            else:
                print("✅ النظام جاهز للإنتاج")
                return True
                
    except Exception as e:
        print(f"❌ خطأ في فحص الجاهزية: {str(e)}")
        return False


def run_auto_setup():
    """تشغيل الإعداد التلقائي"""
    
    # التحقق من البيئة
    is_production = os.getenv('FLASK_ENV') == 'production' or os.getenv('RENDER')
    
    if is_production:
        print("🌍 تم اكتشاف بيئة الإنتاج - تشغيل الإعداد التلقائي...")
        
        # تشغيل الإعداد التلقائي
        setup_success = auto_setup_on_deploy()
        
        if setup_success:
            # فحص الجاهزية
            readiness_ok = check_production_readiness()
            
            if readiness_ok:
                print("🎉 النظام جاهز ويعمل بشكل صحيح في الإنتاج")
            else:
                print("⚠️ النظام يعمل ولكن هناك بعض المشاكل")
        else:
            print("❌ فشل في الإعداد التلقائي")
    else:
        print("🏠 بيئة التطوير - تخطي الإعداد التلقائي")


if __name__ == "__main__":
    run_auto_setup()
