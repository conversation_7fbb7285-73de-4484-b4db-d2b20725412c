# ⚡ دليل النشر السريع - Quran LMS

دليل مختصر لنشر التطبيق على Render في 10 دقائق.

## 🚀 الخطوات السريعة

### 1. رفع إلى GitHub (دق<PERSON><PERSON><PERSON>ان)
```bash
# في مجلد المشروع
git init
git add .
git commit -m "Initial commit - Quran LMS ready for production"
git branch -M main
git remote add origin https://github.com/yourusername/quranlms.git
git push -u origin main
```

### 2. إنشاء قاعدة البيانات على Render (دقيقة واحدة)
1. اذهب إلى [render.com](https://render.com)
2. سجل دخول/إنشاء حساب
3. اضغط "New" → "PostgreSQL"
4. اسم: `quranlms-db`
5. اضغط "Create Database"
6. **احفظ DATABASE_URL**

### 3. إنشاء Web Service (3 دقائق)
1. اضغ<PERSON> "New" → "Web Service"
2. اربط GitHub واختر المستودع
3. الإعدادات:
   - **Name**: `quranlms`
   - **Environment**: `Python 3`
   - **Build Command**: 
     ```
     pip install -r requirements.txt && python production_setup.py
     ```
   - **Start Command**: 
     ```
     gunicorn app:app
     ```

### 4. إضافة متغيرات البيئة (4 دقائق)
في قسم "Environment Variables":

```
SECRET_KEY=your-super-secret-key-here-change-this-to-something-random
DATABASE_URL=postgresql://... (من الخطوة 2)
FLASK_ENV=production
DEBUG=False
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-gmail-app-password
MAIL_DEFAULT_SENDER=<EMAIL>
```

### 5. النشر (5 دقائق)
1. اضغط "Create Web Service"
2. انتظر اكتمال البناء
3. ستحصل على رابط مثل: `https://quranlms.onrender.com`

## 📧 إعداد Gmail السريع

### إنشاء App Password:
1. [Google Account Settings](https://myaccount.google.com/)
2. Security → 2-Step Verification (فعل إذا لم يكن مفعل)
3. App passwords → Mail → Other
4. اسم: "Quran LMS"
5. استخدم كلمة المرور المُنشأة في `MAIL_PASSWORD`

## ✅ التحقق السريع

بعد النشر:
1. افتح الرابط
2. تأكد من ظهور صفحة تسجيل الدخول
3. أنشئ حساب جديد
4. تحقق من وصول بريد التأكيد

## 🔧 إعدادات اختيارية

### Stripe (للمدفوعات):
```
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
```

### PayPal (للمدفوعات):
```
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_secret
PAYPAL_MODE=live
```

## 🆘 حل المشاكل السريع

### خطأ في البناء:
- تأكد من وجود `requirements.txt`
- تأكد من صحة `production_setup.py`

### خطأ في قاعدة البيانات:
- تأكد من صحة `DATABASE_URL`
- تأكد من تشغيل `production_setup.py`

### خطأ في البريد:
- استخدم App Password وليس كلمة مرور Gmail العادية
- تأكد من تفعيل 2-Step Verification

## 📱 الوصول للتطبيق

بعد النشر الناجح:
- **الرابط**: `https://your-app-name.onrender.com`
- **المدير الافتراضي**: سيتم إنشاؤه تلقائياً
- **كلمة المرور**: ستجدها في سجلات Render

---

**🎉 مبروك! تطبيقك الآن متاح على الإنترنت!**

للمساعدة: راجع `DEPLOYMENT.md` للتفاصيل الكاملة.
