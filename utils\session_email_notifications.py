"""
إشعارات البريد الإلكتروني للحصص
"""

from utils.email_service import EmailService
from datetime import datetime

class SessionEmailNotifications:
    """خدمة إشعارات البريد الإلكتروني للحصص"""
    
    def __init__(self):
        self.email_service = EmailService()
    
    def send_session_completed_notification(self, session):
        """إرسال إشعار انتهاء الحصة"""
        try:
            # إشعار للطالب
            student_vars = {
                'user_name': session.student.full_name,
                'teacher_name': session.teacher.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes or 60,
                'academy_name': 'أكاديمية القرآن الكريم',
                'academy_email': '<EMAIL>'
            }
            
            success1, message1 = self.email_service.send_template_email(
                session.student.email,
                'session_completed',
                student_vars
            )
            
            # إشعار للمعلم
            teacher_vars = {
                'user_name': session.teacher.full_name,
                'student_name': session.student.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes or 60,
                'academy_name': 'أكاديمية القرآن الكريم',
                'academy_email': '<EMAIL>'
            }
            
            success2, message2 = self.email_service.send_template_email(
                session.teacher.email,
                'session_completed',
                teacher_vars
            )
            
            return success1 or success2, f"Student: {message1}, Teacher: {message2}"
            
        except Exception as e:
            return False, str(e)
    
    def send_session_reminder(self, session, reminder_type='1_day'):
        """إرسال تذكير بالحصة"""
        try:
            if reminder_type == '1_day':
                template_name = 'session_reminder_1_day'
            elif reminder_type == '5_minutes':
                template_name = 'session_reminder_5_minutes'
            else:
                template_name = 'session_reminder'
            
            # إشعار للطالب
            student_vars = {
                'user_name': session.student.full_name,
                'teacher_name': session.teacher.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes or 60,
                'meeting_link': session.meeting_link or '#',
                'reminder_type': reminder_type,
                'academy_name': 'أكاديمية القرآن الكريم',
                'academy_email': '<EMAIL>'
            }
            
            success1, message1 = self.email_service.send_template_email(
                session.student.email,
                template_name,
                student_vars
            )
            
            # إشعار للمعلم
            teacher_vars = {
                'user_name': session.teacher.full_name,
                'student_name': session.student.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes or 60,
                'meeting_link': session.meeting_link or '#',
                'reminder_type': reminder_type,
                'academy_name': 'أكاديمية القرآن الكريم',
                'academy_email': '<EMAIL>'
            }
            
            success2, message2 = self.email_service.send_template_email(
                session.teacher.email,
                template_name,
                teacher_vars
            )
            
            return success1 or success2, f"Student: {message1}, Teacher: {message2}"
            
        except Exception as e:
            return False, str(e)
    
    def send_trial_session_notification(self, session):
        """إرسال إشعار حصة تجريبية"""
        try:
            student_vars = {
                'user_name': session.student.full_name,
                'teacher_name': session.teacher.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes or 60,
                'meeting_link': session.meeting_link or '#',
                'trial_instructions': getattr(session, 'notes', 'استعد للحصة التجريبية'),
                'academy_name': 'أكاديمية القرآن الكريم',
                'academy_email': '<EMAIL>'
            }
            
            success, message = self.email_service.send_template_email(
                session.student.email,
                'trial_session_created',
                student_vars
            )
            
            return success, message
            
        except Exception as e:
            return False, str(e)
    
    def send_makeup_session_notification(self, session):
        """إرسال إشعار حصة تعويضية"""
        try:
            student_vars = {
                'user_name': session.student.full_name,
                'teacher_name': session.teacher.full_name,
                'session_date': session.scheduled_datetime.strftime('%Y-%m-%d'),
                'session_time': session.scheduled_datetime.strftime('%H:%M'),
                'session_duration': session.duration_minutes or 60,
                'meeting_link': session.meeting_link or '#',
                'makeup_reason': getattr(session, 'notes', 'حصة تعويضية'),
                'academy_name': 'أكاديمية القرآن الكريم',
                'academy_email': '<EMAIL>'
            }
            
            success, message = self.email_service.send_template_email(
                session.student.email,
                'makeup_session_created',
                student_vars
            )
            
            return success, message
            
        except Exception as e:
            return False, str(e)


# دوال مساعدة للاستخدام السريع
def send_session_completed_email(session):
    """دالة مساعدة لإرسال إشعار انتهاء الحصة"""
    service = SessionEmailNotifications()
    return service.send_session_completed_notification(session)

def send_session_reminder_email(session, reminder_type='1_day'):
    """دالة مساعدة لإرسال تذكير بالحصة"""
    service = SessionEmailNotifications()
    return service.send_session_reminder(session, reminder_type)

def send_trial_session_email(session):
    """دالة مساعدة لإرسال إشعار حصة تجريبية"""
    service = SessionEmailNotifications()
    return service.send_trial_session_notification(session)

def send_makeup_session_email(session):
    """دالة مساعدة لإرسال إشعار حصة تعويضية"""
    service = SessionEmailNotifications()
    return service.send_makeup_session_notification(session)
