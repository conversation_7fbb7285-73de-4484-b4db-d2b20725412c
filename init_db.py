#!/usr/bin/env python3
"""
Database initialization script for Quran LMS
Creates tables and adds initial data
"""

from app import app
from models import db, User, Package, AcademySettings, PaymentGateway, EmailSettings, EmailTemplate, SessionSettings

def init_email_templates():
    """Initialize email templates"""
    try:
        from utils.init_templates import initialize_email_templates
        success, message = initialize_email_templates()
        if success:
            print(f"✅ Email templates initialized: {message}")
        else:
            print(f"⚠️ Email templates warning: {message}")
    except Exception as e:
        print(f"❌ Error initializing email templates: {str(e)}")

def init_session_settings():
    """Initialize session settings"""
    try:
        session_settings = SessionSettings.query.first()
        if not session_settings:
            print("Creating default session settings...")
            session_settings = SessionSettings(
                default_provider='jitsi',
                auto_generate_links=True,
                default_duration=60,
                allow_student_reschedule=True,
                reschedule_hours_limit=24,
                allow_teacher_reschedule=True,
                max_reschedules_per_session=2,
                session_buffer_minutes=15,
                auto_end_sessions=True,
                session_timeout_minutes=90,
                require_session_notes=False,
                allow_session_recording=False,
                waiting_room=True
            )
            db.session.add(session_settings)
            print("✅ Session settings created")
        else:
            print("ℹ️ Session settings already exist")
    except Exception as e:
        print(f"❌ Error initializing session settings: {str(e)}")

def init_database():
    """Initialize the database with tables and default data"""
    
    with app.app_context():
        # Create all tables
        print("Creating database tables...")
        db.create_all()
        
        # Check if admin user already exists
        admin_user = User.query.filter_by(email='<EMAIL>').first()

        if not admin_user:
            print("Creating default admin user...")
            # Create default admin user
            admin_user = User(
                email='<EMAIL>',
                first_name='مدير',
                last_name='النظام',
                role='admin',
                status='approved'
            )
            admin_user.set_password('admin123')  # Change this in production!
            db.session.add(admin_user)
        
        # Check if academy settings exist
        academy_settings = AcademySettings.query.first()
        if not academy_settings:
            print("Creating default academy settings...")
            academy_settings = AcademySettings(
                academy_name='أكاديمية القرآن الكريم',
                academy_description='منصة تعليمية متكاملة لتعلم وتحفيظ القرآن الكريم',
                contact_email='<EMAIL>',
                contact_phone='+1234567890',
                primary_color='#007bff',
                secondary_color='#6c757d',
                timezone='UTC',
                currency='USD'
            )
            db.session.add(academy_settings)
        
        # Check if email settings exist
        email_settings = EmailSettings.query.first()
        if not email_settings:
            print("Creating default email settings...")
            email_settings = EmailSettings(
                enabled=True,  # تفعيل نظام البريد الإلكتروني
                smtp_server='smtp.gmail.com',
                smtp_port=587,
                use_tls=True,
                use_ssl=False,
                default_sender='<EMAIL>',
                welcome_email_enabled=True,
                session_reminder_enabled=True,
                payment_confirmation_enabled=True,
                subscription_approval_enabled=True,
                trial_session_notification_enabled=True,
                makeup_session_notification_enabled=True,
                session_deletion_notification_enabled=True
            )
            db.session.add(email_settings)
        
        # Check if default packages exist
        if Package.query.count() == 0:
            print("Creating default packages...")
            
            # Basic Package
            basic_package = Package(
                name='الباقة الأساسية',
                description='باقة مناسبة للمبتدئين تشمل 8 حصص شهرياً',
                price=99.99,
                duration_days=30,
                sessions_count=8,
                features='8 حصص شهرياً\nمعلم مؤهل\nمتابعة التقدم\nدعم فني',
                is_active=True
            )
            db.session.add(basic_package)
            
            # Standard Package
            standard_package = Package(
                name='الباقة المتوسطة',
                description='باقة متوسطة تشمل 12 حصة شهرياً مع مميزات إضافية',
                price=149.99,
                duration_days=30,
                sessions_count=12,
                features='12 حصة شهرياً\nمعلم مؤهل\nمتابعة التقدم\nتقارير مفصلة\nدعم فني متقدم',
                is_active=True
            )
            db.session.add(standard_package)
            
            # Premium Package
            premium_package = Package(
                name='الباقة المتميزة',
                description='باقة شاملة تشمل 20 حصة شهرياً مع جميع المميزات',
                price=199.99,
                duration_days=30,
                sessions_count=20,
                features='20 حصة شهرياً\nمعلم متخصص\nمتابعة شخصية\nتقارير مفصلة\nحصص تعويضية\nدعم فني 24/7',
                is_active=True
            )
            db.session.add(premium_package)
        
        # Check if payment gateways exist
        if PaymentGateway.query.count() == 0:
            print("Creating default payment gateways...")
            
            stripe_gateway = PaymentGateway(
                name='stripe',
                is_active=False,
                configuration='{"currency": "USD", "country": "US"}'
            )
            db.session.add(stripe_gateway)
            
            paypal_gateway = PaymentGateway(
                name='paypal',
                is_active=False,
                configuration='{"currency": "USD", "mode": "sandbox"}'
            )
            db.session.add(paypal_gateway)

        # Initialize email templates
        print("Initializing email templates...")
        init_email_templates()

        # Initialize session settings
        print("Initializing session settings...")
        init_session_settings()

        # Commit all changes
        db.session.commit()
        print("Database initialization completed successfully!")
        
        # Print admin credentials
        print("\n" + "="*50)
        print("DEFAULT ADMIN CREDENTIALS:")
        print("Email: <EMAIL>")
        print("Password: admin123")
        print("PLEASE CHANGE THESE CREDENTIALS AFTER FIRST LOGIN!")
        print("="*50)

if __name__ == '__main__':
    init_database()
