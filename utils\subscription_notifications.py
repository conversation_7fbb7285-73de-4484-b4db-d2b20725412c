"""
خدمة إشعارات الاشتراكات
إرسال إشعارات البريد الإلكتروني عند تغيير حالة الاشتراكات
"""

from datetime import datetime
from flask import current_app
from models import EmailSettings, AcademySettings, db
from utils.email_service import EmailService
from utils.currency_helper import format_currency, get_system_currency


class SubscriptionNotificationService:
    def __init__(self):
        self.email_service = EmailService()
    
    def _get_academy_info(self):
        """جلب معلومات الأكاديمية"""
        academy_settings = AcademySettings.query.first()
        if academy_settings:
            return {
                'academy_name': academy_settings.academy_name or 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email or '',
                'academy_phone': academy_settings.contact_phone or '',
                'academy_website': academy_settings.website_url or '',
                'academy_address': academy_settings.address or '',
                'academy_slogan': academy_settings.academy_slogan or ''
            }
        return {
            'academy_name': 'أكاديمية القرآن الكريم',
            'academy_email': '',
            'academy_phone': '',
            'academy_website': '',
            'academy_address': '',
            'academy_slogan': ''
        }
    
    def _is_notification_enabled(self, notification_type):
        """فحص إذا كان نوع الإشعار مفعل"""
        email_settings = EmailSettings.query.first()
        if not email_settings:
            return True  # افتراضياً مفعل
        
        if notification_type == 'subscription_status_change':
            return getattr(email_settings, 'subscription_status_change_notification', True)
        elif notification_type == 'subscription_approval':
            return getattr(email_settings, 'subscription_approval_enabled', True)
        
        return True
    
    def send_subscription_activated_email(self, subscription, admin_message=None):
        """إرسال إشعار تفعيل الاشتراك"""
        
        # فحص إذا كانت الإشعارات مفعلة (تم إزالة الفحص لأن النظام مفعل)
        
        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()
            
            # إعداد المتغيرات
            system_currency = get_system_currency()
            variables = {
                'user_name': subscription.user.full_name,
                'package_name': subscription.package.name,
                'package_price': subscription.package.price,
                'package_price_formatted': format_currency(subscription.package.price, system_currency),
                'sessions_count': subscription.package.sessions_count,
                'start_date': subscription.start_date.strftime('%Y-%m-%d') if subscription.start_date else 'غير محدد',
                'end_date': subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else 'غير محدد',
                'admin_message': admin_message or '',
                'login_url': f"{academy_info.get('academy_website', '')}/login" if academy_info.get('academy_website') else '#',
                **academy_info
            }
            
            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=subscription.user.email,
                template_name='subscription_activated',
                variables=variables
            )
            
            if success:
                print(f"✅ تم إرسال إشعار تفعيل الاشتراك إلى {subscription.user.email}")
            else:
                print(f"❌ فشل إرسال إشعار تفعيل الاشتراك: {message}")
            
            return success, message
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار تفعيل الاشتراك: {str(e)}")
            return False, str(e)
    
    def send_subscription_cancelled_email(self, subscription, cancellation_reason=None, admin_message=None, refund_info=None):
        """إرسال إشعار إلغاء الاشتراك"""
        
        # فحص إذا كانت الإشعارات مفعلة (تم إزالة الفحص لأن النظام مفعل)
        
        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()
            
            # إعداد المتغيرات
            variables = {
                'user_name': subscription.user.full_name,
                'package_name': subscription.package.name,
                'cancellation_reason': cancellation_reason or '',
                'admin_message': admin_message or '',
                'refund_info': refund_info or '',
                'contact_url': f"{academy_info.get('academy_website', '')}/contact" if academy_info.get('academy_website') else '#',
                **academy_info
            }
            
            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=subscription.user.email,
                template_name='subscription_cancelled',
                variables=variables
            )
            
            if success:
                print(f"✅ تم إرسال إشعار إلغاء الاشتراك إلى {subscription.user.email}")
            else:
                print(f"❌ فشل إرسال إشعار إلغاء الاشتراك: {message}")
            
            return success, message
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار إلغاء الاشتراك: {str(e)}")
            return False, str(e)
    
    def send_subscription_suspended_email(self, subscription, suspension_reason=None, admin_message=None, reactivation_steps=None):
        """إرسال إشعار إيقاف الاشتراك مؤقتاً"""
        
        # فحص إذا كانت الإشعارات مفعلة (تم إزالة الفحص لأن النظام مفعل)
        
        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()
            
            # إعداد المتغيرات
            variables = {
                'user_name': subscription.user.full_name,
                'package_name': subscription.package.name,
                'suspension_reason': suspension_reason or '',
                'admin_message': admin_message or '',
                'reactivation_steps': reactivation_steps or '',
                'contact_url': f"{academy_info.get('academy_website', '')}/contact" if academy_info.get('academy_website') else '#',
                **academy_info
            }
            
            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=subscription.user.email,
                template_name='subscription_suspended',
                variables=variables
            )
            
            if success:
                print(f"✅ تم إرسال إشعار إيقاف الاشتراك إلى {subscription.user.email}")
            else:
                print(f"❌ فشل إرسال إشعار إيقاف الاشتراك: {message}")
            
            return success, message
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار إيقاف الاشتراك: {str(e)}")
            return False, str(e)

    def send_subscription_reactivated_email(self, subscription, admin_message=None):
        """إرسال إشعار إعادة تفعيل الاشتراك"""

        # فحص إذا كانت الإشعارات مفعلة (تم إزالة الفحص لأن النظام مفعل)

        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()

            # إعداد المتغيرات
            system_currency = get_system_currency()
            variables = {
                'user_name': subscription.user.full_name,
                'package_name': subscription.package.name,
                'package_price': subscription.package.price,
                'package_price_formatted': format_currency(subscription.package.price, system_currency),
                'sessions_remaining': subscription.sessions_remaining or subscription.package.sessions_count,
                'reactivation_date': datetime.now().strftime('%Y-%m-%d'),
                'end_date': subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else 'غير محدد',
                'admin_message': admin_message or '',
                'login_url': f"{academy_info.get('academy_website', '')}/login" if academy_info.get('academy_website') else '#',
                **academy_info
            }

            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=subscription.user.email,
                template_name='subscription_reactivated',
                variables=variables
            )

            if success:
                print(f"✅ تم إرسال إشعار إعادة تفعيل الاشتراك إلى {subscription.user.email}")
            else:
                print(f"❌ فشل إرسال إشعار إعادة تفعيل الاشتراك: {message}")

            return success, message

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار إعادة تفعيل الاشتراك: {str(e)}")
            return False, str(e)

    def send_subscription_status_change_notification(self, subscription, old_status, new_status, reason=None, admin_message=None, additional_info=None):
        """إرسال إشعار تغيير حالة الاشتراك (دالة عامة)"""
        
        try:
            if new_status == 'active':
                # فحص إذا كان الاشتراك كان موقوف مؤقتاً (إعادة تفعيل)
                if old_status == 'suspended':
                    return self.send_subscription_reactivated_email(subscription, admin_message)
                else:
                    return self.send_subscription_activated_email(subscription, admin_message)

            elif new_status == 'cancelled':
                return self.send_subscription_cancelled_email(
                    subscription,
                    cancellation_reason=reason,
                    admin_message=admin_message,
                    refund_info=additional_info
                )

            elif new_status == 'suspended':
                return self.send_subscription_suspended_email(
                    subscription,
                    suspension_reason=reason,
                    admin_message=admin_message,
                    reactivation_steps=additional_info
                )

            else:
                print(f"⚠️ لا يوجد قالب بريد لحالة الاشتراك: {new_status}")
                return False, f"لا يوجد قالب بريد لحالة الاشتراك: {new_status}"
                
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار تغيير حالة الاشتراك: {str(e)}")
            return False, str(e)


# دالة مساعدة للاستخدام السريع
def send_subscription_notification(subscription, action, **kwargs):
    """
    دالة مساعدة لإرسال إشعارات الاشتراك

    Args:
        subscription: كائن الاشتراك
        action: نوع الإجراء ('activated', 'cancelled', 'suspended', 'reactivated')
        **kwargs: معاملات إضافية مثل admin_message, reason, etc.
    """

    notification_service = SubscriptionNotificationService()

    if action == 'activated':
        return notification_service.send_subscription_activated_email(
            subscription,
            admin_message=kwargs.get('admin_message')
        )

    elif action == 'reactivated':
        return notification_service.send_subscription_reactivated_email(
            subscription,
            admin_message=kwargs.get('admin_message')
        )

    elif action == 'cancelled':
        return notification_service.send_subscription_cancelled_email(
            subscription,
            cancellation_reason=kwargs.get('reason'),
            admin_message=kwargs.get('admin_message'),
            refund_info=kwargs.get('refund_info')
        )

    elif action == 'suspended':
        return notification_service.send_subscription_suspended_email(
            subscription,
            suspension_reason=kwargs.get('reason'),
            admin_message=kwargs.get('admin_message'),
            reactivation_steps=kwargs.get('reactivation_steps')
        )

    else:
        return False, f"إجراء غير مدعوم: {action}"
