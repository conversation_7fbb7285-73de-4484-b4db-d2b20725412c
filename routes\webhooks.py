"""
Webhook handlers for payment gateways
"""

from flask import Blueprint, request, jsonify
import stripe
import json
import os
from models import db, Payment, Subscription, PaymentGateway
from datetime import datetime

webhooks_bp = Blueprint('webhooks', __name__)

@webhooks_bp.route('/stripe', methods=['POST'])
def stripe_webhook():
    """Handle Stripe webhooks"""
    payload = request.get_data(as_text=True)
    sig_header = request.headers.get('Stripe-Signature')
    
    # Get webhook secret from database
    stripe_gateway = PaymentGateway.query.filter_by(name='stripe', is_active=True).first()
    if not stripe_gateway:
        return jsonify({'error': 'Stripe gateway not configured'}), 400
    
    endpoint_secret = os.environ.get('STRIPE_WEBHOOK_SECRET')
    if not endpoint_secret:
        return jsonify({'error': 'Webhook secret not configured'}), 400
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError:
        # Invalid payload
        return jsonify({'error': 'Invalid payload'}), 400
    except stripe.error.SignatureVerificationError:
        # Invalid signature
        return jsonify({'error': 'Invalid signature'}), 400
    
    # Handle the event
    if event['type'] == 'payment_intent.succeeded':
        payment_intent = event['data']['object']
        handle_stripe_payment_success(payment_intent)
    elif event['type'] == 'payment_intent.payment_failed':
        payment_intent = event['data']['object']
        handle_stripe_payment_failure(payment_intent)
    else:
        print(f'Unhandled event type: {event["type"]}')
    
    return jsonify({'status': 'success'})

def handle_stripe_payment_success(payment_intent):
    """Handle successful Stripe payment"""
    try:
        # Find payment record by transaction ID
        payment = Payment.query.filter_by(
            transaction_id=payment_intent['id'],
            gateway='stripe'
        ).first()
        
        if payment:
            # Update payment status
            payment.status = 'completed'
            payment.payment_date = datetime.utcnow()
            
            # Update subscription status
            subscription = payment.subscription
            if subscription:
                subscription.status = 'paid_pending_approval'
                subscription.mark_as_paid_pending()
            
            db.session.commit()
            
            # Send confirmation email
            send_payment_confirmation_email(payment)

            # Send internal notification
            from utils.notification_service import NotificationService
            NotificationService.create_payment_notification(payment, 'completed')
            
            print(f'Payment {payment_intent["id"]} marked as completed')
        else:
            print(f'Payment not found for transaction {payment_intent["id"]}')
            
    except Exception as e:
        print(f'Error handling Stripe payment success: {str(e)}')
        db.session.rollback()

def handle_stripe_payment_failure(payment_intent):
    """Handle failed Stripe payment"""
    try:
        # Find payment record by transaction ID
        payment = Payment.query.filter_by(
            transaction_id=payment_intent['id'],
            gateway='stripe'
        ).first()
        
        if payment:
            # Update payment status
            payment.status = 'failed'
            
            # Update subscription status
            subscription = payment.subscription
            if subscription:
                subscription.status = 'cancelled'
            
            db.session.commit()
            
            # Send failure notification email
            send_payment_failure_email(payment, payment_intent.get('last_payment_error', {}).get('message', 'Unknown error'))
            
            print(f'Payment {payment_intent["id"]} marked as failed')
        else:
            print(f'Payment not found for transaction {payment_intent["id"]}')
            
    except Exception as e:
        print(f'Error handling Stripe payment failure: {str(e)}')
        db.session.rollback()

@webhooks_bp.route('/paypal', methods=['POST'])
def paypal_webhook():
    """Handle PayPal webhooks"""
    try:
        # Verify webhook signature (implement PayPal signature verification)
        event_body = request.get_json()
        
        if not event_body:
            return jsonify({'error': 'Invalid payload'}), 400
        
        event_type = event_body.get('event_type')
        
        if event_type == 'PAYMENT.CAPTURE.COMPLETED':
            handle_paypal_payment_success(event_body)
        elif event_type == 'PAYMENT.CAPTURE.DENIED':
            handle_paypal_payment_failure(event_body)
        else:
            print(f'Unhandled PayPal event type: {event_type}')
        
        return jsonify({'status': 'success'})
        
    except Exception as e:
        print(f'Error handling PayPal webhook: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

def handle_paypal_payment_success(event_body):
    """Handle successful PayPal payment"""
    try:
        resource = event_body.get('resource', {})
        transaction_id = resource.get('id')
        
        if not transaction_id:
            return
        
        # Find payment record by transaction ID
        payment = Payment.query.filter_by(
            transaction_id=transaction_id,
            gateway='paypal'
        ).first()
        
        if payment:
            # Update payment status
            payment.status = 'completed'
            payment.payment_date = datetime.utcnow()
            
            # Update subscription status
            subscription = payment.subscription
            if subscription:
                subscription.status = 'paid_pending_approval'
                subscription.mark_as_paid_pending()
            
            db.session.commit()
            
            # Send confirmation email
            send_payment_confirmation_email(payment)

            # Send internal notification
            from utils.notification_service import NotificationService
            NotificationService.create_payment_notification(payment, 'completed')
            
            print(f'PayPal payment {transaction_id} marked as completed')
        else:
            print(f'Payment not found for PayPal transaction {transaction_id}')
            
    except Exception as e:
        print(f'Error handling PayPal payment success: {str(e)}')
        db.session.rollback()

def handle_paypal_payment_failure(event_body):
    """Handle failed PayPal payment"""
    try:
        resource = event_body.get('resource', {})
        transaction_id = resource.get('id')
        
        if not transaction_id:
            return
        
        # Find payment record by transaction ID
        payment = Payment.query.filter_by(
            transaction_id=transaction_id,
            gateway='paypal'
        ).first()
        
        if payment:
            # Update payment status
            payment.status = 'failed'
            
            # Update subscription status
            subscription = payment.subscription
            if subscription:
                subscription.status = 'cancelled'
            
            db.session.commit()
            
            # Send failure notification email
            send_payment_failure_email(payment, 'PayPal payment was denied')
            
            print(f'PayPal payment {transaction_id} marked as failed')
        else:
            print(f'Payment not found for PayPal transaction {transaction_id}')
            
    except Exception as e:
        print(f'Error handling PayPal payment failure: {str(e)}')
        db.session.rollback()

def send_payment_confirmation_email(payment):
    """Send payment confirmation email"""
    try:
        from utils.email_service import EmailService
        from utils.user_management import get_academy_variables
        
        email_service = EmailService()
        academy_vars = get_academy_variables()
        
        email_vars = {
            'user_name': payment.user.full_name,
            'package_name': payment.subscription.package.name,
            'amount': f"{payment.amount:.2f}",
            'payment_method': payment.payment_method.title(),
            'transaction_id': payment.transaction_id,
            'payment_date': payment.payment_date.strftime('%Y-%m-%d %H:%M'),
            **academy_vars
        }
        
        email_service.send_template_email(
            payment.user.email,
            'payment_confirmed',
            email_vars
        )
        
    except Exception as e:
        print(f'Error sending payment confirmation email: {str(e)}')

def send_payment_failure_email(payment, error_message):
    """Send payment failure notification email"""
    try:
        from utils.email_service import EmailService
        from utils.user_management import get_academy_variables
        
        email_service = EmailService()
        academy_vars = get_academy_variables()
        
        email_vars = {
            'user_name': payment.user.full_name,
            'package_name': payment.subscription.package.name,
            'amount': f"{payment.amount:.2f}",
            'payment_method': payment.payment_method.title(),
            'error_message': error_message,
            'retry_url': f"{request.url_root}student/packages",
            **academy_vars
        }
        
        email_service.send_template_email(
            payment.user.email,
            'payment_failed',
            email_vars
        )
        
    except Exception as e:
        print(f'Error sending payment failure email: {str(e)}')
