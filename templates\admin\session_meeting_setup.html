{% extends "base.html" %}

{% block title %}إعداد رابط الحصة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-video me-2"></i>إعداد رابط الحصة</h2>
                <a href="{{ url_for('admin.sessions') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة لقائمة الحصص
                </a>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cog me-2"></i>إعداد رابط {{ session.get_meeting_provider_name() }}
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if session.meeting_provider == 'google_meet' %}
                                {% if session.calendar_event_id %}
                                    <div class="alert alert-success">
                                        <h6><i class="fab fa-google me-2"></i>Google Meet (عبر Google Calendar)</h6>
                                        <p><i class="fas fa-check-circle me-1"></i>تم إنشاء رابط Google Meet تلقائياً عبر Google Calendar!</p>

                                        {% if session.meeting_link and session.meeting_link != 'https://meet.google.com/new' %}
                                            <div class="mt-3">
                                                <p class="mb-2"><strong>رابط الحصة:</strong></p>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" value="{{ session.meeting_link }}" readonly>
                                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ session.meeting_link }}')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                    <a href="{{ session.meeting_link }}" target="_blank" class="btn btn-success">
                                                        <i class="fas fa-external-link-alt me-1"></i>فتح الرابط
                                                    </a>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="alert alert-warning mt-3">
                                                <i class="fas fa-clock me-1"></i>
                                                جاري تحديث رابط Google Meet من Calendar... يرجى تحديث الصفحة خلال دقيقة.
                                            </div>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <div class="alert alert-warning">
                                        <h6><i class="fab fa-google me-2"></i>Google Meet</h6>
                                        <p><i class="fas fa-exclamation-triangle me-1"></i>لم يتم إنشاء Google Calendar event لهذه الحصة.</p>
                                        <p>يمكنك إنشاء رابط يدوياً:</p>

                                        <div class="steps">
                                            <div class="step mb-3">
                                                <span class="badge bg-primary me-2">1</span>
                                                <strong>انقر على الزر أدناه لإنشاء غرفة Google Meet:</strong>
                                                <div class="mt-2">
                                                    <a href="https://meet.google.com/new" target="_blank" class="btn btn-success">
                                                        <i class="fab fa-google me-2"></i>إنشاء غرفة Google Meet
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="step mb-3">
                                                <span class="badge bg-primary me-2">2</span>
                                                <strong>انسخ رابط الغرفة من Google Meet وألصقه هنا:</strong>
                                                <form method="POST" action="{{ url_for('admin.update_session_link', session_id=session.id) }}" class="mt-2">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                    <div class="input-group">
                                                        <input type="url" class="form-control" name="meeting_link"
                                                               placeholder="https://meet.google.com/xxx-yyyy-zzz"
                                                               value="{{ session.meeting_link if session.meeting_link != 'https://meet.google.com/new' else '' }}"
                                                               required>
                                                        <button type="submit" class="btn btn-primary">
                                                            <i class="fas fa-save me-1"></i>حفظ الرابط
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                                
                                {% if session.meeting_link and session.meeting_link != 'https://meet.google.com/new' %}
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-check-circle me-2"></i>رابط الحصة جاهز!</h6>
                                        <p class="mb-2">رابط الحصة الحالي:</p>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="{{ session.meeting_link }}" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ session.meeting_link }}')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <a href="{{ session.meeting_link }}" target="_blank" class="btn btn-success">
                                                <i class="fas fa-external-link-alt me-1"></i>فتح الرابط
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                                
                            {% elif session.meeting_provider == 'jitsi' %}
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-video me-2"></i>Jitsi Meet جاهز!</h6>
                                    <p>رابط Jitsi Meet جاهز للاستخدام. المعلم والطالب سيدخلان نفس الغرفة تلقائياً.</p>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ session.meeting_link }}" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ session.meeting_link }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <a href="{{ session.meeting_link }}" target="_blank" class="btn btn-success">
                                            <i class="fas fa-external-link-alt me-1"></i>فتح الرابط
                                        </a>
                                    </div>
                                </div>
                                
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الحصة
                            </h6>
                        </div>
                        <div class="card-body">
                            <p><strong>الطالب:</strong> {{ session.student.name }}</p>
                            <p><strong>المعلم:</strong> {{ session.teacher.name }}</p>
                            <p><strong>التاريخ:</strong> {{ session.scheduled_datetime.strftime('%Y-%m-%d') }}</p>
                            <p><strong>الوقت:</strong> {{ session.scheduled_datetime.strftime('%H:%M') }}</p>
                            <p><strong>المدة:</strong> {{ session.duration_minutes }} دقيقة</p>
                            <p><strong>المزود:</strong> {{ session.get_meeting_provider_name() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.top = '20px';
        toast.style.right = '20px';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    تم نسخ الرابط بنجاح!
                </div>
            </div>
        `;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    });
}
</script>
{% endblock %}
