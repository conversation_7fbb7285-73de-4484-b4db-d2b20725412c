<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين النظام - أكاديمية القرآن الكريم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome - Multiple CDN sources for reliability -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">

    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Tajawal', sans-serif !important;
        }

        /* Fallback for Font Awesome icons */
        .fas, .far, .fab, .fal, .fad, .fa {
            font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome", sans-serif !important;
            font-weight: 900;
        }

        /* Icon fallbacks */
        .fa-exclamation-triangle:before { content: "⚠️"; }
        .fa-warning:before { content: "⚠️"; }
        .fa-info-circle:before { content: "ℹ️"; }
        .fa-redo:before { content: "🔄"; }
        .fa-arrow-left:before { content: "←"; }
        .fa-shield-alt:before { content: "🛡️"; }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Tajawal', sans-serif;
        }
        .reset-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .reset-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
        }
        .reset-header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .reset-body {
            padding: 2rem;
        }
        .warning-box {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .btn-reset {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(238, 90, 36, 0.3);
            color: white;
        }
        .btn-cancel {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(9, 132, 227, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h2>إعادة تعيين النظام</h2>
                <p class="mb-0">إعادة تعيين إعدادات النظام للبدء من جديد</p>
            </div>
            
            <div class="reset-body">
                <div class="warning-box">
                    <h5><i class="fas fa-warning me-2"></i>تحذير مهم!</h5>
                    <p class="mb-2">هذا الإجراء سيقوم بـ:</p>
                    <ul class="mb-0">
                        <li>حذف جميع حسابات المديرين</li>
                        <li>حذف إعدادات الأكاديمية</li>
                        <li>إعادة تفعيل معالج الإعداد</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>متى تستخدم هذه الميزة؟</h6>
                    <p class="mb-0">
                        استخدم هذه الميزة فقط إذا كنت تواجه مشاكل في الوصول لمعالج الإعداد في بيئة الإنتاج.
                    </p>
                </div>
                
                <div class="text-center">
                    <a href="{{ url_for('setup.reset_setup_force') }}" 
                       class="btn btn-reset me-3"
                       onclick="return confirm('هل أنت متأكد من إعادة تعيين النظام؟ هذا الإجراء لا يمكن التراجع عنه!')">
                        <i class="fas fa-redo me-2"></i>إعادة تعيين النظام
                    </a>
                    
                    <a href="{{ url_for('setup.welcome') }}" class="btn btn-cancel">
                        <i class="fas fa-arrow-left me-2"></i>العودة للإعداد
                    </a>
                </div>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        هذه الميزة متاحة فقط قبل إكمال الإعداد الأولي
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
