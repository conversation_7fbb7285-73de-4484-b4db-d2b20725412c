{% extends "base.html" %}

{% block title %}قوالب البريد الإلكتروني - {{ academy_name }}{% endblock %}
{% block page_title %}قوالب البريد الإلكتروني{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">إدارة قوالب البريد الإلكتروني</h4>
                    <p class="text-muted mb-0">إنشاء وتعديل قوالب الرسائل الإلكترونية</p>
                </div>
                <div>
                    <a href="{{ url_for('admin.email_settings') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right me-1"></i>العودة للإعدادات
                    </a>
                    <a href="{{ url_for('admin.new_email_template') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>قالب جديد
                    </a>
                </div>
            </div>

            <!-- Templates Grid -->
            <div class="row">
                {% for template in templates %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">{{ template.display_name }}</h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('admin.preview_email_template', template_id=template.id) }}">
                                            <i class="fas fa-eye me-2"></i>معاينة
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('admin.edit_email_template', template_id=template.id) }}">
                                            <i class="fas fa-edit me-2"></i>تعديل
                                        </a>
                                    </li>
                                    {% if not template.is_system %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" onclick="deleteTemplate({{ template.id }})">
                                            <i class="fas fa-trash me-2"></i>حذف
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted d-block">اسم القالب:</small>
                                <code>{{ template.name }}</code>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block">الموضوع:</small>
                                <p class="mb-0 small">{{ template.subject[:80] }}{% if template.subject|length > 80 %}...{% endif %}</p>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block">النوع:</small>
                                <span class="badge bg-info">{{ template.template_type }}</span>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block">المتغيرات المتاحة:</small>
                                {% set variables = template.get_variables_list() %}
                                {% if variables %}
                                    <div class="d-flex flex-wrap gap-1">
                                        {% for var in variables[:3] %}
                                        <span class="badge bg-light text-dark">{{ var }}</span>
                                        {% endfor %}
                                        {% if variables|length > 3 %}
                                        <span class="badge bg-light text-dark">+{{ variables|length - 3 }}</span>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <span class="text-muted small">لا توجد متغيرات</span>
                                {% endif %}
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if template.is_active %}
                                        <span class="badge bg-success">مفعل</span>
                                    {% else %}
                                        <span class="badge bg-secondary">معطل</span>
                                    {% endif %}
                                    {% if template.is_system %}
                                        <span class="badge bg-warning">نظام</span>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {{ template.updated_at.strftime('%Y-%m-%d') }}
                                </small>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ url_for('admin.preview_email_template', template_id=template.id) }}" 
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye me-1"></i>معاينة
                                </a>
                                <a href="{{ url_for('admin.edit_email_template', template_id=template.id) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            {% if not templates %}
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد قوالب بريد إلكتروني</h5>
                <p class="text-muted">ابدأ بإنشاء قالب جديد</p>
                <a href="{{ url_for('admin.new_email_template') }}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>إنشاء قالب جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا القالب؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let templateToDelete = null;

function deleteTemplate(templateId) {
    templateToDelete = templateId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (templateToDelete) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/email-template/${templateToDelete}/delete`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrf_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
});
</script>
{% endblock %}
