#!/usr/bin/env python3
"""
Test production setup behavior
"""

import os
import sys

# Simulate production environment
os.environ['RENDER'] = 'true'
os.environ['FLASK_ENV'] = 'production'

# Remove existing database
if os.path.exists('instance/academy.db'):
    os.remove('instance/academy.db')
    print('✅ Old database removed')

if os.path.exists('instance/quranlms.db'):
    os.remove('instance/quranlms.db')
    print('✅ Old quranlms database removed')

print('🌍 Simulating production environment on Render...')
print(f'RENDER env: {os.getenv("RENDER")}')
print(f'FLASK_ENV: {os.getenv("FLASK_ENV")}')

# Test production setup
try:
    # Import app (this will trigger auto_setup)
    print('\n📦 Importing app...')
    from app import app
    from models import db, User, AcademySettings
    
    with app.app_context():
        print('🔄 Checking database after import...')
        
        # Check if any data exists
        admin_count = User.query.filter_by(role='admin').count()
        settings_count = AcademySettings.query.count()
        total_users = User.query.count()
        
        print(f'📊 Database status after import:')
        print(f'   Total users: {total_users}')
        print(f'   Admin users: {admin_count}')
        print(f'   Academy settings: {settings_count}')
        
        # Test setup check
        from routes.setup import is_setup_completed
        setup_completed = is_setup_completed()
        
        print(f'🔍 Setup completed: {setup_completed}')
        
        if setup_completed and (admin_count == 0 and settings_count == 0):
            print('❌ ERROR: Setup shows completed but no data exists!')
            print('🚨 This means middleware will not redirect to setup!')
        elif not setup_completed and (admin_count == 0 and settings_count == 0):
            print('✅ SUCCESS: Setup system working correctly in production!')
            print('🌐 Users will be redirected to setup wizard')
        elif setup_completed and (admin_count > 0 or settings_count > 0):
            print('⚠️ WARNING: Auto-setup created data in production!')
            print('🚨 This means setup wizard will not appear!')
        else:
            print('⚠️ WARNING: Unexpected state')
            
        # Test middleware behavior
        print('\n🔧 Testing middleware behavior...')
        
        # Simulate a request to root path
        with app.test_request_context('/'):
            from flask import request
            print(f'Request path: {request.path}')
            print(f'Request endpoint: {request.endpoint}')
            
            # This should trigger setup redirect if setup not completed
            if not setup_completed:
                print('✅ Middleware will redirect to setup')
            else:
                print('❌ Middleware will NOT redirect to setup')
                
except Exception as e:
    print(f'❌ Error: {str(e)}')
    import traceback
    traceback.print_exc()

print('\n' + '='*60)
print('🎯 PRODUCTION TEST SUMMARY:')
print('='*60)

if 'setup_completed' in locals():
    if not setup_completed:
        print('✅ PASS: Setup wizard will appear on Render')
        print('✅ PASS: Users will be redirected to /setup/')
        print('✅ PASS: No default data created')
    else:
        print('❌ FAIL: Setup wizard will NOT appear on Render')
        print('❌ FAIL: Users will see login page instead')
        print('❌ FAIL: Default data was created automatically')
else:
    print('❌ FAIL: Could not test setup system')

print('='*60)
