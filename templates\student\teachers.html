{% extends "base.html" %}

{% block title %}معلميّ - {{ academy_name }}{% endblock %}
{% block page_title %}معلميّ{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chalkboard-teacher me-2"></i>معلميّ
                </h5>
            </div>
            <div class="card-body">
                {% if teachers_data %}
                    <div class="row">
                        {% for teacher_info in teachers_data %}
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto"
                                         style="width: 80px; height: 80px; font-size: 24px;">
                                        <i class="fas fa-chalkboard-teacher"></i>
                                    </div>
                                    <h5 class="card-title">{{ teacher_info.teacher.full_name }}</h5>
                                    <p class="text-muted">{{ teacher_info.teacher.email }}</p>
                                    
                                    {% if teacher_info.teacher.phone %}
                                    <p class="text-muted">
                                        <i class="fas fa-phone me-1"></i>{{ teacher_info.teacher.phone }}
                                    </p>
                                    {% endif %}
                                    
                                    <!-- Statistics -->
                                    <div class="row text-center mt-3">
                                        <div class="col-4">
                                            <div class="h5 text-primary">{{ teacher_info.total_sessions }}</div>
                                            <div class="small text-muted">إجمالي الحصص</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-success">{{ teacher_info.completed_sessions }}</div>
                                            <div class="small text-muted">حصص مكتملة</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="h5 text-warning">
                                                {% if teacher_info.avg_rating > 0 %}
                                                {{ teacher_info.avg_rating }}
                                                {% else %}
                                                -
                                                {% endif %}
                                            </div>
                                            <div class="small text-muted">التقييم</div>
                                        </div>
                                    </div>
                                    
                                    <!-- Rating Stars -->
                                    {% if teacher_info.avg_rating > 0 %}
                                    <div class="mt-2">
                                        {% for i in range(1, 6) %}
                                            <i class="fas fa-star {{ 'text-warning' if i <= teacher_info.avg_rating else 'text-muted' }}"></i>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Progress Bar -->
                                    {% if teacher_info.total_sessions > 0 %}
                                    {% set completion_rate = (teacher_info.completed_sessions / teacher_info.total_sessions * 100) %}
                                    <div class="mt-3">
                                        <div class="small text-muted mb-1">معدل إكمال الحصص</div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ completion_rate }}%"
                                                 aria-valuenow="{{ completion_rate }}" aria-valuemin="0" aria-valuemax="100">
                                                {{ "%.1f"|format(completion_rate) }}%
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="card-footer">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                data-bs-toggle="modal" data-bs-target="#teacherModal{{ teacher_info.teacher.id }}">
                                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                        </button>
                                        <a href="{{ url_for('student.sessions') }}?teacher={{ teacher_info.teacher.id }}" 
                                           class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-calendar-alt me-1"></i>عرض الحصص
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد معلمين</h5>
                        <p class="text-muted">لم تحضر أي حصص مع معلمين بعد.</p>
                        <a href="{{ url_for('student.packages') }}" class="btn btn-primary">
                            <i class="fas fa-shopping-cart me-2"></i>تصفح الباقات
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Teacher Details Modals -->
{% for teacher_info in teachers_data %}
<div class="modal fade" id="teacherModal{{ teacher_info.teacher.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المعلم: {{ teacher_info.teacher.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto"
                             style="width: 120px; height: 120px; font-size: 36px;">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h5>{{ teacher_info.teacher.full_name }}</h5>
                        <p class="text-muted">{{ teacher_info.teacher.email }}</p>
                        {% if teacher_info.teacher.phone %}
                        <p class="text-muted">
                            <i class="fas fa-phone me-1"></i>{{ teacher_info.teacher.phone }}
                        </p>
                        {% endif %}
                    </div>
                    <div class="col-md-8">
                        <h6>إحصائيات الحصص معي</h6>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="h4 text-primary">{{ teacher_info.total_sessions }}</div>
                                <div class="small text-muted">إجمالي الحصص</div>
                            </div>
                            <div class="col-4">
                                <div class="h4 text-success">{{ teacher_info.completed_sessions }}</div>
                                <div class="small text-muted">حصص مكتملة</div>
                            </div>
                            <div class="col-4">
                                <div class="h4 text-info">{{ teacher_info.total_sessions - teacher_info.completed_sessions }}</div>
                                <div class="small text-muted">حصص أخرى</div>
                            </div>
                        </div>
                        
                        {% if teacher_info.avg_rating > 0 %}
                        <h6>تقييمي للمعلم</h6>
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <span class="h4 text-warning">{{ teacher_info.avg_rating }}</span>
                                <span class="text-muted">/5</span>
                            </div>
                            <div>
                                {% for i in range(1, 6) %}
                                    <i class="fas fa-star {{ 'text-warning' if i <= teacher_info.avg_rating else 'text-muted' }}"></i>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if teacher_info.total_sessions > 0 %}
                        <h6>معدل إكمال الحصص</h6>
                        {% set completion_rate = (teacher_info.completed_sessions / teacher_info.total_sessions * 100) %}
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ completion_rate }}%"
                                 aria-valuenow="{{ completion_rate }}" aria-valuemin="0" aria-valuemax="100">
                                {{ "%.1f"|format(completion_rate) }}%
                            </div>
                        </div>
                        {% endif %}
                        
                        <h6>معلومات إضافية</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>تاريخ أول حصة:</strong></td>
                                <td>{{ teacher_info.first_session_date.strftime('%Y-%m-%d') if teacher_info.first_session_date else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر حصة:</strong></td>
                                <td>{{ teacher_info.last_session_date.strftime('%Y-%m-%d') if teacher_info.last_session_date else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>حالة المعلم:</strong></td>
                                <td>
                                    <span class="badge bg-success">نشط</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="{{ url_for('student.sessions') }}?teacher={{ teacher_info.teacher.id }}" 
                   class="btn btn-primary">
                    عرض جميع الحصص مع هذا المعلم
                </a>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
    .card {
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .progress {
        height: 8px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Add animation to teacher cards
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
</script>
{% endblock %}
