{% extends "base.html" %}

{% block title %}دفع آمن - Stripe - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}دفع آمن عبر Stripe{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Order Summary -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>ملخص الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5>{{ package.name }}</h5>
                        <p class="text-muted">{{ package.description }}</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>{{ package.sessions_count }} حصة</li>
                            <li><i class="fas fa-check text-success me-2"></i>مدة {{ package.duration_days }} يوم</li>
                            {% if package.features %}
                                {% for feature in package.features.split(',') %}
                                <li><i class="fas fa-check text-success me-2"></i>{{ feature.strip() }}</li>
                                {% endfor %}
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="display-6 text-primary">
                            {{ "%.2f"|format(package.price) }} {{ academy_currency }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stripe Payment Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fab fa-cc-stripe me-2"></i>معلومات الدفع
                </h5>
            </div>
            <div class="card-body">
                <form id="payment-form">
                    <div class="mb-3">
                        <label for="card-element" class="form-label">
                            بيانات البطاقة الائتمانية
                        </label>
                        <div id="card-element" class="form-control" style="height: 40px; padding: 10px;">
                            <!-- Stripe Elements will create form elements here -->
                        </div>
                        <div id="card-errors" role="alert" class="text-danger mt-2"></div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="billing-name" class="form-label">الاسم على البطاقة</label>
                            <input type="text" class="form-control" id="billing-name" 
                                   value="{{ current_user.full_name }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="billing-email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="billing-email" 
                                   value="{{ current_user.email }}" required>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" id="submit-button" class="btn btn-primary btn-lg">
                            <i class="fas fa-lock me-2"></i>
                            دفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} بأمان
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('student.purchase_package', package_id=package.id) }}"
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة لاختيار وسيلة الدفع
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Info -->
        <div class="card mt-4 border-success">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <i class="fas fa-shield-alt fa-3x text-success"></i>
                    </div>
                    <div class="col-md-10">
                        <h6 class="text-success">حماية متقدمة بواسطة Stripe</h6>
                        <p class="text-muted small mb-0">
                            <i class="fas fa-check text-success me-1"></i>تشفير SSL 256-bit<br>
                            <i class="fas fa-check text-success me-1"></i>معتمد PCI DSS Level 1<br>
                            <i class="fas fa-check text-success me-1"></i>حماية من الاحتيال
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>جاري معالجة الدفع...</h5>
                <p class="text-muted">يرجى عدم إغلاق هذه الصفحة</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://js.stripe.com/v3/"></script>
<script>
    // Check if we have a valid Stripe key
    const stripeKey = '{{ stripe_key }}';

    if (!stripeKey || stripeKey === 'pk_test_demo_key') {
        // Demo mode - simulate payment without real Stripe
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('payment-form');
            const cardElement = document.getElementById('card-element');

            // Show demo message
            cardElement.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>وضع التجربة:</strong> هذا عرض تجريبي لواجهة الدفع.
                    لا يتم خصم أي مبلغ فعلي.
                </div>
                <div class="form-control" style="height: 40px; display: flex; align-items: center; color: #6c757d;">
                    <i class="fab fa-cc-visa me-2"></i>
                    <span>**** **** **** 4242 | 12/25 | 123</span>
                </div>
            `;

            // Handle demo form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                handleDemoPayment();
            });
        });

        function handleDemoPayment() {
            const submitButton = document.getElementById('submit-button');
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));

            // Show loading
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
            loadingModal.show();

            // Simulate payment processing
            setTimeout(function() {
                // Simulate successful payment
                window.location.href = '{{ url_for("student.payment_success") }}';
            }, 2000);
        }
    } else {
        // Real Stripe mode
        const stripe = Stripe(stripeKey);
        const elements = stripe.elements();

        // Create card element with Arabic styling
        const cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    fontSmoothing: 'antialiased',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
            hidePostalCode: true
        });

        cardElement.mount('#card-element');

    // Handle real-time validation errors from the card Element
    cardElement.on('change', function(event) {
        const displayError = document.getElementById('card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });

    // Handle form submission
    const form = document.getElementById('payment-form');
    const submitButton = document.getElementById('submit-button');

    form.addEventListener('submit', async function(event) {
        event.preventDefault();

        // Disable submit button and show loading
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
        
        // Show loading modal
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();

        // Create payment method
        const {error, paymentMethod} = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
                name: document.getElementById('billing-name').value,
                email: document.getElementById('billing-email').value,
            },
        });

        if (error) {
            // Show error to customer
            const errorElement = document.getElementById('card-errors');
            errorElement.textContent = error.message;
            
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock me-2"></i>دفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} بأمان';
            
            // Hide loading modal
            loadingModal.hide();
        } else {
            // Send payment method to server
            handlePaymentMethodResult(paymentMethod);
        }
    });

    async function handlePaymentMethodResult(paymentMethod) {
        try {
            const response = await fetch('/student/payment/stripe/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    payment_method_id: paymentMethod.id,
                    package_id: {{ package.id }},
                    amount: {{ package.price * 100 }} // Amount in cents
                })
            });

            const result = await response.json();

            if (result.success) {
                // Payment successful
                window.location.href = '{{ url_for("student.payment_success") }}';
            } else if (result.requires_action) {
                // 3D Secure authentication required
                const {error: confirmError} = await stripe.confirmCardPayment(
                    result.payment_intent.client_secret
                );

                if (confirmError) {
                    // Show error to customer
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = confirmError.message;

                    // Re-enable submit button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="fas fa-lock me-2"></i>دفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} بأمان';

                    // Hide loading modal
                    const loadingModal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
                    loadingModal.hide();
                } else {
                    // Payment succeeded after authentication
                    window.location.href = '{{ url_for("student.payment_success") }}';
                }
            } else {
                // Payment failed
                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = result.error || 'حدث خطأ في معالجة الدفع';

                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-lock me-2"></i>دفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} بأمان';

                // Hide loading modal
                const loadingModal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
                loadingModal.hide();
            }
        } catch (error) {
            console.error('Error:', error);
            const errorElement = document.getElementById('card-errors');
            errorElement.textContent = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
            
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock me-2"></i>دفع {{ "%.2f"|format(package.price) }} {{ academy_currency }} بأمان';
            
            // Hide loading modal
            const loadingModal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
            loadingModal.hide();
        }
    }

    // Close the real Stripe mode block
    }
</script>
{% endblock %}
