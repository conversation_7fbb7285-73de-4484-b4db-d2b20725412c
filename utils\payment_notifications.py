"""
خدمة إشعارات الدفع
"""

from utils.email_service import EmailService
from utils.currency_helper import format_currency, get_system_currency
from models import AcademySettings

class PaymentNotificationService:
    def __init__(self):
        self.email_service = EmailService()
    
    def _get_academy_info(self):
        """جلب معلومات الأكاديمية"""
        academy_settings = AcademySettings.query.first()
        if academy_settings:
            return {
                'academy_name': academy_settings.academy_name or 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email or '',
                'academy_phone': academy_settings.contact_phone or '',
                'academy_website': academy_settings.website_url or ''
            }
        return {
            'academy_name': 'أكاديمية القرآن الكريم',
            'academy_email': '',
            'academy_phone': '',
            'academy_website': ''
        }
    
    def send_payment_confirmation(self, user_email, user_name, amount, payment_date, package_name):
        """إرسال تأكيد الدفع"""
        
        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()
            
            # تنسيق المبلغ
            system_currency = get_system_currency()
            amount_formatted = format_currency(amount, system_currency)
            
            # إعداد المتغيرات
            variables = {
                'user_name': user_name,
                'amount': amount,
                'amount_formatted': amount_formatted,
                'payment_date': payment_date,
                'package_name': package_name,
                **academy_info
            }
            
            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=user_email,
                template_name='payment_confirmation',
                variables=variables
            )
            
            if success:
                print(f"✅ تم إرسال تأكيد الدفع إلى {user_email}")
            else:
                print(f"❌ فشل إرسال تأكيد الدفع: {message}")
            
            return success, message
            
        except Exception as e:
            print(f"❌ خطأ في إرسال تأكيد الدفع: {str(e)}")
            return False, str(e)

# دالة مساعدة للاستخدام السريع
def send_payment_confirmation_email(user_email, user_name, amount, payment_date, package_name):
    """دالة مساعدة لإرسال تأكيد الدفع"""
    service = PaymentNotificationService()
    return service.send_payment_confirmation(user_email, user_name, amount, payment_date, package_name)
