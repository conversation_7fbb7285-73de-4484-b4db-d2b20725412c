"""
خدمة الإشعارات الداخلية
Internal Notification Service
"""

from models import Notification, User, UserNotificationSettings, db
from datetime import datetime


class NotificationService:
    """خدمة إدارة الإشعارات الداخلية"""
    
    @staticmethod
    def create_notification(user_id, title, message, notification_type='system', 
                          priority='normal', action_url=None, icon=None):
        """إنشاء إشعار جديد"""
        try:
            # التحقق من وجود المستخدم
            user = User.query.get(user_id)
            if not user:
                return None
            
            # التحقق من إعدادات الإشعارات للمستخدم
            settings = UserNotificationSettings.get_or_create_for_user(user_id)
            if not settings.email_notifications:
                return None  # المستخدم لا يريد إشعارات
            
            notification = Notification.create_notification(
                user_id=user_id,
                title=title,
                message=message,
                notification_type=notification_type,
                priority=priority,
                action_url=action_url,
                icon=icon
            )
            
            return notification
            
        except Exception as e:
            print(f"خطأ في إنشاء الإشعار: {str(e)}")
            return None
    
    @staticmethod
    def create_session_notification(session, action='created'):
        """إنشاء إشعار متعلق بالحصص"""
        try:
            if action == 'created':
                # إشعار للطالب
                NotificationService.create_notification(
                    user_id=session.student_id,
                    title='حصة جديدة مجدولة',
                    message=f'تم جدولة حصة جديدة معك في {session.scheduled_datetime.strftime("%Y/%m/%d %H:%M")} مع المعلم {session.teacher.full_name}',
                    notification_type='session',
                    priority='high',
                    action_url=f'/student/sessions/{session.id}',
                    icon='fas fa-calendar-plus'
                )
                
                # إشعار للمعلم
                NotificationService.create_notification(
                    user_id=session.teacher_id,
                    title='حصة جديدة مجدولة',
                    message=f'تم جدولة حصة جديدة لك في {session.scheduled_datetime.strftime("%Y/%m/%d %H:%M")} مع الطالب {session.student.full_name}',
                    notification_type='session',
                    priority='high',
                    action_url=f'/teacher/sessions/{session.id}',
                    icon='fas fa-calendar-plus'
                )
                
            elif action == 'cancelled':
                # إشعار للطالب والمعلم
                for user_id in [session.student_id, session.teacher_id]:
                    NotificationService.create_notification(
                        user_id=user_id,
                        title='تم إلغاء الحصة',
                        message=f'تم إلغاء الحصة المجدولة في {session.scheduled_datetime.strftime("%Y/%m/%d %H:%M")}',
                        notification_type='session',
                        priority='high',
                        icon='fas fa-calendar-times'
                    )
                    
            elif action == 'completed':
                # إشعار للطالب
                NotificationService.create_notification(
                    user_id=session.student_id,
                    title='تم إكمال الحصة',
                    message=f'تم إكمال حصتك مع المعلم {session.teacher.full_name} بنجاح',
                    notification_type='session',
                    priority='normal',
                    action_url=f'/student/sessions/{session.id}',
                    icon='fas fa-check-circle'
                )
                
        except Exception as e:
            print(f"خطأ في إنشاء إشعار الحصة: {str(e)}")
    
    @staticmethod
    def create_payment_notification(payment, action='completed'):
        """إنشاء إشعار متعلق بالدفع"""
        try:
            if action == 'completed':
                NotificationService.create_notification(
                    user_id=payment.user_id,
                    title='تم استلام الدفعة',
                    message=f'تم استلام دفعتك بقيمة {payment.amount} {payment.currency} بنجاح',
                    notification_type='payment',
                    priority='high',
                    action_url=f'/student/payments/{payment.id}',
                    icon='fas fa-credit-card'
                )
                
                # إشعار للإدمن
                admins = User.query.filter_by(role='admin').all()
                for admin in admins:
                    NotificationService.create_notification(
                        user_id=admin.id,
                        title='دفعة جديدة',
                        message=f'تم استلام دفعة جديدة من {payment.user.full_name} بقيمة {payment.amount} {payment.currency}',
                        notification_type='payment',
                        priority='normal',
                        action_url=f'/admin/payments/{payment.id}',
                        icon='fas fa-money-bill-wave'
                    )
                    
            elif action == 'failed':
                NotificationService.create_notification(
                    user_id=payment.user_id,
                    title='فشل في الدفع',
                    message=f'فشل في معالجة دفعتك بقيمة {payment.amount} {payment.currency}. يرجى المحاولة مرة أخرى',
                    notification_type='payment',
                    priority='urgent',
                    action_url='/student/packages',
                    icon='fas fa-exclamation-triangle'
                )
                
        except Exception as e:
            print(f"خطأ في إنشاء إشعار الدفع: {str(e)}")
    
    @staticmethod
    def create_subscription_notification(subscription, action='activated'):
        """إنشاء إشعار متعلق بالاشتراك"""
        try:
            if action == 'activated':
                NotificationService.create_notification(
                    user_id=subscription.user_id,
                    title='تم تفعيل اشتراكك',
                    message=f'تم تفعيل اشتراكك في باقة {subscription.package.name} بنجاح',
                    notification_type='subscription',
                    priority='high',
                    action_url='/student/subscriptions',
                    icon='fas fa-star'
                )
                
            elif action == 'expired':
                NotificationService.create_notification(
                    user_id=subscription.user_id,
                    title='انتهى اشتراكك',
                    message=f'انتهت صلاحية اشتراكك في باقة {subscription.package.name}',
                    notification_type='subscription',
                    priority='high',
                    action_url='/student/packages',
                    icon='fas fa-clock'
                )
                
            elif action == 'expiring_soon':
                NotificationService.create_notification(
                    user_id=subscription.user_id,
                    title='اشتراكك ينتهي قريباً',
                    message=f'سينتهي اشتراكك في باقة {subscription.package.name} خلال 3 أيام',
                    notification_type='subscription',
                    priority='high',
                    action_url='/student/packages',
                    icon='fas fa-exclamation-circle'
                )
                
        except Exception as e:
            print(f"خطأ في إنشاء إشعار الاشتراك: {str(e)}")
    
    @staticmethod
    def create_user_management_notification(user, action, admin_user=None, reason=None):
        """إنشاء إشعار متعلق بإدارة المستخدمين"""
        try:
            if action == 'approved':
                NotificationService.create_notification(
                    user_id=user.id,
                    title='تم قبول حسابك',
                    message='تم قبول طلب انضمامك للأكاديمية. يمكنك الآن الاستفادة من جميع الخدمات',
                    notification_type='user_management',
                    priority='high',
                    action_url='/student/dashboard' if user.role == 'student' else '/teacher/dashboard',
                    icon='fas fa-check-circle'
                )
                
            elif action == 'rejected':
                message = 'تم رفض طلب انضمامك للأكاديمية'
                if reason:
                    message += f'. السبب: {reason}'
                    
                NotificationService.create_notification(
                    user_id=user.id,
                    title='تم رفض حسابك',
                    message=message,
                    notification_type='user_management',
                    priority='urgent',
                    icon='fas fa-times-circle'
                )
                
            elif action == 'suspended':
                message = 'تم تعليق حسابك مؤقتاً'
                if reason:
                    message += f'. السبب: {reason}'
                    
                NotificationService.create_notification(
                    user_id=user.id,
                    title='تم تعليق حسابك',
                    message=message,
                    notification_type='user_management',
                    priority='urgent',
                    icon='fas fa-pause-circle'
                )
                
        except Exception as e:
            print(f"خطأ في إنشاء إشعار إدارة المستخدمين: {str(e)}")
    
    @staticmethod
    def create_system_notification(title, message, user_ids=None, priority='normal'):
        """إنشاء إشعار نظام لمستخدمين محددين أو جميع المستخدمين"""
        try:
            if user_ids is None:
                # إرسال لجميع المستخدمين النشطين
                users = User.query.filter_by(status='approved').all()
                user_ids = [user.id for user in users]
            
            for user_id in user_ids:
                NotificationService.create_notification(
                    user_id=user_id,
                    title=title,
                    message=message,
                    notification_type='system',
                    priority=priority,
                    icon='fas fa-bullhorn'
                )
                
        except Exception as e:
            print(f"خطأ في إنشاء إشعار النظام: {str(e)}")
    
    @staticmethod
    def get_user_notifications(user_id, limit=20, unread_only=False):
        """جلب إشعارات المستخدم"""
        query = Notification.query.filter_by(user_id=user_id)
        
        if unread_only:
            query = query.filter_by(is_read=False)
            
        return query.order_by(Notification.created_at.desc()).limit(limit).all()
    
    @staticmethod
    def mark_as_read(notification_id, user_id):
        """تحديد إشعار كمقروء"""
        notification = Notification.query.filter_by(
            id=notification_id, 
            user_id=user_id
        ).first()
        
        if notification:
            notification.mark_as_read()
            return True
        return False
    
    @staticmethod
    def mark_all_as_read(user_id):
        """تحديد جميع الإشعارات كمقروءة"""
        notifications = Notification.query.filter_by(
            user_id=user_id, 
            is_read=False
        ).all()
        
        for notification in notifications:
            notification.is_read = True
            notification.read_at = datetime.utcnow()
            
        db.session.commit()
        return len(notifications)
    
    @staticmethod
    def delete_notification(notification_id, user_id):
        """حذف إشعار"""
        notification = Notification.query.filter_by(
            id=notification_id, 
            user_id=user_id
        ).first()
        
        if notification:
            db.session.delete(notification)
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def delete_all_notifications(user_id):
        """حذف جميع الإشعارات"""
        count = Notification.query.filter_by(user_id=user_id).count()
        Notification.query.filter_by(user_id=user_id).delete()
        db.session.commit()
        return count
