{% extends "base.html" %}

{% block title %}طلابي - {{ academy_name }}{% endblock %}
{% block page_title %}طلابي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>طلابي
                </h5>
            </div>
            <div class="card-body">
                {% if students_data %}
                    <div class="row">
                        {% for student_data in students_data %}
                        {% set student = student_data.student %}
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto"
                                         style="width: 80px; height: 80px; font-size: 24px;">
                                        <i class="fas fa-user-graduate"></i>
                                    </div>
                                    <h5 class="card-title">{{ student.full_name }}</h5>
                                    <p class="text-muted">{{ student.email }}</p>
                                    
                                    {% if student.phone %}
                                    <p class="text-muted">
                                        <i class="fas fa-phone me-1"></i>{{ student.phone }}
                                    </p>
                                    {% endif %}
                                    
                                    <!-- Statistics -->
                                    <div class="row text-center mt-3">
                                        <div class="col-6">
                                            <div class="h5 text-primary">{{ student_data.total_sessions }}</div>
                                            <div class="small text-muted">إجمالي الحصص</div>
                                        </div>
                                        <div class="col-6">
                                            <div class="h5 text-success">{{ student_data.completed_sessions }}</div>
                                            <div class="small text-muted">حصص مكتملة</div>
                                        </div>
                                    </div>

                                    <!-- Progress Bar -->
                                    {% if student_data.total_sessions > 0 %}
                                    {% set completion_rate = (student_data.completed_sessions / student_data.total_sessions * 100) %}
                                    <div class="mt-3">
                                        <div class="small text-muted mb-1">معدل إكمال الحصص</div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ completion_rate }}%"
                                                 aria-valuenow="{{ completion_rate }}" aria-valuemin="0" aria-valuemax="100">
                                                {{ "%.1f"|format(completion_rate) }}%
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Student Status -->
                                    <div class="mt-3">
                                        <span class="badge {{ 'bg-success' if student.status == 'approved' else 'bg-warning' if student.status == 'pending' else 'bg-danger' }}">
                                            {% if student.status == 'approved' %}طالب نشط
                                            {% elif student.status == 'pending' %}في الانتظار
                                            {% else %}غير نشط{% endif %}
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                data-bs-toggle="modal" data-bs-target="#studentModal{{ student.id }}">
                                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                        </button>
                                        <a href="{{ url_for('teacher.sessions') }}?student={{ student.id }}" 
                                           class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-calendar-alt me-1"></i>عرض الحصص
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد طلاب</h5>
                        <p class="text-muted">لم تقم بتدريس أي طلاب بعد.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Student Details Modals -->
{% for student_data in students_data %}
{% set student = student_data.student %}
<div class="modal fade" id="studentModal{{ student.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطالب: {{ student.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto"
                             style="width: 120px; height: 120px; font-size: 36px;">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h5>{{ student.full_name }}</h5>
                        <p class="text-muted">{{ student.email }}</p>
                        {% if student.phone %}
                        <p class="text-muted">
                            <i class="fas fa-phone me-1"></i>{{ student.phone }}
                        </p>
                        {% endif %}
                        
                        <span class="badge {{ 'bg-success' if student.status == 'approved' else 'bg-warning' if student.status == 'pending' else 'bg-danger' }}">
                            {% if student.status == 'approved' %}طالب نشط
                            {% elif student.status == 'pending' %}في الانتظار
                            {% else %}غير نشط{% endif %}
                        </span>
                    </div>
                    <div class="col-md-8">
                        <h6>إحصائيات الحصص معي</h6>
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="h4 text-primary">{{ student_data.total_sessions }}</div>
                                <div class="small text-muted">إجمالي الحصص</div>
                            </div>
                            <div class="col-4">
                                <div class="h4 text-success">{{ student_data.completed_sessions }}</div>
                                <div class="small text-muted">حصص مكتملة</div>
                            </div>
                            <div class="col-4">
                                <div class="h4 text-info">{{ student_data.upcoming_sessions }}</div>
                                <div class="small text-muted">حصص قادمة</div>
                            </div>
                        </div>
                        
                        {% if student_data.total_sessions > 0 %}
                        <h6>معدل إكمال الحصص</h6>
                        {% set completion_rate = (student_data.completed_sessions / student_data.total_sessions * 100) %}
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: {{ completion_rate }}%"
                                 aria-valuenow="{{ completion_rate }}" aria-valuemin="0" aria-valuemax="100">
                                {{ "%.1f"|format(completion_rate) }}%
                            </div>
                        </div>
                        {% endif %}
                        
                        <h6>معلومات الطالب</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>تاريخ التسجيل:</strong></td>
                                <td>{{ student.created_at.strftime('%Y-%m-%d') if student.created_at else '-' }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر دخول:</strong></td>
                                <td>{{ student.last_login.strftime('%Y-%m-%d %H:%M') if student.last_login else 'لم يدخل بعد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>إجمالي الاشتراكات:</strong></td>
                                <td>{{ student.subscriptions|length }}</td>
                            </tr>
                            <tr>
                                <td><strong>الاشتراكات النشطة:</strong></td>
                                <td>{{ student.subscriptions|selectattr('status', 'equalto', 'active')|list|length }}</td>
                            </tr>
                        </table>
                        
                        <!-- Recent Sessions -->
                        <h6>آخر الحصص</h6>
                        <p class="text-muted">لعرض الحصص التفصيلية، اضغط على "عرض الحصص" أدناه.</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="{{ url_for('teacher.sessions') }}?student={{ student.id }}" 
                   class="btn btn-primary">
                    عرض جميع الحصص مع هذا الطالب
                </a>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
    .card {
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .progress {
        height: 8px;
    }
    
    .list-group-item {
        border: 1px solid rgba(0,0,0,.125);
        margin-bottom: 5px;
        border-radius: 0.375rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Add animation to student cards
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
</script>
{% endblock %}
