# Flask Configuration
FLASK_ENV=production
SECRET_KEY=your-super-secret-production-key-change-this
DEBUG=False

# Database Configuration
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# Email Configuration (SMTP)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Payment Gateway Configuration
# Stripe
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=live

# Google Services (Optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_SERVICE_ACCOUNT_FILE=path/to/service-account.json

# Academy Settings
ACADEMY_NAME=أكاديمية القرآن الكريم
ACADEMY_EMAIL=<EMAIL>
ACADEMY_PHONE=+**********
ACADEMY_WEBSITE=https://academy.com

# Security Settings
CSRF_SECRET_KEY=your-csrf-secret-key
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# Render Specific (for deployment)
PYTHON_VERSION=3.11.0
PORT=10000
