{% extends "setup/base.html" %}

{% block title %}إعدادات الأمان - {{ super() }}{% endblock %}

{% block header_title %}إعدادات الأمان{% endblock %}
{% block header_subtitle %}قم بتخصيص إعدادات الأمان والحماية للنظام{% endblock %}

{% block content %}
<div class="text-center mb-4">
    <div class="feature-icon">
        <i class="fas fa-shield-alt"></i>
    </div>
    <h3 class="mb-3">إعدادات الأمان والحماية</h3>
    <p class="text-muted">
        تخصيص إعدادات الأمان لحماية النظام والمستخدمين
    </p>
</div>

<form method="POST" id="securityForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>إعدادات المستخدمين</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>إعدادات الأمان الأساسية:</strong>
                <ul class="mb-0 mt-2">
                    <li>التحقق من البريد الإلكتروني مفعل افتراضياً</li>
                    <li>كلمة المرور 8 أحرف كحد أدنى</li>
                    <li>5 محاولات تسجيل دخول كحد أقصى</li>
                    <li>انتهاء الجلسة بعد 30 دقيقة من عدم النشاط</li>
                </ul>
            </div>
        </div>
    </div>
    

    
    <div class="alert alert-success">
        <h6><i class="fas fa-check-circle me-2"></i>ميزات الأمان المدمجة:</h6>
        <ul class="mb-0">
            <li>تشفير كلمات المرور باستخدام bcrypt</li>
            <li>حماية CSRF لجميع النماذج</li>
            <li>تشفير SSL/TLS للاتصالات</li>
            <li>حماية من هجمات SQL Injection</li>
            <li>تسجيل العمليات الحساسة</li>
        </ul>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>ملاحظة:</strong> يمكنك تخطي هذه الخطوة واستخدام الإعدادات الافتراضية الآمنة، ثم تعديلها لاحقاً من لوحة التحكم.
    </div>
    
    <div class="d-flex justify-content-between align-items-center mt-4">
        <a href="{{ url_for('setup.email_settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>السابق
        </a>
        
        <div class="text-center">
            <button type="submit" name="skip_step" value="1" class="btn btn-outline-secondary me-2">
                <i class="fas fa-forward me-2"></i>تخطي
            </button>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-check me-2"></i>إنهاء الإعداد
            </button>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('securityForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
        if (e.submitter === submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        }
    });
});
</script>
{% endblock %}
