{% extends "setup/base.html" %}

{% block title %}إعدادات الأمان - {{ super() }}{% endblock %}

{% block header_title %}إعدادات الأمان{% endblock %}
{% block header_subtitle %}قم بتخصيص إعدادات الأمان والحماية للنظام{% endblock %}

{% block content %}
<div class="text-center mb-4">
    <div class="feature-icon">
        <i class="fas fa-shield-alt"></i>
    </div>
    <h3 class="mb-3">إعدادات الأمان والحماية</h3>
    <p class="text-muted">
        تخصيص إعدادات الأمان لحماية النظام والمستخدمين
    </p>
</div>

<form method="POST" id="securityForm">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>إعدادات المستخدمين</h5>
        </div>
        <div class="card-body">
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="require_email_verification" name="require_email_verification" 
                       {{ 'checked' if settings and settings.require_email_verification else 'checked' }}>
                <label class="form-check-label" for="require_email_verification">
                    <i class="fas fa-envelope-check me-2"></i>
                    <strong>تفعيل التحقق من البريد الإلكتروني</strong>
                    <br><small class="text-muted">يتطلب من المستخدمين تأكيد بريدهم الإلكتروني قبل تفعيل الحساب</small>
                </label>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-lock me-2"></i>إعدادات كلمات المرور</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password_min_length" class="form-label">
                        <i class="fas fa-ruler me-2"></i>الحد الأدنى لطول كلمة المرور
                    </label>
                    <select class="form-select" id="password_min_length" name="password_min_length">
                        <option value="6" {{ 'selected' if settings and settings.password_min_length == 6 else '' }}>6 أحرف</option>
                        <option value="8" {{ 'selected' if settings and settings.password_min_length == 8 else 'selected' }}>8 أحرف (موصى به)</option>
                        <option value="10" {{ 'selected' if settings and settings.password_min_length == 10 else '' }}>10 أحرف</option>
                        <option value="12" {{ 'selected' if settings and settings.password_min_length == 12 else '' }}>12 حرف</option>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="max_login_attempts" class="form-label">
                        <i class="fas fa-ban me-2"></i>عدد محاولات تسجيل الدخول المسموحة
                    </label>
                    <select class="form-select" id="max_login_attempts" name="max_login_attempts">
                        <option value="3" {{ 'selected' if settings and settings.max_login_attempts == 3 else '' }}>3 محاولات</option>
                        <option value="5" {{ 'selected' if settings and settings.max_login_attempts == 5 else 'selected' }}>5 محاولات (موصى به)</option>
                        <option value="10" {{ 'selected' if settings and settings.max_login_attempts == 10 else '' }}>10 محاولات</option>
                        <option value="0" {{ 'selected' if settings and settings.max_login_attempts == 0 else '' }}>غير محدود</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-clock me-2"></i>إعدادات الجلسات</h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="session_timeout" class="form-label">
                    <i class="fas fa-hourglass-half me-2"></i>مهلة انتهاء الجلسة (بالدقائق)
                </label>
                <select class="form-select" id="session_timeout" name="session_timeout">
                    <option value="15" {{ 'selected' if settings and settings.session_timeout == 15 else '' }}>15 دقيقة</option>
                    <option value="30" {{ 'selected' if settings and settings.session_timeout == 30 else 'selected' }}>30 دقيقة (موصى به)</option>
                    <option value="60" {{ 'selected' if settings and settings.session_timeout == 60 else '' }}>ساعة واحدة</option>
                    <option value="120" {{ 'selected' if settings and settings.session_timeout == 120 else '' }}>ساعتان</option>
                    <option value="480" {{ 'selected' if settings and settings.session_timeout == 480 else '' }}>8 ساعات</option>
                </select>
                <div class="form-text">المدة التي يبقى فيها المستخدم مسجل الدخول بدون نشاط</div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-success">
        <h6><i class="fas fa-check-circle me-2"></i>ميزات الأمان المدمجة:</h6>
        <ul class="mb-0">
            <li>تشفير كلمات المرور باستخدام bcrypt</li>
            <li>حماية CSRF لجميع النماذج</li>
            <li>تشفير SSL/TLS للاتصالات</li>
            <li>حماية من هجمات SQL Injection</li>
            <li>تسجيل العمليات الحساسة</li>
        </ul>
    </div>
    
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>ملاحظة:</strong> يمكنك تخطي هذه الخطوة واستخدام الإعدادات الافتراضية الآمنة، ثم تعديلها لاحقاً من لوحة التحكم.
    </div>
    
    <div class="d-flex justify-content-between align-items-center mt-4">
        <a href="{{ url_for('setup.email_settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>السابق
        </a>
        
        <div class="text-center">
            <button type="submit" name="skip_step" value="1" class="btn btn-outline-secondary me-2">
                <i class="fas fa-forward me-2"></i>تخطي
            </button>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-check me-2"></i>إنهاء الإعداد
            </button>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('securityForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
        if (e.submitter === submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        }
    });
});
</script>
{% endblock %}
