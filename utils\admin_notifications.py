"""
خدمة إشعارات الإدمن
إرسال إشعارات البريد الإلكتروني للإدمن عند الأحداث المهمة
"""

from datetime import datetime
from flask import current_app
from models import EmailSettings, AcademySettings, User, db
from utils.email_service import EmailService


class AdminNotificationService:
    def __init__(self):
        self.email_service = EmailService()
    
    def _get_academy_info(self):
        """جلب معلومات الأكاديمية"""
        academy_settings = AcademySettings.query.first()
        if academy_settings:
            return {
                'academy_name': academy_settings.academy_name or 'أكاديمية القرآن الكريم',
                'academy_email': academy_settings.contact_email or '',
                'academy_phone': academy_settings.contact_phone or '',
                'academy_website': academy_settings.website_url or '',
                'academy_address': academy_settings.address or ''
            }
        return {
            'academy_name': 'أكاديمية القرآن الكريم',
            'academy_email': '',
            'academy_phone': '',
            'academy_website': '',
            'academy_address': ''
        }
    
    def _get_admin_email(self):
        """جلب بريد الإدمن"""
        academy_info = self._get_academy_info()
        admin_email = academy_info.get('academy_email')
        
        if not admin_email:
            # البحث عن أول إدمن في النظام
            admin_user = User.query.filter_by(role='admin', status='approved').first()
            if admin_user:
                admin_email = admin_user.email
        
        return admin_email
    
    def _is_admin_payment_notification_enabled(self):
        """فحص إذا كان إشعار الدفعات للإدمن مفعل"""
        email_settings = EmailSettings.query.first()
        if not email_settings:
            return True  # افتراضياً مفعل
        
        return getattr(email_settings, 'admin_payment_notification_enabled', True)
    
    def _is_admin_new_user_notification_enabled(self):
        """فحص إذا كان إشعار المستخدمين الجدد للإدمن مفعل"""
        email_settings = EmailSettings.query.first()
        if not email_settings:
            return True  # افتراضياً مفعل
        
        return getattr(email_settings, 'admin_new_user_notification_enabled', True)
    
    def send_payment_notification_to_admin(self, user, payment_data):
        """إرسال إشعار للإدمن بدفعة جديدة"""
        
        if not self._is_admin_payment_notification_enabled():
            print("ℹ️ إشعارات الدفعات للإدمن معطلة")
            return False, "إشعارات الدفعات للإدمن معطلة"
        
        admin_email = self._get_admin_email()
        if not admin_email:
            print("⚠️ لا يوجد بريد إدمن محدد")
            return False, "لا يوجد بريد إدمن محدد"
        
        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()
            
            # إعداد المتغيرات
            variables = {
                'user_name': user.full_name,
                'user_email': user.email,
                'user_phone': user.phone or 'غير محدد',
                'amount_formatted': payment_data.get('amount_formatted', ''),
                'payment_method': payment_data.get('payment_method', ''),
                'transaction_id': payment_data.get('transaction_id', ''),
                'payment_date': payment_data.get('payment_date', datetime.now().strftime('%Y-%m-%d %H:%M')),
                'package_name': payment_data.get('package_name', ''),
                'payment_status': payment_data.get('payment_status', 'في انتظار المراجعة'),
                'admin_panel_url': f"{academy_info.get('academy_website', '')}/admin/payments" if academy_info.get('academy_website') else '#',
                **academy_info
            }
            
            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=admin_email,
                template_name='admin_payment_notification',
                variables=variables
            )
            
            if success:
                print(f"✅ تم إرسال إشعار الدفعة للإدمن: {admin_email}")
            else:
                print(f"❌ فشل إرسال إشعار الدفعة للإدمن: {message}")
            
            return success, message
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الدفعة للإدمن: {str(e)}")
            return False, str(e)
    
    def send_new_user_notification_to_admin(self, user):
        """إرسال إشعار للإدمن بمستخدم جديد"""
        
        if not self._is_admin_new_user_notification_enabled():
            print("ℹ️ إشعارات المستخدمين الجدد للإدمن معطلة")
            return False, "إشعارات المستخدمين الجدد للإدمن معطلة"
        
        admin_email = self._get_admin_email()
        if not admin_email:
            print("⚠️ لا يوجد بريد إدمن محدد")
            return False, "لا يوجد بريد إدمن محدد"
        
        try:
            # جلب معلومات الأكاديمية
            academy_info = self._get_academy_info()
            
            # إعداد المتغيرات
            variables = {
                'user_name': user.full_name,
                'user_email': user.email,
                'user_phone': user.phone or 'غير محدد',
                'user_role': user.role,
                'registration_date': datetime.now().strftime('%Y-%m-%d %H:%M'),
                'user_status': user.status or 'في انتظار الموافقة',
                'admin_panel_url': f"{academy_info.get('academy_website', '')}/admin/users" if academy_info.get('academy_website') else '#',
                **academy_info
            }
            
            # إرسال البريد
            success, message = self.email_service.send_template_email(
                to_email=admin_email,
                template_name='admin_new_user_notification',
                variables=variables
            )
            
            if success:
                print(f"✅ تم إرسال إشعار المستخدم الجديد للإدمن: {admin_email}")
            else:
                print(f"❌ فشل إرسال إشعار المستخدم الجديد للإدمن: {message}")
            
            return success, message
            
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار المستخدم الجديد للإدمن: {str(e)}")
            return False, str(e)


def send_payment_notification_to_admin(payment):
    """إرسال إشعار للإدمن بدفعة جديدة"""

    try:
        # الحصول على بريد الإدمن
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            return False, "لا يوجد مستخدم إدمن في النظام"

        admin_email = admin_user.email
        if not admin_email:
            return False, "لا يوجد بريد إلكتروني للإدمن"

        # إنشاء خدمة البريد الإلكتروني
        email_service = AdminNotificationService()

        try:
            # جلب معلومات الأكاديمية
            academy_info = email_service._get_academy_info()

            # تنسيق المبلغ
            amount_formatted = f"{payment.amount:.2f} {payment.currency}"

            # إعداد المتغيرات
            variables = {
                'user_name': payment.user.full_name,
                'user_email': payment.user.email,
                'user_phone': payment.user.phone or 'غير محدد',
                'amount_formatted': amount_formatted,
                'payment_method': payment.payment_method.title(),
                'transaction_id': payment.transaction_id,
                'payment_date': payment.payment_date.strftime('%Y-%m-%d %H:%M'),
                'package_name': payment.subscription.package.name,
                'payment_status': payment.status,
                'admin_panel_url': f"{academy_info.get('academy_website', '')}/admin/payments" if academy_info.get('academy_website') else '#',
                **academy_info
            }

            # إرسال البريد
            success, message = email_service.email_service.send_template_email(
                to_email=admin_email,
                template_name='admin_payment_notification',
                variables=variables
            )

            if success:
                print(f"✅ تم إرسال إشعار الدفعة الجديدة للإدمن: {admin_email}")
            else:
                print(f"❌ فشل إرسال إشعار الدفعة الجديدة للإدمن: {message}")

            return success, message

        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار الدفعة الجديدة للإدمن: {str(e)}")
            return False, str(e)

    except Exception as e:
        print(f"❌ خطأ في إرسال إشعار الدفعة الجديدة للإدمن: {str(e)}")
        return False, str(e)


# دوال مساعدة للاستخدام السريع
def send_payment_notification_to_admin(user, payment_data):
    """
    دالة مساعدة لإرسال إشعار دفعة للإدمن
    
    Args:
        user: كائن المستخدم
        payment_data: بيانات الدفعة (dict)
    """
    
    notification_service = AdminNotificationService()
    return notification_service.send_payment_notification_to_admin(user, payment_data)


def send_new_user_notification_to_admin(user):
    """
    دالة مساعدة لإرسال إشعار مستخدم جديد للإدمن
    
    Args:
        user: كائن المستخدم الجديد
    """
    
    notification_service = AdminNotificationService()
    return notification_service.send_new_user_notification_to_admin(user)


def notify_admin_of_payment(user, amount, payment_method, transaction_id, package_name, payment_status='pending'):
    """
    دالة شاملة لإشعار الإدمن بدفعة جديدة
    
    Args:
        user: كائن المستخدم
        amount: المبلغ
        payment_method: طريقة الدفع
        transaction_id: رقم المعاملة
        package_name: اسم الباقة
        payment_status: حالة الدفعة
    """
    
    from utils.currency_helper import format_currency, get_system_currency
    
    # تنسيق المبلغ
    system_currency = get_system_currency()
    amount_formatted = format_currency(amount, system_currency)
    
    # إعداد بيانات الدفعة
    payment_data = {
        'amount_formatted': amount_formatted,
        'payment_method': payment_method,
        'transaction_id': transaction_id,
        'payment_date': datetime.now().strftime('%Y-%m-%d %H:%M'),
        'package_name': package_name,
        'payment_status': payment_status
    }
    
    return send_payment_notification_to_admin(user, payment_data)
