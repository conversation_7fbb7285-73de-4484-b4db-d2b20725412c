# 📂 إعداد مستودع GitHub لـ Quran LMS

## 🔗 خطوات إنشاء المستودع على GitHub:

### 1. إنشاء مستودع جديد
1. اذهب إلى [github.com](https://github.com)
2. سجل دخول إلى حسابك
3. اضغط على زر **"New"** أو **"+"** → **"New repository"**

### 2. إعدادات المستودع
املأ البيانات التالية:

**Repository name**: `quranlms`

**Description**: 
```
🕌 Quran LMS - Complete Learning Management System for Quran Academies | نظام إدارة تعلم شامل لأكاديميات القرآن الكريم
```

**Visibility**: 
- ✅ **Public** (للمشاركة مع المجتمع)
- أو **Private** (للاستخدام الخاص)

**Initialize repository**:
- ❌ لا تختر "Add a README file"
- ❌ لا تختر "Add .gitignore"
- ❌ لا تختر "Choose a license"

(لأن هذه الملفات موجودة بالفعل في مشروعنا)

### 3. إنشاء المستودع
اضغط **"Create repository"**

### 4. ربط المستودع المحلي بـ GitHub
بعد إنشاء المستودع، ستظهر لك صفحة بالتعليمات. استخدم هذه الأوامر:

```bash
# في مجلد المشروع (quranlms)
git remote add origin https://github.com/yourusername/quranlms.git
git push -u origin main
```

**استبدل `yourusername` باسم المستخدم الخاص بك على GitHub**

## 🚀 الأوامر الكاملة للرفع:

```bash
# التأكد من وجود commit
git log --oneline

# إضافة remote origin
git remote add origin https://github.com/yourusername/quranlms.git

# رفع الكود إلى GitHub
git push -u origin main
```

## ✅ التحقق من نجاح الرفع:

1. اذهب إلى مستودعك على GitHub
2. تأكد من ظهور جميع الملفات
3. تأكد من ظهور README.md بشكل صحيح
4. تحقق من وجود ملفات الإنتاج:
   - `requirements.txt`
   - `Procfile`
   - `runtime.txt`
   - `.env.example`

## 🔧 في حالة وجود مشاكل:

### خطأ: remote origin already exists
```bash
git remote remove origin
git remote add origin https://github.com/yourusername/quranlms.git
```

### خطأ: failed to push
```bash
git pull origin main --allow-unrelated-histories
git push -u origin main
```

### خطأ: authentication failed
- تأكد من صحة اسم المستخدم
- استخدم Personal Access Token بدلاً من كلمة المرور

## 📱 الخطوة التالية:

بعد رفع الكود بنجاح إلى GitHub:
1. اتبع دليل `QUICK_DEPLOY.md` للنشر على Render
2. أو اتبع دليل `DEPLOYMENT.md` للتعليمات المفصلة

---

**🎉 مبروك! مشروعك الآن على GitHub وجاهز للنشر!**
