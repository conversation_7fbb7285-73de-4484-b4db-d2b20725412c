{% extends "base.html" %}

{% block title %}معاينة القالب: {{ template.display_name }} - {{ academy_name }}{% endblock %}
{% block page_title %}معاينة القالب{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">معاينة القالب: {{ template.display_name }}</h4>
                    <p class="text-muted mb-0">{{ template.name }}</p>
                </div>
                <div>
                    <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right me-1"></i>العودة للقوالب
                    </a>
                    <a href="{{ url_for('admin.edit_email_template', template_id=template.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i>تعديل القالب
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Template Info -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات القالب
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label small text-muted">اسم القالب</label>
                                <div><code>{{ template.name }}</code></div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label small text-muted">الاسم المعروض</label>
                                <div>{{ template.display_name }}</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label small text-muted">النوع</label>
                                <div><span class="badge bg-info">{{ template.template_type }}</span></div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label small text-muted">الحالة</label>
                                <div>
                                    {% if template.is_active %}
                                        <span class="badge bg-success">مفعل</span>
                                    {% else %}
                                        <span class="badge bg-secondary">معطل</span>
                                    {% endif %}
                                    {% if template.is_system %}
                                        <span class="badge bg-warning">نظام</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label small text-muted">آخر تحديث</label>
                                <div>{{ template.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                            </div>
                            
                            {% set variables = template.get_variables_list() %}
                            {% if variables %}
                            <div class="mb-3">
                                <label class="form-label small text-muted">المتغيرات المتاحة</label>
                                <div class="d-flex flex-wrap gap-1">
                                    {% for var in variables %}
                                    <span class="badge bg-light text-dark">{{ var }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Sample Data Used -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>البيانات التجريبية
                            </h6>
                        </div>
                        <div class="card-body">
                            <small class="text-muted">البيانات المستخدمة في المعاينة:</small>
                            <div class="mt-2">
                                <code class="small">
                                    user_name: "أحمد محمد"<br>
                                    user_email: "<EMAIL>"<br>
                                    academy_name: "أكاديمية القرآن الكريم"<br>
                                    session_date: "2024-01-15"<br>
                                    session_time: "10:00 صباحاً"<br>
                                    teacher_name: "الأستاذ محمد"
                                </code>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Preview -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-eye me-2"></i>معاينة الرسالة
                            </h6>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary active" onclick="showPreview('html')">
                                    HTML
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="showPreview('text')">
                                    نص عادي
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Subject Preview -->
                            <div class="mb-3">
                                <label class="form-label small text-muted">الموضوع</label>
                                <div class="p-2 bg-light rounded">
                                    <strong>{{ rendered_subject }}</strong>
                                </div>
                            </div>
                            
                            <!-- HTML Preview -->
                            <div id="html-preview">
                                <label class="form-label small text-muted">محتوى HTML</label>
                                <div class="border rounded p-3" style="background-color: #f8f9fa;">
                                    <div style="background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        {{ rendered_html|safe }}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Text Preview -->
                            <div id="text-preview" style="display: none;">
                                <label class="form-label small text-muted">النص العادي</label>
                                <div class="border rounded p-3 bg-light">
                                    <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">{{ template.body_text or 'لا يوجد نص عادي محدد' }}</pre>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    هذه معاينة تستخدم بيانات تجريبية
                                </small>
                                <div>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printPreview()">
                                        <i class="fas fa-print me-1"></i>طباعة
                                    </button>
                                    <a href="{{ url_for('admin.edit_email_template', template_id=template.id) }}" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>تعديل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showPreview(type) {
    // Update buttons
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Show/hide previews
    if (type === 'html') {
        document.getElementById('html-preview').style.display = 'block';
        document.getElementById('text-preview').style.display = 'none';
    } else {
        document.getElementById('html-preview').style.display = 'none';
        document.getElementById('text-preview').style.display = 'block';
    }
}

function printPreview() {
    const printWindow = window.open('', '_blank');
    const htmlContent = document.querySelector('#html-preview .border').innerHTML;
    
    printWindow.document.write(
        '<html>' +
        '<head>' +
            '<title>معاينة القالب: {{ template.display_name }}</title>' +
            '<style>' +
                'body { font-family: Arial, sans-serif; direction: rtl; }' +
                '.container { max-width: 600px; margin: 20px auto; }' +
            '</style>' +
        '</head>' +
        '<body>' +
            '<div class="container">' +
                '<h3>الموضوع: {{ rendered_subject }}</h3>' +
                '<hr>' +
                htmlContent +
            '</div>' +
        '</body>' +
        '</html>'
    );
    
    printWindow.document.close();
    printWindow.print();
}
</script>
{% endblock %}
