from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_required, current_user
from models import User, AcademySettings, PaymentGateway, EmailSettings, db
from werkzeug.utils import secure_filename
import os
import uuid

setup_bp = Blueprint('setup', __name__)

def is_setup_completed():
    """Check if initial setup is completed"""
    # Check if admin user exists and academy settings are configured
    admin_exists = User.query.filter_by(role='admin').first() is not None
    academy_settings = AcademySettings.query.first()
    
    if admin_exists and academy_settings and academy_settings.academy_name:
        return True
    return False

def setup_required(f):
    """Decorator to ensure setup is not completed"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if is_setup_completed():
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@setup_bp.route('/')
@setup_required
def welcome():
    """Welcome page - Introduction to Marketation LMS"""
    return render_template('setup/welcome.html')

@setup_bp.route('/about')
@setup_required
def about():
    """About Marketation and the system"""
    return render_template('setup/about.html')

@setup_bp.route('/features')
@setup_required
def features():
    """System features overview"""
    return render_template('setup/features.html')

@setup_bp.route('/developer')
@setup_required
def developer():
    """Developer and company information"""
    return render_template('setup/developer.html')

@setup_bp.route('/admin-account', methods=['GET', 'POST'])
@setup_required
def admin_account():
    """Create admin account"""
    if request.method == 'POST':
        try:
            # Check if admin already exists
            existing_admin = User.query.filter_by(role='admin').first()
            if existing_admin:
                flash('حساب المدير موجود بالفعل.', 'warning')
                return redirect(url_for('setup.academy_settings'))
            
            # Create admin user
            admin = User(
                first_name=request.form.get('first_name'),
                last_name=request.form.get('last_name'),
                email=request.form.get('email'),
                phone=request.form.get('phone', ''),
                role='admin',
                is_active=True,
                email_verified=True
            )
            admin.set_password(request.form.get('password'))
            
            db.session.add(admin)
            db.session.commit()
            
            flash('تم إنشاء حساب المدير بنجاح!', 'success')
            return redirect(url_for('setup.academy_settings'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في إنشاء الحساب: {str(e)}', 'danger')
    
    return render_template('setup/admin_account.html')

@setup_bp.route('/academy-settings', methods=['GET', 'POST'])
@setup_required
def academy_settings():
    """Academy basic settings"""
    if request.method == 'POST':
        try:
            settings = AcademySettings.query.first()
            if not settings:
                settings = AcademySettings()
                db.session.add(settings)
            
            # Basic academy info
            settings.academy_name = request.form.get('academy_name')
            settings.academy_slogan = request.form.get('academy_slogan', '')
            settings.academy_description = request.form.get('academy_description', '')
            
            # Contact info
            settings.contact_email = request.form.get('contact_email', '')
            settings.contact_phone = request.form.get('contact_phone', '')
            settings.contact_address = request.form.get('contact_address', '')
            
            # Social media
            settings.facebook_url = request.form.get('facebook_url', '')
            settings.twitter_url = request.form.get('twitter_url', '')
            settings.instagram_url = request.form.get('instagram_url', '')
            settings.youtube_url = request.form.get('youtube_url', '')
            settings.whatsapp_number = request.form.get('whatsapp_number', '')
            
            # Colors and branding
            settings.primary_color = request.form.get('primary_color', '#007bff')
            settings.secondary_color = request.form.get('secondary_color', '#6c757d')
            settings.accent_color = request.form.get('accent_color', '#28a745')
            
            # Handle logo upload
            if 'academy_logo' in request.files:
                file = request.files['academy_logo']
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    file_extension = filename.rsplit('.', 1)[1].lower()
                    if file_extension in ['png', 'jpg', 'jpeg', 'gif', 'svg']:
                        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
                        upload_dir = 'static/uploads/academy'
                        os.makedirs(upload_dir, exist_ok=True)
                        file_path = os.path.join(upload_dir, unique_filename)
                        file.save(file_path)
                        settings.academy_logo = f"/static/uploads/academy/{unique_filename}"
            
            db.session.commit()
            flash('تم حفظ إعدادات الأكاديمية بنجاح!', 'success')
            
            # Check if user wants to skip remaining steps
            if request.form.get('skip_remaining'):
                return redirect(url_for('setup.complete'))
            
            return redirect(url_for('setup.payment_settings'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في حفظ الإعدادات: {str(e)}', 'danger')
    
    settings = AcademySettings.query.first()
    return render_template('setup/academy_settings.html', settings=settings)

@setup_bp.route('/payment-settings', methods=['GET', 'POST'])
@setup_required
def payment_settings():
    """Payment gateway settings"""
    if request.method == 'POST':
        try:
            # Handle skip
            if request.form.get('skip_step'):
                return redirect(url_for('setup.email_settings'))
            
            # Save payment settings
            # Stripe
            if request.form.get('enable_stripe'):
                stripe_gateway = PaymentGateway.query.filter_by(name='stripe').first()
                if not stripe_gateway:
                    stripe_gateway = PaymentGateway(name='stripe')
                    db.session.add(stripe_gateway)
                
                stripe_gateway.is_active = True
                stripe_gateway.public_key = request.form.get('stripe_public_key', '')
                stripe_gateway.secret_key = request.form.get('stripe_secret_key', '')
                stripe_gateway.webhook_secret = request.form.get('stripe_webhook_secret', '')
            
            # PayPal
            if request.form.get('enable_paypal'):
                paypal_gateway = PaymentGateway.query.filter_by(name='paypal').first()
                if not paypal_gateway:
                    paypal_gateway = PaymentGateway(name='paypal')
                    db.session.add(paypal_gateway)
                
                paypal_gateway.is_active = True
                paypal_gateway.client_id = request.form.get('paypal_client_id', '')
                paypal_gateway.client_secret = request.form.get('paypal_client_secret', '')
                paypal_gateway.environment = request.form.get('paypal_environment', 'sandbox')
            
            db.session.commit()
            flash('تم حفظ إعدادات الدفع بنجاح!', 'success')
            return redirect(url_for('setup.email_settings'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في حفظ إعدادات الدفع: {str(e)}', 'danger')
    
    stripe_gateway = PaymentGateway.query.filter_by(name='stripe').first()
    paypal_gateway = PaymentGateway.query.filter_by(name='paypal').first()
    
    return render_template('setup/payment_settings.html', 
                         stripe_gateway=stripe_gateway, 
                         paypal_gateway=paypal_gateway)

@setup_bp.route('/email-settings', methods=['GET', 'POST'])
@setup_required
def email_settings():
    """Email SMTP settings"""
    if request.method == 'POST':
        try:
            # Handle skip
            if request.form.get('skip_step'):
                return redirect(url_for('setup.security_settings'))
            
            # Save email settings
            email_settings = EmailSettings.query.first()
            if not email_settings:
                email_settings = EmailSettings()
                db.session.add(email_settings)
            
            email_settings.smtp_server = request.form.get('smtp_server', '')
            email_settings.smtp_port = int(request.form.get('smtp_port', 587))
            email_settings.smtp_username = request.form.get('smtp_username', '')
            email_settings.smtp_password = request.form.get('smtp_password', '')
            email_settings.use_tls = request.form.get('use_tls') == 'on'
            email_settings.use_ssl = request.form.get('use_ssl') == 'on'
            email_settings.sender_name = request.form.get('sender_name', '')
            email_settings.sender_email = request.form.get('sender_email', '')
            
            db.session.commit()
            flash('تم حفظ إعدادات البريد الإلكتروني بنجاح!', 'success')
            return redirect(url_for('setup.security_settings'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في حفظ إعدادات البريد: {str(e)}', 'danger')
    
    email_settings = EmailSettings.query.first()
    return render_template('setup/email_settings.html', email_settings=email_settings)

@setup_bp.route('/security-settings', methods=['GET', 'POST'])
@setup_required
def security_settings():
    """Security and advanced settings"""
    if request.method == 'POST':
        try:
            # Handle skip
            if request.form.get('skip_step'):
                return redirect(url_for('setup.complete'))
            
            # Save security settings to academy settings
            settings = AcademySettings.query.first()
            if not settings:
                settings = AcademySettings()
                db.session.add(settings)
            
            # Security settings (you can expand this based on your security model)
            settings.require_email_verification = request.form.get('require_email_verification') == 'on'
            settings.session_timeout = int(request.form.get('session_timeout', 30))
            settings.max_login_attempts = int(request.form.get('max_login_attempts', 5))
            settings.password_min_length = int(request.form.get('password_min_length', 8))
            
            db.session.commit()
            flash('تم حفظ إعدادات الأمان بنجاح!', 'success')
            return redirect(url_for('setup.complete'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في حفظ إعدادات الأمان: {str(e)}', 'danger')
    
    settings = AcademySettings.query.first()
    return render_template('setup/security_settings.html', settings=settings)

@setup_bp.route('/complete')
@setup_required
def complete():
    """Setup completion page"""
    # Mark setup as completed by ensuring all required data exists
    admin_exists = User.query.filter_by(role='admin').first() is not None
    academy_settings = AcademySettings.query.first()
    
    if admin_exists and academy_settings and academy_settings.academy_name:
        # Setup is complete, redirect to login or dashboard
        return render_template('setup/complete.html')
    else:
        flash('يرجى إكمال جميع الخطوات المطلوبة.', 'warning')
        return redirect(url_for('setup.admin_account'))

@setup_bp.route('/skip-all')
@setup_required
def skip_all():
    """Skip all setup steps and create minimal configuration"""
    try:
        # Create default admin if doesn't exist
        admin = User.query.filter_by(role='admin').first()
        if not admin:
            admin = User(
                first_name='مدير',
                last_name='النظام',
                email='<EMAIL>',
                role='admin',
                is_active=True,
                email_verified=True
            )
            admin.set_password('admin123')
            db.session.add(admin)
        
        # Create default academy settings
        settings = AcademySettings.query.first()
        if not settings:
            settings = AcademySettings(
                academy_name='أكاديمية القرآن الكريم',
                academy_slogan='تعلم القرآن الكريم واللغة العربية أونلاين',
                primary_color='#007bff',
                secondary_color='#6c757d',
                accent_color='#28a745'
            )
            db.session.add(settings)
        
        db.session.commit()
        flash('تم إنشاء إعداد افتراضي للنظام. يمكنك تخصيصه لاحقاً من لوحة التحكم.', 'info')
        return redirect(url_for('index'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في إنشاء الإعداد الافتراضي: {str(e)}', 'danger')
        return redirect(url_for('setup.welcome'))
