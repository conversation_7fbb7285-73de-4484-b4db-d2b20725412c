from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_required, current_user
from models import User, AcademySettings, PaymentGateway, EmailSettings, db
from werkzeug.utils import secure_filename
import os
import uuid

setup_bp = Blueprint('setup', __name__)

def is_setup_completed():
    """Check if initial setup is completed"""
    try:
        import os

        # PRODUCTION FIX: Check for setup completion marker file
        # This ensures fresh deployments always show setup wizard
        setup_marker_file = 'instance/setup_completed.marker'

        # If we're in production (Render), check for the marker file
        is_production = os.getenv('RENDER') or os.getenv('FLASK_ENV') == 'production'

        if is_production:
            # In production, only consider setup complete if marker file exists
            if not os.path.exists(setup_marker_file):
                print("🔄 Production: No setup marker file found - forcing setup wizard")
                return False
            else:
                print("✅ Production: Setup marker file exists - setup completed")
                return True

        # For development/local, use the old logic
        # Check for force setup reset environment variable
        force_setup = os.getenv('FORCE_SETUP_RESET', 'false').lower() == 'true'
        if force_setup:
            print("🔄 FORCE_SETUP_RESET enabled - forcing setup wizard")
            return False

        # Check if admin user exists
        admin_user = User.query.filter_by(role='admin').first()
        admin_exists = admin_user is not None

        # Check if academy settings exist with required fields
        academy_settings = AcademySettings.query.first()
        settings_configured = (academy_settings and
                             academy_settings.academy_name and
                             len(academy_settings.academy_name.strip()) > 0)

        # Both conditions must be true for complete setup
        setup_completed = admin_exists and settings_configured

        print(f"🔍 Setup Check: Admin={admin_exists}, Settings={settings_configured}, Completed={setup_completed}")

        # Additional debug info
        if admin_exists and admin_user:
            print(f"   Admin email: {admin_user.email}")
        if academy_settings:
            print(f"   Academy name: '{academy_settings.academy_name}'")

        return setup_completed

    except Exception as e:
        print(f"❌ Setup check error: {str(e)}")
        # If there's an error (like tables don't exist), setup is not completed
        return False

def setup_required(f):
    """Decorator to ensure setup is not completed and provide security"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First check: If setup is completed, redirect to main page
        if is_setup_completed():
            flash('تم إكمال إعداد النظام مسبقاً. يمكنك تسجيل الدخول الآن.', 'info')
            return redirect(url_for('auth.login'))

        # Second check: Security - if there are existing users but no admin,
        # this might be a security issue
        try:
            total_users = User.query.count()
            admin_users = User.query.filter_by(role='admin').count()

            # If there are users but no admin, something is wrong
            if total_users > 0 and admin_users == 0:
                print(f"⚠️ Security Warning: {total_users} users exist but no admin found")
                # Allow setup to continue to create admin

            # If there are multiple admins, setup should not be accessible
            elif admin_users > 1:
                print(f"⚠️ Security Warning: Multiple admins ({admin_users}) found during setup")
                flash('النظام محمي. يرجى تسجيل الدخول.', 'warning')
                return redirect(url_for('auth.login'))

        except Exception as e:
            print(f"⚠️ Setup security check error: {str(e)}")
            # Continue with setup if there's a database error

        return f(*args, **kwargs)
    return decorated_function

@setup_bp.route('/')
@setup_required
def welcome():
    """Welcome page - Introduction to Marketation LMS"""
    return render_template('setup/welcome.html')

@setup_bp.route('/about')
@setup_required
def about():
    """About Marketation and the system"""
    return render_template('setup/about.html')

@setup_bp.route('/features')
@setup_required
def features():
    """System features overview"""
    return render_template('setup/features.html')

@setup_bp.route('/developer')
@setup_required
def developer():
    """Developer and company information"""
    return render_template('setup/developer.html')

@setup_bp.route('/admin-account', methods=['GET', 'POST'])
@setup_required
def admin_account():
    """Create admin account"""
    if request.method == 'POST':
        try:
            # Check if admin already exists
            existing_admin = User.query.filter_by(role='admin').first()
            if existing_admin:
                # Check if user wants to replace existing admin
                if request.form.get('replace_existing') == 'true':
                    print(f"🔄 Replacing existing admin: {existing_admin.email}")
                    db.session.delete(existing_admin)
                    db.session.flush()  # Ensure deletion before creating new one
                else:
                    # Show template with existing admin options (no flash message)
                    return render_template('setup/admin_account.html', existing_admin=existing_admin)
            
            # Create admin user
            admin = User(
                first_name=request.form.get('first_name'),
                last_name=request.form.get('last_name'),
                email=request.form.get('email'),
                phone=request.form.get('phone', ''),
                role='admin',
                status='approved'
            )
            admin.set_password(request.form.get('password'))

            db.session.add(admin)
            db.session.commit()

            print(f"✅ Admin created successfully:")
            print(f"   Email: {admin.email}")
            print(f"   Name: {admin.first_name} {admin.last_name}")
            print(f"   Status: {admin.status}")
            print(f"   Password set: True")

            flash('تم إنشاء حساب المدير بنجاح!', 'success')
            return redirect(url_for('setup.academy_settings'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في إنشاء الحساب: {str(e)}', 'danger')
    
    return render_template('setup/admin_account.html')

@setup_bp.route('/academy-settings', methods=['GET', 'POST'])
@setup_required
def academy_settings():
    """Academy basic settings"""
    if request.method == 'POST':
        try:
            settings = AcademySettings.query.first()
            if not settings:
                settings = AcademySettings()
                db.session.add(settings)
            
            # Basic academy info
            settings.academy_name = request.form.get('academy_name')
            settings.academy_slogan = request.form.get('academy_slogan', '')
            settings.academy_description = request.form.get('academy_description', '')
            
            # Contact info
            settings.contact_email = request.form.get('contact_email', '')
            settings.contact_phone = request.form.get('contact_phone', '')
            settings.address = request.form.get('contact_address', '')
            
            # Social media
            settings.facebook_url = request.form.get('facebook_url', '')
            settings.twitter_url = request.form.get('twitter_url', '')
            settings.instagram_url = request.form.get('instagram_url', '')
            settings.youtube_url = request.form.get('youtube_url', '')
            settings.contact_whatsapp = request.form.get('whatsapp_number', '')
            
            # Colors and branding
            settings.primary_color = request.form.get('primary_color', '#007bff')
            settings.secondary_color = request.form.get('secondary_color', '#6c757d')
            
            # Handle logo upload
            if 'academy_logo' in request.files:
                file = request.files['academy_logo']
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    # Safe file extension extraction
                    if '.' in filename:
                        file_extension = filename.rsplit('.', 1)[1].lower()
                        if file_extension in ['png', 'jpg', 'jpeg', 'gif', 'svg']:
                            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
                            upload_dir = 'static/uploads/academy'
                            os.makedirs(upload_dir, exist_ok=True)
                            file_path = os.path.join(upload_dir, unique_filename)
                            file.save(file_path)
                            settings.academy_logo = f"/static/uploads/academy/{unique_filename}"
                        else:
                            flash('نوع الملف غير مدعوم. يرجى رفع صورة بصيغة PNG, JPG, JPEG, GIF, أو SVG.', 'warning')
                    else:
                        flash('اسم الملف غير صحيح. يرجى رفع ملف بامتداد صحيح.', 'warning')
            
            db.session.commit()
            flash('تم حفظ إعدادات الأكاديمية بنجاح!', 'success')
            
            # Check if user wants to skip remaining steps
            if request.form.get('skip_remaining'):
                return redirect(url_for('setup.complete'))
            
            return redirect(url_for('setup.payment_settings'))
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Academy settings error: {str(e)}")
            import traceback
            traceback.print_exc()
            flash(f'حدث خطأ في حفظ الإعدادات: {str(e)}', 'danger')
    
    settings = AcademySettings.query.first()
    return render_template('setup/academy_settings.html', settings=settings)

@setup_bp.route('/payment-settings', methods=['GET', 'POST'])
@setup_required
def payment_settings():
    """Payment gateway settings"""
    if request.method == 'POST':
        try:
            # Handle skip
            if request.form.get('skip_step'):
                return redirect(url_for('setup.email_settings'))
            
            # Save payment settings
            # Stripe
            if request.form.get('enable_stripe'):
                stripe_gateway = PaymentGateway.query.filter_by(name='stripe').first()
                if not stripe_gateway:
                    stripe_gateway = PaymentGateway(name='stripe')
                    db.session.add(stripe_gateway)
                
                stripe_gateway.is_active = True
                stripe_gateway.public_key = request.form.get('stripe_public_key', '')
                stripe_gateway.secret_key = request.form.get('stripe_secret_key', '')
                stripe_gateway.webhook_secret = request.form.get('stripe_webhook_secret', '')
            
            # PayPal
            if request.form.get('enable_paypal'):
                paypal_gateway = PaymentGateway.query.filter_by(name='paypal').first()
                if not paypal_gateway:
                    paypal_gateway = PaymentGateway(name='paypal')
                    db.session.add(paypal_gateway)
                
                paypal_gateway.is_active = True
                paypal_gateway.client_id = request.form.get('paypal_client_id', '')
                paypal_gateway.client_secret = request.form.get('paypal_client_secret', '')
                paypal_gateway.environment = request.form.get('paypal_environment', 'sandbox')
            
            db.session.commit()
            flash('تم حفظ إعدادات الدفع بنجاح!', 'success')
            return redirect(url_for('setup.email_settings'))
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Payment settings error: {str(e)}")
            import traceback
            traceback.print_exc()
            flash(f'حدث خطأ في حفظ إعدادات الدفع: {str(e)}', 'danger')
    
    stripe_gateway = PaymentGateway.query.filter_by(name='stripe').first()
    paypal_gateway = PaymentGateway.query.filter_by(name='paypal').first()
    
    return render_template('setup/payment_settings.html', 
                         stripe_gateway=stripe_gateway, 
                         paypal_gateway=paypal_gateway)

@setup_bp.route('/email-settings', methods=['GET', 'POST'])
@setup_required
def email_settings():
    """Email SMTP settings"""
    if request.method == 'POST':
        try:
            # Handle skip
            if request.form.get('skip_step'):
                return redirect(url_for('setup.security_settings'))
            
            # Save email settings
            email_settings = EmailSettings.query.first()
            if not email_settings:
                email_settings = EmailSettings()
                db.session.add(email_settings)
            
            email_settings.smtp_server = request.form.get('smtp_server', '')
            email_settings.smtp_port = int(request.form.get('smtp_port', 587))
            email_settings.smtp_username = request.form.get('smtp_username', '')
            email_settings.smtp_password = request.form.get('smtp_password', '')
            email_settings.use_tls = request.form.get('use_tls') == 'on'
            email_settings.use_ssl = request.form.get('use_ssl') == 'on'
            email_settings.sender_name = request.form.get('sender_name', '')
            email_settings.sender_email = request.form.get('sender_email', '')
            
            db.session.commit()
            flash('تم حفظ إعدادات البريد الإلكتروني بنجاح!', 'success')
            return redirect(url_for('setup.security_settings'))
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Email settings error: {str(e)}")
            import traceback
            traceback.print_exc()
            flash(f'حدث خطأ في حفظ إعدادات البريد: {str(e)}', 'danger')
    
    email_settings = EmailSettings.query.first()
    return render_template('setup/email_settings.html', email_settings=email_settings)

@setup_bp.route('/security-settings', methods=['GET', 'POST'])
@setup_required
def security_settings():
    """Security and advanced settings"""
    if request.method == 'POST':
        try:
            # Handle skip
            if request.form.get('skip_step'):
                return redirect(url_for('setup.complete'))
            
            # Save security settings to academy settings
            settings = AcademySettings.query.first()
            if not settings:
                settings = AcademySettings()
                db.session.add(settings)
            
            # Security settings can be added later through admin panel
            # For now, just save basic settings
            
            db.session.commit()
            flash('تم حفظ إعدادات الأمان بنجاح!', 'success')
            return redirect(url_for('setup.complete'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في حفظ إعدادات الأمان: {str(e)}', 'danger')
    
    settings = AcademySettings.query.first()
    return render_template('setup/security_settings.html', settings=settings)

@setup_bp.route('/complete')
@setup_required
def complete():
    """Setup completion page"""
    try:
        # Verify setup is actually completed
        admin_exists = User.query.filter_by(role='admin').first() is not None
        academy_settings = AcademySettings.query.first()
        settings_valid = (academy_settings and
                         academy_settings.academy_name and
                         len(academy_settings.academy_name.strip()) > 0)

        if admin_exists and settings_valid:
            print("✅ Setup completed successfully!")
            # Setup is complete, show completion page
            return render_template('setup/complete.html')
        else:
            print(f"❌ Setup incomplete - Admin: {admin_exists}, Settings: {settings_valid}")
            flash('يرجى إكمال جميع الخطوات المطلوبة.', 'warning')
            return redirect(url_for('setup.admin_account'))

    except Exception as e:
        print(f"❌ Setup completion check error: {str(e)}")
        flash('حدث خطأ في التحقق من الإعداد.', 'danger')
        return redirect(url_for('setup.admin_account'))

@setup_bp.route('/skip-all')
@setup_required
def skip_all():
    """Skip all setup steps and create minimal configuration"""
    try:
        # Delete any existing admin to ensure clean state
        existing_admins = User.query.filter_by(role='admin').all()
        for existing_admin in existing_admins:
            print(f"🗑️ Deleting existing admin: {existing_admin.email}")
            db.session.delete(existing_admin)

        # Create default admin
        admin = User(
            first_name='مدير',
            last_name='النظام',
            email='<EMAIL>',  # البريد الافتراضي الموحد
            role='admin',
            status='approved'
        )
        admin.set_password('admin123')
        db.session.add(admin)

        print(f"✅ Default admin created:")
        print(f"   Email: {admin.email}")
        print(f"   Password: admin123")
            admin = User(
                first_name='مدير',
                last_name='النظام',
                email='<EMAIL>',  # البريد الافتراضي الموحد
                role='admin',
                status='approved'
            )
            admin.set_password('admin123')
            db.session.add(admin)
        
        # Create default academy settings
        settings = AcademySettings.query.first()
        if not settings:
            settings = AcademySettings(
                academy_name='أكاديمية القرآن الكريم',
                academy_slogan='تعلم القرآن الكريم واللغة العربية أونلاين',
                primary_color='#007bff',
                secondary_color='#6c757d',
                success_color='#28a745'
            )
            db.session.add(settings)
        
        db.session.commit()

        # Create setup completion marker file for production
        import os
        from datetime import datetime
        setup_marker_file = 'instance/setup_completed.marker'
        try:
            os.makedirs('instance', exist_ok=True)
            with open(setup_marker_file, 'w') as f:
                f.write(f'Setup completed at: {datetime.now().isoformat()}\n')
                f.write(f'Admin email: {admin.email}\n')
                f.write(f'Academy name: {settings.academy_name}\n')
                f.write('Setup method: skip_all\n')
            print(f"✅ Created setup completion marker: {setup_marker_file}")
        except Exception as e:
            print(f"⚠️ Could not create setup marker: {str(e)}")

        print("✅ Default setup completed successfully")

        # Show auto-complete page with countdown
        return render_template('setup/auto_complete.html',
                             admin_email='<EMAIL>',  # البريد الافتراضي الموحد
                             admin_password='admin123',
                             academy_name=settings.academy_name)
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في إنشاء الإعداد الافتراضي: {str(e)}', 'danger')
        return redirect(url_for('setup.welcome'))

@setup_bp.route('/finish')
def finish():
    """Finish setup and redirect to login"""
    # Manual check for setup completion (bypass marker file check)
    try:
        admin_exists = User.query.filter_by(role='admin').first() is not None
        academy_settings = AcademySettings.query.first()
        settings_valid = (academy_settings and
                         academy_settings.academy_name and
                         len(academy_settings.academy_name.strip()) > 0)

        if admin_exists and settings_valid:
            print("✅ Setup verification passed - creating completion marker")

            # Create setup completion marker file for production
            import os
            from datetime import datetime
            setup_marker_file = 'instance/setup_completed.marker'
            try:
                # Ensure instance directory exists
                os.makedirs('instance', exist_ok=True)
                # Create marker file
                with open(setup_marker_file, 'w') as f:
                    f.write(f'Setup completed at: {datetime.now().isoformat()}\n')
                    f.write(f'Admin email: {User.query.filter_by(role="admin").first().email}\n')
                    f.write(f'Academy name: {academy_settings.academy_name}\n')
                print(f"✅ Created setup completion marker: {setup_marker_file}")
            except Exception as e:
                print(f"⚠️ Could not create setup marker: {str(e)}")

            flash('تم إكمال إعداد النظام بنجاح! يمكنك الآن تسجيل الدخول.', 'success')
            return redirect(url_for('auth.login'))
        else:
            print(f"❌ Setup verification failed - Admin: {admin_exists}, Settings: {settings_valid}")
            flash('لم يتم إكمال الإعداد بعد. يرجى التأكد من إنشاء حساب المدير وإعدادات الأكاديمية.', 'warning')
            return redirect(url_for('setup.admin_account'))

    except Exception as e:
        print(f"❌ Error in finish route: {str(e)}")
        flash(f'حدث خطأ في إنهاء الإعداد: {str(e)}', 'danger')
        return redirect(url_for('setup.welcome'))

@setup_bp.route('/reset')
def reset():
    """Show setup reset page"""
    return render_template('setup/reset.html')

@setup_bp.route('/reset-setup-force')
def reset_setup_force():
    """Force reset setup - for debugging production issues"""
    try:
        # Delete setup completion marker file
        import os
        setup_marker_file = 'instance/setup_completed.marker'
        if os.path.exists(setup_marker_file):
            os.remove(setup_marker_file)
            print("🔄 Removed setup completion marker file")

        # Delete all admin users
        admin_users = User.query.filter_by(role='admin').all()
        for admin in admin_users:
            db.session.delete(admin)

        # Delete academy settings
        academy_settings = AcademySettings.query.all()
        for setting in academy_settings:
            db.session.delete(setting)

        db.session.commit()

        print("🔄 Setup forcefully reset - all admin users and academy settings deleted")
        flash('تم إعادة تعيين النظام بنجاح. يمكنك الآن إعداد النظام من جديد.', 'success')
        return redirect(url_for('setup.welcome'))

    except Exception as e:
        db.session.rollback()
        print(f"❌ Error resetting setup: {str(e)}")
        flash(f'حدث خطأ في إعادة تعيين النظام: {str(e)}', 'danger')
        return redirect(url_for('setup.welcome'))
