<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - أكاديمية القرآن الكريم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome - Latest Version -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Bootstrap Icons as Fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        /* Tajawal Font Application */
        * {
            font-family: 'Tajawal', sans-serif !important;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            margin: 0 auto;
        }
        
        .register-form {
            padding: 3rem;
        }
        
        .register-image {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            text-align: center;
            padding: 3rem;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        
        .form-floating label {
            color: #6c757d;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        @media (max-width: 768px) {
            .register-form {
                padding: 2rem;
            }
            
            .register-image {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <div class="row g-0">
                <!-- Register Form -->
                <div class="col-lg-8">
                    <div class="register-form">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">إنشاء حساب جديد</h2>
                            <p class="text-muted">انضم إلى أكاديمية القرآن الكريم</p>
                        </div>
                        
                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST" id="registerForm">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="first_name" name="first_name" placeholder="الاسم الأول" required>
                                        <label for="first_name"><i class="fas fa-user me-2"></i>الاسم الأول</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="last_name" name="last_name" placeholder="اسم العائلة" required>
                                        <label for="last_name"><i class="fas fa-user me-2"></i>اسم العائلة</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="email" name="email" placeholder="البريد الإلكتروني" required>
                                <label for="email"><i class="fas fa-envelope me-2"></i>البريد الإلكتروني</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="رقم الهاتف">
                                <label for="phone"><i class="fas fa-phone me-2"></i>رقم الهاتف</label>
                            </div>
                            
                            <div class="mb-3">
                                <label for="role" class="form-label"><i class="fas fa-user-tag me-2"></i>نوع الحساب</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="student" selected>طالب</option>
                                    <option value="teacher">معلم</option>
                                </select>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required minlength="6">
                                        <label for="password"><i class="fas fa-lock me-2"></i>كلمة المرور</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="تأكيد كلمة المرور" required>
                                        <label for="confirm_password"><i class="fas fa-lock me-2"></i>تأكيد كلمة المرور</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على <a href="#" class="text-decoration-none">الشروط والأحكام</a>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                            </button>
                            
                            <div class="text-center">
                                <p class="mb-0">لديك حساب بالفعل؟</p>
                                <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Register Image -->
                <div class="col-lg-4 d-none d-lg-block">
                    <div class="register-image">
                        <i class="fas fa-quran-book fa-4x mb-3"></i>
                        <h4 class="fw-bold mb-3">ابدأ رحلتك</h4>
                        <p class="lead">مع أفضل المعلمين المؤهلين</p>
                        
                        <div class="mt-4">
                            <div class="mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>تعلم مع معلمين مؤهلين</span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>جدولة مرنة تناسبك</span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>تتبع تقدمك بسهولة</span>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>باقات متنوعة ومناسبة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const terms = document.getElementById('terms').checked;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمات المرور غير متطابقة');
                return;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }
            
            if (!terms) {
                e.preventDefault();
                alert('يجب الموافقة على الشروط والأحكام');
                return;
            }
        });
        
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
