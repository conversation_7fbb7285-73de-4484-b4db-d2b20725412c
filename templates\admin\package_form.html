{% extends "base.html" %}

{% block title %}{{ 'تعديل الباقة' if package else 'إضافة باقة جديدة' }} - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}{{ 'تعديل الباقة' if package else 'إضافة باقة جديدة' }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-{{ 'edit' if package else 'plus' }} me-2"></i>
                    {{ 'تعديل الباقة' if package else 'إضافة باقة جديدة' }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="packageForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم الباقة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ package.name if package else '' }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">السعر (USD) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           value="{{ package.price if package else '' }}" 
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="duration_days" class="form-label">مدة الباقة (بالأيام) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="duration_days" name="duration_days"
                                       value="{{ package.duration_days if package else '' }}"
                                       min="1" required>
                                <div class="form-text">مدة صلاحية الباقة بالأيام</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sessions_count" class="form-label">عدد الحصص <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="sessions_count" name="sessions_count"
                                       value="{{ package.sessions_count if package else '' }}"
                                       min="1" required>
                                <div class="form-text">عدد الحصص المتاحة في الباقة</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="session_duration" class="form-label">مدة الحصة (بالدقائق) <span class="text-danger">*</span></label>
                                <select class="form-select" id="session_duration" name="session_duration" required>
                                    <option value="">اختر مدة الحصة</option>
                                    <option value="30" {{ 'selected' if package and package.session_duration == 30 else '' }}>30 دقيقة</option>
                                    <option value="45" {{ 'selected' if package and package.session_duration == 45 else '' }}>45 دقيقة</option>
                                    <option value="60" {{ 'selected' if package and package.session_duration == 60 or (package and not package.session_duration) else '' }}>60 دقيقة</option>
                                    <option value="90" {{ 'selected' if package and package.session_duration == 90 else '' }}>90 دقيقة</option>
                                </select>
                                <div class="form-text">مدة كل حصة في الباقة</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الباقة</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="وصف مختصر للباقة ومميزاتها">{{ package.description if package else '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="features" class="form-label">مميزات الباقة</label>
                        <textarea class="form-control" id="features" name="features" rows="5" 
                                  placeholder="اكتب كل ميزة في سطر منفصل">{{ package.features if package else '' }}</textarea>
                        <div class="form-text">اكتب كل ميزة في سطر منفصل</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {{ 'checked' if not package or package.is_active else '' }}>
                            <label class="form-check-label" for="is_active">
                                باقة نشطة (متاحة للشراء)
                            </label>
                        </div>
                    </div>
                    
                    <!-- Preview Section -->
                    <div class="mb-4">
                        <h6>معاينة الباقة</h6>
                        <div class="card border-primary" id="packagePreview">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0" id="previewName">اسم الباقة</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="h2 text-primary" id="previewPrice">$0.00</div>
                                    <div class="text-muted" id="previewDuration">0 يوم</div>
                                </div>
                                <p class="card-text" id="previewDescription">وصف الباقة</p>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span><i class="fas fa-calendar-alt me-2"></i>عدد الحصص:</span>
                                        <span class="fw-bold" id="previewSessions">0</span>
                                    </div>
                                </div>
                                <div id="previewFeatures"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>{{ 'تحديث الباقة' if package else 'إنشاء الباقة' }}
                        </button>
                        <a href="{{ url_for('admin.packages') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Live preview functionality
    function updatePreview() {
        const name = document.getElementById('name').value || 'اسم الباقة';
        const price = document.getElementById('price').value || '0.00';
        const duration = document.getElementById('duration_days').value || '0';
        const sessions = document.getElementById('sessions_count').value || '0';
        const description = document.getElementById('description').value || 'وصف الباقة';
        const features = document.getElementById('features').value;
        
        document.getElementById('previewName').textContent = name;
        document.getElementById('previewPrice').textContent = `$${parseFloat(price).toFixed(2)}`;
        document.getElementById('previewDuration').textContent = `${duration} يوم`;
        document.getElementById('previewSessions').textContent = sessions;
        document.getElementById('previewDescription').textContent = description;
        
        // Update features
        const featuresContainer = document.getElementById('previewFeatures');
        if (features.trim()) {
            const featuresList = features.split('\n').filter(f => f.trim());
            let featuresHtml = '<h6>المميزات:</h6><div class="small">';
            featuresList.forEach(feature => {
                if (feature.trim()) {
                    featuresHtml += `<div class="mb-1"><i class="fas fa-check text-success me-2"></i>${feature.trim()}</div>`;
                }
            });
            featuresHtml += '</div>';
            featuresContainer.innerHTML = featuresHtml;
        } else {
            featuresContainer.innerHTML = '';
        }
    }
    
    // Add event listeners for live preview
    document.querySelectorAll('#name, #price, #duration_days, #sessions_count, #description, #features').forEach(input => {
        input.addEventListener('input', updatePreview);
    });
    
    // Initial preview update
    updatePreview();
    
    // Form validation
    document.getElementById('packageForm').addEventListener('submit', function(e) {
        const price = parseFloat(document.getElementById('price').value);
        const duration = parseInt(document.getElementById('duration_days').value);
        const sessions = parseInt(document.getElementById('sessions_count').value);
        
        if (price <= 0) {
            e.preventDefault();
            alert('السعر يجب أن يكون أكبر من صفر');
            return;
        }
        
        if (duration <= 0) {
            e.preventDefault();
            alert('مدة الباقة يجب أن تكون أكبر من صفر');
            return;
        }
        
        if (sessions <= 0) {
            e.preventDefault();
            alert('عدد الحصص يجب أن يكون أكبر من صفر');
            return;
        }
    });
    
    // Calculate price per session
    function calculatePricePerSession() {
        const price = parseFloat(document.getElementById('price').value) || 0;
        const sessions = parseInt(document.getElementById('sessions_count').value) || 1;
        const pricePerSession = price / sessions;
        
        if (price > 0 && sessions > 0) {
            const info = document.createElement('div');
            info.className = 'form-text text-info';
            info.innerHTML = `<i class="fas fa-info-circle me-1"></i>سعر الحصة الواحدة: $${pricePerSession.toFixed(2)}`;
            
            // Remove existing info
            const existing = document.querySelector('.price-per-session-info');
            if (existing) existing.remove();
            
            info.classList.add('price-per-session-info');
            document.getElementById('sessions_count').parentNode.appendChild(info);
        }
    }
    
    document.getElementById('price').addEventListener('input', calculatePricePerSession);
    document.getElementById('sessions_count').addEventListener('input', calculatePricePerSession);
    
    // Initial calculation
    calculatePricePerSession();
</script>
{% endblock %}
