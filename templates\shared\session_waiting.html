<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الانتقال للحصة - {{ session.teacher.full_name if user_type == 'student' else session.student.full_name }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .waiting-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }
        
        .waiting-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
        }
        
        .countdown-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745, #20c997);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 2.5rem;
            font-weight: bold;
            position: relative;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .session-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            border-right: 5px solid #28a745;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
        }
        
        .info-label i {
            margin-left: 8px;
            color: #28a745;
        }
        
        .info-value {
            font-weight: 500;
            color: #212529;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: 600;
            margin: 20px 0;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .meeting-link {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .progress-bar-container {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
            transition: width 1s linear;
        }
        
        .footer-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 30px;
        }
        
        .manual-link {
            margin-top: 20px;
            padding: 15px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="waiting-container">
        <!-- العداد التنازلي -->
        <div class="countdown-circle">
            <span id="countdown">5</span>
        </div>
        
        <!-- العنوان الرئيسي -->
        <h2 class="mb-3">
            <i class="fas fa-check-circle text-success me-2"></i>
            تم تسجيل حضورك بنجاح!
        </h2>
        
        <p class="lead text-muted mb-4">
            سيتم توجيهك للحصة خلال <span id="countdown-text">5</span> ثواني...
        </p>
        
        <!-- شريط التقدم -->
        <div class="progress-bar-container">
            <div class="progress-bar" id="progress-bar" style="width: 100%;"></div>
        </div>
        
        <!-- معلومات الحصة -->
        <div class="session-info">
            <h5 class="mb-3">
                <i class="fas fa-info-circle text-primary me-2"></i>
                تفاصيل الحصة
            </h5>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-user"></i>
                    {% if user_type == 'student' %}المعلم{% else %}الطالب{% endif %}
                </span>
                <span class="info-value">
                    {{ session.teacher.full_name if user_type == 'student' else session.student.full_name }}
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-calendar"></i>
                    التاريخ
                </span>
                <span class="info-value">
                    {{ session.scheduled_datetime.strftime('%Y-%m-%d') }}
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-clock"></i>
                    الوقت
                </span>
                <span class="info-value">
                    {{ session.scheduled_datetime.strftime('%H:%M') }}
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-hourglass-half"></i>
                    المدة
                </span>
                <span class="info-value">
                    {{ session.duration_minutes }} دقيقة
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-video"></i>
                    منصة الاجتماع
                </span>
                <span class="info-value">
                    {{ session.get_meeting_provider_name() }}
                </span>
            </div>
        </div>
        
        <!-- حالة الحضور -->
        <div class="status-badge status-success">
            <i class="fas fa-check-circle me-2"></i>
            تم تأكيد حضورك للحصة
        </div>
        
        <!-- رابط الحصة اليدوي -->
        {% if session.meeting_link %}
        <div class="manual-link">
            <p class="mb-2">
                <i class="fas fa-info-circle text-warning me-2"></i>
                إذا لم يتم التوجيه تلقائياً، يمكنك النقر على الرابط أدناه:
            </p>
            <a href="{{ session.meeting_link }}" target="_blank" class="btn btn-primary btn-lg">
                <i class="fas fa-external-link-alt me-2"></i>
                دخول للحصة يدوياً
            </a>
        </div>
        {% endif %}
        
        <!-- نص تذييل -->
        <div class="footer-text">
            <i class="fas fa-shield-alt me-1"></i>
            نتمنى لك حصة مثمرة ومفيدة
        </div>
    </div>

    <!-- JavaScript للعداد التنازلي -->
    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        const countdownTextElement = document.getElementById('countdown-text');
        const progressBar = document.getElementById('progress-bar');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            countdownTextElement.textContent = countdown;
            
            // تحديث شريط التقدم
            const progress = (countdown / 5) * 100;
            progressBar.style.width = progress + '%';
            
            if (countdown <= 0) {
                clearInterval(timer);
                
                // توجيه للحصة
                {% if session.meeting_link %}
                window.open('{{ session.meeting_link }}', '_blank');
                {% endif %}
                
                // العودة لصفحة التفاصيل
                setTimeout(() => {
                    {% if user_type == 'student' %}
                    window.location.href = '{{ url_for("student.session_details", session_id=session.id) }}';
                    {% else %}
                    window.location.href = '{{ url_for("teacher.session_details", session_id=session.id) }}';
                    {% endif %}
                }, 1000);
            }
        }, 1000);
        
        // إضافة تأثير صوتي (اختياري)
        document.addEventListener('DOMContentLoaded', function() {
            // يمكن إضافة تأثير صوتي هنا إذا رغبت
        });
    </script>
</body>
</html>
