{% extends "base.html" %}

{% block title %}لوحة تحكم المعلم - {{ academy_name }}{% endblock %}
{% block page_title %}لوحة تحكم المعلم{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4 class="card-title mb-1">مرحباً، {{ current_user.full_name }}</h4>
                        <p class="card-text mb-0">إليك ملخص نشاطك التعليمي اليوم</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chalkboard-teacher fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-primary text-uppercase mb-1">إجمالي الحصص</div>
                        <div class="h5 mb-0">{{ stats.total_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">الحصص المكتملة</div>
                        <div class="h5 mb-0">{{ stats.completed_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">الحصص القادمة</div>
                        <div class="h5 mb-0">{{ stats.scheduled_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">عدد الطلاب</div>
                        <div class="h5 mb-0">{{ stats.students_count }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-graduate fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Session Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">حصص الاشتراكات</div>
                        <div class="h5 mb-0">{{ stats.subscription_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-check fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-secondary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-secondary text-uppercase mb-1">الحصص الملغية</div>
                        <div class="h5 mb-0">{{ stats.cancelled_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle fa-2x text-secondary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-purple border-4" style="border-color: #6f42c1 !important;">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-uppercase mb-1" style="color: #6f42c1;">الحصص التجريبية</div>
                        <div class="h5 mb-0">{{ stats.trial_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-flask fa-2x" style="color: #6f42c1;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-dark border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-dark text-uppercase mb-1">الحصص التعويضية</div>
                        <div class="h5 mb-0">{{ stats.makeup_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-redo fa-2x text-dark"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Sessions Card -->
{% if stats.subscription_sessions > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>حصص الاشتراكات المجدولة
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="text-center">
                            <div class="h3 text-warning mb-0">{{ stats.subscription_sessions }}</div>
                            <div class="small text-muted">حصة مجدولة</div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle text-warning me-2"></i>
                            <div>
                                <div class="fw-bold">حصص من اشتراكات الطلاب</div>
                                <div class="small text-muted">
                                    هذه الحصص تم جدولتها من قبل الإدارة من اشتراكات الطلاب في الباقات المختلفة
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('teacher.sessions') }}?type=subscription" class="btn btn-warning">
                            <i class="fas fa-eye me-1"></i>عرض الحصص
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Today's Sessions and Performance -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-day me-2"></i>حصص اليوم
                </h5>
                <span class="badge bg-primary">{{ today_sessions|length }} حصة</span>
            </div>
            <div class="card-body">
                {% if today_sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>الطالب</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in today_sessions %}
                                <tr>
                                    <td>{{ session.scheduled_datetime.strftime('%H:%M') }}</td>
                                    <td>{{ session.student.full_name }}</td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {% if session.session_type == 'trial' %}تجريبية
                                            {% elif session.session_type == 'makeup' %}تعويضية
                                            {% else %}مجدولة{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">مجدولة</span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('teacher.session_details', session_id=session.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد حصص مجدولة لليوم</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>تقييمي
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <div class="h2 text-warning">{{ stats.avg_rating }}</div>
                    <div class="text-muted">من 5 نجوم</div>
                </div>
                <div class="mb-3">
                    {% for i in range(1, 6) %}
                        {% if i <= stats.avg_rating %}
                            <i class="fas fa-star text-warning"></i>
                        {% else %}
                            <i class="far fa-star text-muted"></i>
                        {% endif %}
                    {% endfor %}
                </div>
                <p class="small text-muted">متوسط تقييم الطلاب</p>
            </div>
        </div>
    </div>
</div>

<!-- This Week's Sessions and Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-week me-2"></i>حصص هذا الأسبوع
                </h5>
                <a href="{{ url_for('teacher.sessions') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if week_sessions %}
                    {% for session in week_sessions[:5] %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">{{ session.student.full_name }}</div>
                            <div class="small text-muted">
                                {{ session.scheduled_datetime.strftime('%Y-%m-%d %H:%M') }}
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-info">{{ session.session_type }}</span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد حصص هذا الأسبوع</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>الحصص المكتملة مؤخراً
                </h5>
                <a href="{{ url_for('teacher.sessions') }}?status=completed" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_completed %}
                    {% for session in recent_completed %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold">{{ session.student.full_name }}</div>
                            <div class="small text-muted">
                                {{ session.completed_at.strftime('%Y-%m-%d %H:%M') }}
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <a href="{{ url_for('teacher.session_details', session_id=session.id) }}" 
                               class="btn btn-sm btn-outline-success">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد حصص مكتملة مؤخراً</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('teacher.sessions') }}" class="btn btn-primary w-100">
                            <i class="fas fa-calendar-alt me-2"></i>عرض جميع الحصص
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('teacher.students') }}" class="btn btn-success w-100">
                            <i class="fas fa-user-graduate me-2"></i>عرض الطلاب
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('teacher.reports') }}" class="btn btn-info w-100">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-warning w-100">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
    
    // Add hover effects to cards
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
{% endblock %}
