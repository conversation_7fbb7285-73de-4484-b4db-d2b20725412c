{% extends "base.html" %}

{% block title %}تم الدفع بنجاح - {{ academy_name }}{% endblock %}
{% block page_title %}تم الدفع بنجاح{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Success Message -->
        <div class="card border-success">
            <div class="card-header bg-success text-white text-center">
                <div class="display-1 mb-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="mb-0">تم الدفع بنجاح!</h3>
            </div>
            <div class="card-body text-center">
                <h5 class="text-success mb-3">شكراً لك على ثقتك بنا</h5>
                <p class="lead">تم استلام دفعتك بنجاح وسيتم مراجعة اشتراكك من قبل الإدارة.</p>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>الخطوات التالية:</strong>
                    <ul class="list-unstyled mt-2 mb-0">
                        <li>✅ تم استلام الدفع بنجاح</li>
                        <li>🔄 مراجعة الدفع من قبل الإدارة</li>
                        <li>📧 ستصلك إشعار فور الموافقة على الدفع</li>
                        <li>🎯 تفعيل الاشتراك فور موافقة الإدارة</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-clock me-2"></i>
                    <strong>مهم:</strong> اشتراكك في حالة انتظار موافقة الإدارة على الدفع.
                    سيتم تفعيل الاشتراك تلقائياً فور الموافقة وستصلك رسالة تأكيد.
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('student.subscriptions') }}" class="btn btn-primary w-100">
                            <i class="fas fa-list me-2"></i>عرض اشتراكاتي
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="{{ url_for('student.dashboard') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- What's Next -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>ماذا بعد؟
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <div class="display-6 text-primary mb-2">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <h6>مراجعة الطلب</h6>
                        <p class="text-muted small">سيقوم فريق الإدارة بمراجعة طلبك والموافقة عليه</p>
                    </div>
                    <div class="col-md-4 text-center mb-3">
                        <div class="display-6 text-success mb-2">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h6>إشعار التفعيل</h6>
                        <p class="text-muted small">ستصلك رسالة تأكيد عند تفعيل اشتراكك</p>
                    </div>
                    <div class="col-md-4 text-center mb-3">
                        <div class="display-6 text-warning mb-2">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h6>حجز الحصص</h6>
                        <p class="text-muted small">يمكنك البدء في حجز حصصك مع المعلمين</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support Info -->
        <div class="card mt-4 border-info">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <i class="fas fa-headset fa-3x text-info"></i>
                    </div>
                    <div class="col-md-10">
                        <h6 class="text-info">هل تحتاج مساعدة؟</h6>
                        <p class="text-muted small mb-0">
                            إذا كان لديك أي استفسار حول دفعتك أو اشتراكك، لا تتردد في التواصل معنا.
                            فريق الدعم في {{ academy_name }} متاح لمساعدتك.
                        </p>
                        {% if academy_email or academy_phone %}
                        <p class="text-muted small mb-0 mt-1">
                            <strong>طرق التواصل:</strong>
                            {% if academy_email %}📧 {{ academy_email }}{% endif %}
                            {% if academy_phone %}{% if academy_email %} | {% endif %}📞 {{ academy_phone }}{% endif %}
                        </p>
                        {% endif %}
                        <div class="mt-2">
                            {% if academy_email %}
                            <a href="mailto:{{ academy_email }}" class="btn btn-outline-info btn-sm me-2">
                                <i class="fas fa-envelope me-1"></i>راسلنا
                            </a>
                            {% endif %}
                            {% if academy_phone %}
                            <a href="tel:{{ academy_phone }}" class="btn btn-outline-info btn-sm me-2">
                                <i class="fas fa-phone me-1"></i>اتصل بنا
                            </a>
                            {% endif %}
                            {% if academy_whatsapp %}
                            <a href="https://wa.me/{{ academy_whatsapp.replace('+', '').replace(' ', '') }}" target="_blank" class="btn btn-outline-success btn-sm">
                                <i class="fab fa-whatsapp me-1"></i>واتساب
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh subscription status (optional)
    setTimeout(function() {
        // You can add code here to check subscription status
        // and redirect if approved
    }, 5000);
    
    // Confetti effect (optional)
    document.addEventListener('DOMContentLoaded', function() {
        // Simple celebration animation
        const successIcon = document.querySelector('.fa-check-circle');
        if (successIcon) {
            successIcon.style.animation = 'bounce 1s ease-in-out';
        }
    });
</script>

<style>
    @keyframes bounce {
        0%, 20%, 60%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-20px);
        }
        80% {
            transform: translateY(-10px);
        }
    }
    
    .card.border-success {
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
    }
</style>
{% endblock %}
