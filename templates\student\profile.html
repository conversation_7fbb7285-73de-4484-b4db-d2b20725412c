{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ super() }}{% endblock %}

{% block page_title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="row">
    <!-- Profile Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center bg-primary text-white" style="width: 80px; height: 80px; font-size: 32px;">
                    {{ current_user.full_name[0] if current_user.full_name else 'U' }}
                </div>
                <h5 class="card-title">{{ current_user.full_name }}</h5>
                <p class="text-muted">{{ current_user.email }}</p>
                <p class="text-muted">
                    <i class="fas fa-user-graduate me-2"></i>طالب
                </p>
                {% if current_user.phone %}
                <p class="text-muted">
                    <i class="fas fa-phone me-2"></i>{{ current_user.phone }}
                </p>
                {% endif %}
                <p class="text-muted">
                    <i class="fas fa-calendar me-2"></i>انضم في {{ current_user.created_at.strftime('%Y/%m/%d') }}
                </p>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائياتي</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ total_sessions }}</h4>
                            <small class="text-muted">إجمالي الحصص</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ completed_sessions }}</h4>
                        <small class="text-muted">حصص مكتملة</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <h5 class="text-info">{{ "%.1f"|format(completion_rate) }}%</h5>
                    <small class="text-muted">معدل الإكمال</small>
                </div>
                {% if active_subscription %}
                <hr>
                <div class="text-center">
                    <h6 class="text-warning">{{ active_subscription.package.name }}</h6>
                    <small class="text-muted">الباقة النشطة</small>
                    <div class="mt-2">
                        <span class="badge bg-success">{{ active_subscription.sessions_remaining }} حصة متبقية</span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Profile Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-edit me-2"></i>تحديث الملف الشخصي</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('student.update_profile') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name"
                                       value="{{ current_user.first_name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name"
                                       value="{{ current_user.last_name }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" value="{{ current_user.email }}" disabled>
                        <div class="form-text">لا يمكن تغيير البريد الإلكتروني</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="{{ current_user.phone or '' }}" placeholder="+966xxxxxxxxx">
                    </div>
                    
                    <hr>
                    <h6>تغيير كلمة المرور</h6>
                    <p class="text-muted">اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور</p>
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="current_password" name="current_password">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       minlength="6">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       minlength="6">
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                        <a href="{{ url_for('student.dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Account Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الدور:</strong></td>
                                <td>
                                    <span class="badge bg-info">طالب</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حالة الحساب:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if current_user.status == 'approved' else 'bg-warning' if current_user.status == 'pending' else 'bg-danger' }}">
                                        {% if current_user.status == 'approved' %}مقبول
                                        {% elif current_user.status == 'pending' %}معلق
                                        {% else %}مرفوض{% endif %}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التسجيل:</strong></td>
                                <td>{{ current_user.created_at.strftime('%Y-%m-%d') if current_user.created_at else '-' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>آخر دخول:</strong></td>
                                <td>{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else 'لم يدخل بعد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>إجمالي الحصص:</strong></td>
                                <td>{{ current_user.student_sessions|length }}</td>
                            </tr>
                            <tr>
                                <td><strong>الاشتراكات:</strong></td>
                                <td>{{ current_user.subscriptions|length }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = this.value;
        
        if (newPassword && confirmPassword && newPassword !== confirmPassword) {
            this.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Require current password if new password is entered
    document.getElementById('new_password').addEventListener('input', function() {
        const currentPasswordField = document.getElementById('current_password');
        
        if (this.value) {
            currentPasswordField.required = true;
            currentPasswordField.parentElement.querySelector('label').innerHTML = 
                'كلمة المرور الحالية <span class="text-danger">*</span>';
        } else {
            currentPasswordField.required = false;
            currentPasswordField.parentElement.querySelector('label').innerHTML = 'كلمة المرور الحالية';
        }
    });
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const currentPassword = document.getElementById('current_password').value;
        
        if (newPassword && !currentPassword) {
            e.preventDefault();
            alert('يرجى إدخال كلمة المرور الحالية');
            document.getElementById('current_password').focus();
            return;
        }
        
        if (newPassword && newPassword !== confirmPassword) {
            e.preventDefault();
            alert('كلمات المرور غير متطابقة');
            document.getElementById('confirm_password').focus();
            return;
        }
        
        if (newPassword && newPassword.length < 6) {
            e.preventDefault();
            alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            document.getElementById('new_password').focus();
            return;
        }
    });
</script>
{% endblock %}
