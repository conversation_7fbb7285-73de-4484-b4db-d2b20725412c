{% extends "base.html" %}

{% block title %}اختيار وسيلة الدفع - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}اختيار وسيلة الدفع{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Package Summary -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-box me-2"></i>ملخص الباقة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4>{{ package.name }}</h4>
                        <p class="text-muted">{{ package.description }}</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>{{ package.sessions_count }} حصة</li>
                            <li><i class="fas fa-check text-success me-2"></i>مدة {{ package.duration_days }} يوم</li>
                            {% if package.features %}
                                {% for feature in package.features.split(',') %}
                                <li><i class="fas fa-check text-success me-2"></i>{{ feature.strip() }}</li>
                                {% endfor %}
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="display-6 text-primary">
                            {{ "%.2f"|format(package.price) }} {{ academy_currency }}
                        </div>
                        <small class="text-muted">السعر الإجمالي</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Method Selection -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>اختر وسيلة الدفع
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('student.process_purchase', package_id=package.id) }}" id="paymentForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    {% if payment_methods %}
                        <div class="row">
                            {% for method in payment_methods %}
                            <div class="col-md-6 mb-3">
                                <div class="card payment-method-card" style="cursor: pointer; transition: all 0.3s;">
                                    <div class="card-body text-center">
                                        <input type="radio" name="payment_method" value="{{ method.name }}" 
                                               id="payment_{{ method.name }}" class="d-none payment-radio">
                                        <label for="payment_{{ method.name }}" class="w-100 h-100 d-block" style="cursor: pointer;">
                                            <div class="display-6 text-primary mb-3">
                                                <i class="{{ method.icon }}"></i>
                                            </div>
                                            <h6>{{ method.display_name }}</h6>
                                            <p class="text-muted small">{{ method.description }}</p>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>لا توجد وسائل دفع متاحة حالياً</strong>
                            <p class="mb-0">يمكنك طلب مراجعة يدوية من الإدارة</p>
                        </div>
                    {% endif %}
                    
                    <!-- Manual Review Option -->
                    <div class="col-12 mb-3">
                        <div class="card payment-method-card border-secondary" style="cursor: pointer; transition: all 0.3s;">
                            <div class="card-body text-center">
                                <input type="radio" name="payment_method" value="manual_review" 
                                       id="payment_manual_review" class="d-none payment-radio">
                                <label for="payment_manual_review" class="w-100 h-100 d-block" style="cursor: pointer;">
                                    <div class="display-6 text-secondary mb-3">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <h6>مراجعة يدوية من الإدارة</h6>
                                    <p class="text-muted small">سيتم مراجعة طلبك من قبل الإدارة وسيتم التواصل معك</p>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <a href="{{ url_for('student.packages') }}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-arrow-right me-2"></i>العودة للباقات
                            </a>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary w-100" id="continueBtn" disabled>
                                <i class="fas fa-arrow-left me-2"></i>متابعة الدفع
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="card mt-4 border-success">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <i class="fas fa-shield-alt fa-3x text-success"></i>
                    </div>
                    <div class="col-md-10">
                        <h6 class="text-success">دفع آمن ومضمون</h6>
                        <p class="text-muted small mb-0">
                            جميع المعاملات محمية بأحدث تقنيات التشفير. لن يتم حفظ بيانات بطاقتك الائتمانية على خوادمنا.
                            يتم معالجة جميع المدفوعات عبر بوابات دفع معتمدة وآمنة.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paymentCards = document.querySelectorAll('.payment-method-card');
        const paymentRadios = document.querySelectorAll('.payment-radio');
        const continueBtn = document.getElementById('continueBtn');
        
        // Handle payment method selection
        paymentCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                paymentCards.forEach(c => {
                    c.classList.remove('border-primary', 'bg-light');
                    c.style.transform = 'scale(1)';
                });
                
                // Add active class to clicked card
                this.classList.add('border-primary', 'bg-light');
                this.style.transform = 'scale(1.02)';
                
                // Check the radio button
                const radio = this.querySelector('.payment-radio');
                radio.checked = true;
                
                // Enable continue button
                continueBtn.disabled = false;
            });
        });
        
        // Handle radio button changes
        paymentRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    continueBtn.disabled = false;
                }
            });
        });
        
        // Form submission
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
            
            if (!selectedMethod) {
                e.preventDefault();
                alert('يرجى اختيار وسيلة دفع');
                return;
            }
            
            // Show loading state
            continueBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
            continueBtn.disabled = true;
        });
        
        // Add hover effects
        paymentCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                if (!this.classList.contains('border-primary')) {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                }
            });
            
            card.addEventListener('mouseleave', function() {
                if (!this.classList.contains('border-primary')) {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                }
            });
        });
    });
</script>

<style>
    .payment-method-card {
        border: 2px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .payment-method-card:hover {
        border-color: #007bff;
    }
    
    .payment-method-card.border-primary {
        border-color: #007bff !important;
        background-color: #f8f9fa !important;
    }
    
    .payment-radio:checked + label .card-body {
        background-color: rgba(0, 123, 255, 0.1);
    }
</style>
{% endblock %}
