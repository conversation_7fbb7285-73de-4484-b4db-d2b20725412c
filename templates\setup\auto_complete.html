{% extends "setup/base.html" %}

{% block title %}تم الإعداد التلقائي - {{ super() }}{% endblock %}

{% block header_title %}تم إعداد النظام تلقائياً{% endblock %}
{% block header_subtitle %}تم إنشاء حساب المدير وإعدادات الأكاديمية بنجاح{% endblock %}

{% block progress %}
<div class="setup-progress">
    <div class="progress">
        <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
    <div class="step-indicator">
        <div class="step completed">الترحيب</div>
        <div class="step completed">عن النظام</div>
        <div class="step completed">المميزات</div>
        <div class="step completed">المطور</div>
        <div class="step completed active">مكتمل</div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="text-center">
    <!-- Success Icon -->
    <div class="mb-4">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
    </div>
    
    <!-- Main Message -->
    <h2 class="text-success mb-3">
        <i class="fas fa-magic me-2"></i>تم الإعداد التلقائي بنجاح!
    </h2>
    
    <p class="lead text-muted mb-4">
        تم إنشاء حساب المدير وإعدادات الأكاديمية تلقائياً. يمكنك البدء في استخدام النظام الآن.
    </p>
    
    <!-- Countdown Timer -->
    <div class="countdown-container mb-4">
        <div class="countdown-circle">
            <div class="countdown-number" id="countdown">30</div>
            <div class="countdown-text">ثانية</div>
        </div>
        <p class="text-muted mt-2">سيتم توجيهك تلقائياً لصفحة تسجيل الدخول</p>
    </div>
    
    <!-- Admin Credentials -->
    <div class="credentials-card">
        <h5 class="text-primary mb-3">
            <i class="fas fa-key me-2"></i>بيانات تسجيل الدخول
        </h5>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="credential-item">
                    <label class="form-label text-muted">البريد الإلكتروني:</label>
                    <div class="credential-value">
                        <code id="adminEmail">{{ admin_email }}</code>
                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('adminEmail')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-3">
                <div class="credential-item">
                    <label class="form-label text-muted">كلمة المرور:</label>
                    <div class="credential-value">
                        <code id="adminPassword">{{ admin_password }}</code>
                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('adminPassword')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-warning mt-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>مهم جداً:</strong> احفظ هذه البيانات في مكان آمن. ستحتاجها لتسجيل الدخول إلى النظام.
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="action-buttons mt-4">
        <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-lg me-3">
            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول الآن
        </a>
        
        <button class="btn btn-outline-secondary" onclick="stopCountdown()">
            <i class="fas fa-pause me-2"></i>إيقاف العداد
        </button>
    </div>
    
    <!-- Additional Info -->
    <div class="mt-4">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            يمكنك تخصيص إعدادات الأكاديمية لاحقاً من لوحة التحكم
        </small>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.success-icon {
    font-size: 4rem;
    color: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.countdown-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.countdown-circle {
    width: 120px;
    height: 120px;
    border: 4px solid #007bff;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    animation: rotate 30s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.countdown-number {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

.countdown-text {
    font-size: 0.8rem;
    color: #6c757d;
}

.credentials-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.credential-item {
    text-align: start;
}

.credential-value {
    display: flex;
    align-items: center;
    background: white;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.credential-value code {
    flex: 1;
    background: none;
    color: #495057;
    font-size: 1rem;
}

.action-buttons .btn {
    min-width: 180px;
}

/* Responsive */
@media (max-width: 768px) {
    .countdown-circle {
        width: 100px;
        height: 100px;
    }
    
    .countdown-number {
        font-size: 1.5rem;
    }
    
    .credentials-card {
        padding: 1rem;
    }
    
    .action-buttons .btn {
        min-width: auto;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let countdownInterval;
let countdownActive = true;

function startCountdown() {
    let timeLeft = 30;
    const countdownElement = document.getElementById('countdown');
    
    countdownInterval = setInterval(function() {
        if (!countdownActive) return;
        
        timeLeft--;
        countdownElement.textContent = timeLeft;
        
        if (timeLeft <= 0) {
            clearInterval(countdownInterval);
            window.location.href = "{{ url_for('auth.login') }}";
        }
    }, 1000);
}

function stopCountdown() {
    countdownActive = false;
    clearInterval(countdownInterval);
    document.getElementById('countdown').textContent = '⏸️';
    
    // Update button
    const stopBtn = document.querySelector('button[onclick="stopCountdown()"]');
    stopBtn.innerHTML = '<i class="fas fa-play me-2"></i>استئناف العداد';
    stopBtn.setAttribute('onclick', 'resumeCountdown()');
}

function resumeCountdown() {
    countdownActive = true;
    const currentTime = parseInt(document.getElementById('countdown').textContent) || 30;
    document.getElementById('countdown').textContent = currentTime;
    
    // Update button
    const resumeBtn = document.querySelector('button[onclick="resumeCountdown()"]');
    resumeBtn.innerHTML = '<i class="fas fa-pause me-2"></i>إيقاف العداد';
    resumeBtn.setAttribute('onclick', 'stopCountdown()');
    
    startCountdown();
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = element.nextElementSibling;
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';
        
        setTimeout(function() {
            button.innerHTML = originalHTML;
        }, 2000);
    });
}

// Start countdown when page loads
document.addEventListener('DOMContentLoaded', function() {
    startCountdown();
});
</script>
{% endblock %}
