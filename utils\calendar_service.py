#!/usr/bin/env python3
"""
Google Calendar Service for Quran LMS
Handles Google Calendar integration for session management
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GoogleCalendarService:
    """Service for managing Google Calendar integration"""
    
    def __init__(self):
        self.service = None
        self.calendar_id = None
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize Google Calendar service with service account credentials"""
        try:
            # Get service account credentials from environment or file
            credentials_json = os.environ.get('GOOGLE_SERVICE_ACCOUNT_JSON')
            
            if credentials_json:
                # Load from environment variable
                credentials_info = json.loads(credentials_json)
                credentials = service_account.Credentials.from_service_account_info(
                    credentials_info,
                    scopes=[
                        'https://www.googleapis.com/auth/calendar',
                        'https://www.googleapis.com/auth/calendar.events',
                        'https://www.googleapis.com/auth/meetings'
                    ]
                )
            else:
                # Load from file (for development)
                credentials_file = 'quranlms-calendar-service-account.json'
                if os.path.exists(credentials_file):
                    credentials = service_account.Credentials.from_service_account_file(
                        credentials_file,
                        scopes=[
                            'https://www.googleapis.com/auth/calendar',
                            'https://www.googleapis.com/auth/calendar.events',
                            'https://www.googleapis.com/auth/meetings'
                        ]
                    )
                else:
                    logger.warning("No Google Calendar credentials found")
                    return False
            
            # Build the service
            self.service = build('calendar', 'v3', credentials=credentials)
            logger.info("✅ Google Calendar service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Google Calendar service: {str(e)}")
            return False
    
    def set_calendar_id(self, calendar_id: str):
        """Set the calendar ID to use"""
        self.calendar_id = calendar_id
    
    def is_available(self) -> bool:
        """Check if Google Calendar service is available"""
        return self.service is not None
    
    def create_event(self, session) -> Optional[str]:
        """Create a calendar event for a session"""
        if not self.is_available() or not self.calendar_id:
            logger.warning("Google Calendar service not available or calendar ID not set")
            return None
        
        try:
            # Get calendar settings for templates
            from models import CalendarSettings
            settings = CalendarSettings.get_settings()
            
            # Prepare event data and get the generated Jitsi link
            event_data, jitsi_link = self._prepare_event_data(session, settings)

            # Create the event (without conferenceDataVersion to avoid API limitations)
            event = self.service.events().insert(
                calendarId=self.calendar_id,
                body=event_data
            ).execute()

            event_id = event.get('id')
            logger.info(f"✅ Created calendar event {event_id} for session {session.id}")

            # Store the generated Jitsi link in session
            if jitsi_link:
                session.meeting_link = jitsi_link
                logger.info(f"✅ Stored Jitsi meeting link in session: {jitsi_link}")
            else:
                logger.warning(f"⚠️ No Jitsi link generated for session {session.id}")

            return event_id
            
        except HttpError as e:
            logger.error(f"❌ HTTP error creating calendar event: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error creating calendar event: {str(e)}")
            return None
    
    def update_event(self, event_id: str, session) -> bool:
        """Update an existing calendar event"""
        if not self.is_available() or not self.calendar_id:
            return False
        
        try:
            # Get calendar settings for templates
            from models import CalendarSettings
            settings = CalendarSettings.get_settings()
            
            # Prepare updated event data
            event_data = self._prepare_event_data(session, settings)
            
            # Update the event
            self.service.events().update(
                calendarId=self.calendar_id,
                eventId=event_id,
                body=event_data,
                conferenceDataVersion=1  # Required for Google Meet integration
            ).execute()

            logger.info(f"✅ Updated calendar event {event_id} for session {session.id}")
            return True
            
        except HttpError as e:
            logger.error(f"❌ HTTP error updating calendar event: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Error updating calendar event: {str(e)}")
            return False
    
    def delete_event(self, event_id: str) -> bool:
        """Delete a calendar event"""
        if not self.is_available() or not self.calendar_id:
            return False
        
        try:
            self.service.events().delete(
                calendarId=self.calendar_id,
                eventId=event_id
            ).execute()
            
            logger.info(f"✅ Deleted calendar event {event_id}")
            return True
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.info(f"Calendar event {event_id} not found (already deleted)")
                return True
            logger.error(f"❌ HTTP error deleting calendar event: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Error deleting calendar event: {str(e)}")
            return False
    
    def _prepare_event_data(self, session, settings) -> tuple[Dict[str, Any], str]:
        """Prepare event data for Google Calendar"""
        # Format session type in Arabic
        session_type_ar = {
            'trial': 'تجريبية',
            'makeup': 'تعويضية', 
            'scheduled': 'مجدولة'
        }.get(session.session_type, session.session_type)
        
        # Prepare template variables
        variables = {
            'student_name': session.student.full_name,
            'teacher_name': session.teacher.full_name,
            'session_type': session_type_ar,
            'duration': session.duration_minutes,
            'meeting_link': session.meeting_link or 'سيتم إرسال الرابط قريباً'
        }
        
        # Generate Jitsi meeting link (more reliable than Google Meet API)
        from utils.session_link_generator import SessionLinkGenerator
        jitsi_link = SessionLinkGenerator.generate_jitsi_link(
            session.id,
            session.scheduled_datetime
        )

        # Update variables with the Jitsi link
        variables['meeting_link'] = jitsi_link

        # Format title and description
        title = settings.event_title_template.format(**variables)
        description = settings.event_description_template.format(**variables)

        # Add meeting link prominently to description
        description += f"\n\n🎥 رابط الحصة المباشر:\n{jitsi_link}"
        description += f"\n\n📋 تعليمات الدخول:"
        description += f"\n• انقر على الرابط أعلاه للانضمام للحصة"
        description += f"\n• الرابط يعمل على جميع الأجهزة والمتصفحات"
        description += f"\n• لا يحتاج تسجيل أو تطبيقات إضافية"
        
        # Calculate end time
        start_time = session.scheduled_datetime
        end_time = start_time + timedelta(minutes=session.duration_minutes)
        
        # Prepare event data
        event_data = {
            'summary': title,
            'description': description,
            'start': {
                'dateTime': start_time.isoformat(),
                'timeZone': 'Africa/Cairo',  # Egypt timezone
            },
            'end': {
                'dateTime': end_time.isoformat(),
                'timeZone': 'Africa/Cairo',
            },
            # Note: Attendees removed to avoid Domain-Wide Delegation requirement
            # 'attendees': [
            #     {'email': session.teacher.email, 'displayName': session.teacher.full_name},
            #     {'email': session.student.email, 'displayName': session.student.full_name}
            # ],
            'reminders': {
                'useDefault': False,
                'overrides': [
                    {'method': 'email', 'minutes': 24 * 60},  # 1 day before
                    {'method': 'email', 'minutes': 5},        # 5 minutes before
                ],
            },
            # Note: Google Meet will be added automatically if calendar has auto-add enabled
            # Or we'll generate a custom Google Meet link
        }

        return event_data, jitsi_link

    def get_event_meeting_link(self, event_id: str) -> Optional[str]:
        """Get Google Meet link from calendar event"""
        if not self.is_available() or not self.calendar_id:
            return None

        try:
            # Get the event
            event = self.service.events().get(
                calendarId=self.calendar_id,
                eventId=event_id
            ).execute()

            # First try to extract from conference data (if available)
            conference_data = event.get('conferenceData', {})
            entry_points = conference_data.get('entryPoints', [])

            for entry_point in entry_points:
                if entry_point.get('entryPointType') == 'video':
                    meeting_link = entry_point.get('uri')
                    if meeting_link and 'meet.google.com' in meeting_link:
                        logger.info(f"✅ Extracted Google Meet link from conference data: {meeting_link}")
                        return meeting_link

            # If no conference data, try to extract from description
            description = event.get('description', '')
            if description:
                import re
                # Look for Google Meet links in description
                meet_pattern = r'https://meet\.google\.com/[a-z0-9\-]+'
                matches = re.findall(meet_pattern, description)
                if matches:
                    meeting_link = matches[0]
                    logger.info(f"✅ Extracted Google Meet link from description: {meeting_link}")
                    return meeting_link

            logger.warning(f"⚠️ No Google Meet link found in event {event_id}")
            return None

        except HttpError as e:
            logger.error(f"❌ HTTP error getting event meeting link: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error getting event meeting link: {str(e)}")
            return None

    def test_connection(self) -> Dict[str, Any]:
        """Test Google Calendar connection"""
        if not self.is_available():
            return {
                'success': False,
                'message': 'خدمة Google Calendar غير متاحة'
            }
        
        try:
            # Try to get calendar info
            calendar = self.service.calendars().get(calendarId=self.calendar_id).execute()
            
            return {
                'success': True,
                'message': f'تم الاتصال بنجاح مع التقويم: {calendar.get("summary", "غير محدد")}',
                'calendar_name': calendar.get('summary'),
                'calendar_id': calendar.get('id')
            }
            
        except HttpError as e:
            return {
                'success': False,
                'message': f'خطأ في الاتصال: {e}'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'خطأ غير متوقع: {str(e)}'
            }


# Global instance
calendar_service = GoogleCalendarService()
