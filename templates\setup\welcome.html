{% extends "setup/base.html" %}

{% block title %}مرحباً بك - {{ super() }}{% endblock %}

{% block header_title %}مرحباً بك في نظام <span class="company-name">ماركتيشن - Marketation</span> لإدارة الأكاديميات التعليمية أونلاين{% endblock %}
{% block header_subtitle %}نظام شامل ومتطور لإدارة أكاديميات تحفيظ القرآن واللغة العربية أونلاين{% endblock %}

{% block progress %}
<div class="setup-progress">
    <div class="progress">
        <div class="progress-bar" role="progressbar" style="width: 10%" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
    </div>
    <div class="step-indicator">
        <div class="step active">الترحيب</div>
        <div class="step">عن النظام</div>
        <div class="step">المميزات</div>
        <div class="step">المطور</div>
        <div class="step">الإعداد</div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="text-center mb-4">
    <div class="feature-icon">
        <i class="fas fa-quran"></i>
    </div>
    <h2 class="mb-3">أهلاً وسهلاً بك!</h2>
    <p class="lead text-muted">
        نرحب بك في نظام <strong class="company-name">ماركتيشن - Marketation</strong> لإدارة الأكاديميات التعليمية الأكثر تطوراً وشمولية
    </p>
</div>

<div class="feature-grid">
    <div class="feature-card">
        <div class="feature-icon">
            <i class="fas fa-graduation-cap"></i>
        </div>
        <h5>إدارة شاملة</h5>
        <p class="text-muted">إدارة كاملة للطلاب والمعلمين والحصص والاشتراكات</p>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon">
            <i class="fas fa-credit-card"></i>
        </div>
        <h5>نظام دفع متقدم</h5>
        <p class="text-muted">دعم متعدد لبوابات الدفع مع أمان عالي</p>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <h5>تقارير وإحصائيات</h5>
        <p class="text-muted">تقارير مفصلة وإحصائيات دقيقة لمتابعة الأداء</p>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon">
            <i class="fas fa-mobile-alt"></i>
        </div>
        <h5>تصميم متجاوب</h5>
        <p class="text-muted">يعمل بكفاءة على جميع الأجهزة والشاشات</p>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>
    <strong>ملاحظة:</strong> سيستغرق الإعداد الأولي حوالي 5-10 دقائق. يمكنك تخطي أي خطوة والعودة إليها لاحقاً.
</div>

<!-- Desktop Layout -->
<div class="d-none d-md-flex justify-content-between align-items-center mt-4">
    <div>
        <a href="{{ url_for('setup.skip_all') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-forward me-2"></i>تخطي جميع الخطوات
        </a>
        <a href="{{ url_for('setup.reset') }}" class="btn btn-outline-warning">
            <i class="fas fa-redo me-2"></i>إعادة تعيين
        </a>
    </div>

    <a href="{{ url_for('setup.about') }}" class="btn btn-primary">
        <i class="fas fa-arrow-left me-2"></i>التالي
    </a>
</div>

<!-- Mobile Layout -->
<div class="d-md-none mt-4">
    <!-- Primary Action Button -->
    <div class="d-grid mb-3">
        <a href="{{ url_for('setup.about') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-arrow-left me-2"></i>التالي
        </a>
    </div>

    <!-- Secondary Actions -->
    <div class="row g-2">
        <div class="col-6">
            <div class="d-grid">
                <a href="{{ url_for('setup.skip_all') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-forward me-1"></i>تخطي الكل
                </a>
            </div>
        </div>
        <div class="col-6">
            <div class="d-grid">
                <a href="{{ url_for('setup.reset') }}" class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-redo me-1"></i>إعادة تعيين
                </a>
            </div>
        </div>
    </div>
</div>

<div class="text-center mt-4">
    <small class="text-muted">
        <i class="fas fa-clock me-1"></i>
        وقت الإعداد المتوقع: 5-10 دقائق
    </small>
</div>
{% endblock %}
