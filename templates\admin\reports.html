{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}التقارير والإحصائيات{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        transition: transform 0.2s;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .progress {
        height: 8px;
    }

    .card-body {
        overflow: hidden;
    }

    .card {
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .vertical-progress {
        width: 20px;
        margin: 0 auto;
        background-color: #e9ecef;
        border-radius: 0.25rem;
    }

    .border-end {
        border-right: 1px solid #dee2e6 !important;
    }

    .h3, .h4, .h5 {
        margin-bottom: 0.5rem;
    }

    .small {
        font-size: 0.875em;
    }

    .row {
        margin-left: -0.75rem;
        margin-right: -0.75rem;
    }

    .col-lg-8, .col-lg-6, .col-lg-4, .col-md-3, .col-md-6 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    @media (max-width: 768px) {
        .border-end {
            border-right: none !important;
            border-bottom: 1px solid #dee2e6 !important;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-primary border-4 stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-primary text-uppercase mb-1">إجمالي الإيرادات</div>
                        <div class="h4 mb-0">{{ format_currency(stats.total_revenue) }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-success border-4 stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-success text-uppercase mb-1">إيرادات الشهر</div>
                        <div class="h4 mb-0">${{ "%.2f"|format(stats.monthly_revenue) }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-info border-4 stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-info text-uppercase mb-1">إجمالي الحصص</div>
                        <div class="h4 mb-0">{{ stats.total_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-start border-warning border-4 stats-card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">حصص مكتملة</div>
                        <div class="h4 mb-0">{{ stats.completed_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small fw-bold text-warning text-uppercase mb-1">الحصص المجدولة</div>
                        <div class="h5 mb-0">{{ stats.scheduled_sessions }}</div>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar-alt fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Trends -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>اتجاه الإيرادات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-md-3">
                        <div class="border-end">
                            <div class="h4 text-success">${{ "%.0f"|format(stats.monthly_revenue) }}</div>
                            <div class="small text-muted">هذا الشهر</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <div class="h4 text-primary">${{ "%.0f"|format(stats.total_revenue) }}</div>
                            <div class="small text-muted">إجمالي الإيرادات</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <div class="h4 text-info">${{ "%.0f"|format(stats.avg_revenue_per_user) }}</div>
                            <div class="small text-muted">متوسط لكل مستخدم</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="h4 text-warning">{{ "%.1f"|format(stats.renewal_rate) }}%</div>
                        <div class="small text-muted">معدل التجديد</div>
                    </div>
                </div>

                <!-- Revenue Progress -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">تقدم الإيرادات الشهرية</span>
                        <span class="small fw-bold">85%</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 85%"></div>
                    </div>
                </div>

                <!-- Monthly Comparison -->
                <div class="row">
                    <div class="col-2">
                        <div class="text-center">
                            <div class="small text-muted">يناير</div>
                            <div class="progress vertical-progress mt-2" style="height: 60px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="height: 60%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="text-center">
                            <div class="small text-muted">فبراير</div>
                            <div class="progress vertical-progress mt-2" style="height: 60px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="height: 70%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="text-center">
                            <div class="small text-muted">مارس</div>
                            <div class="progress vertical-progress mt-2" style="height: 60px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="height: 80%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="text-center">
                            <div class="small text-muted">أبريل</div>
                            <div class="progress vertical-progress mt-2" style="height: 60px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="height: 90%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="text-center">
                            <div class="small text-muted">مايو</div>
                            <div class="progress vertical-progress mt-2" style="height: 60px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="height: 85%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="text-center">
                            <div class="small text-muted">يونيو</div>
                            <div class="progress vertical-progress mt-2" style="height: 60px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="height: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>إحصائيات الحصص
                </h5>
            </div>
            <div class="card-body">
                <!-- Sessions Summary -->
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="h3 text-success">{{ stats.completed_sessions }}</div>
                        <div class="small text-muted">مكتملة</div>
                    </div>
                    <div class="col-6">
                        <div class="h3 text-primary">{{ stats.total_sessions }}</div>
                        <div class="small text-muted">إجمالي</div>
                    </div>
                </div>

                <!-- Sessions Breakdown -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="small">مكتملة:</span>
                        <span class="small fw-bold text-success">{{ stats.completed_sessions or 0 }}</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 75%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="small">مجدولة:</span>
                        <span class="small fw-bold text-warning">{{ stats.scheduled_sessions or 0 }}</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 20%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="small">ملغية:</span>
                        <span class="small fw-bold text-danger">{{ stats.cancelled_sessions or 0 }}</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar bg-danger" role="progressbar" style="width: 5%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Reports -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>إحصائيات المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <div class="h4 text-primary">{{ stats.active_students }}</div>
                            <div class="small text-muted">طلاب نشطون</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <div class="h4 text-success">{{ stats.active_teachers }}</div>
                            <div class="small text-muted">معلمون نشطون</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="h4 text-info">{{ stats.total_users - stats.active_students - stats.active_teachers }}</div>
                        <div class="small text-muted">إداريون</div>
                    </div>
                </div>
                
                <hr>
                
                <div class="progress mb-2">
                    <div class="progress-bar bg-primary" role="progressbar" 
                         style="width: {{ (stats.active_students / stats.total_users * 100) if stats.total_users > 0 else 0 }}%">
                        طلاب
                    </div>
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ (stats.active_teachers / stats.total_users * 100) if stats.total_users > 0 else 0 }}%">
                        معلمون
                    </div>
                </div>
                <div class="small text-muted text-center">توزيع المستخدمين</div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>أداء الحصص
                </h5>
            </div>
            <div class="card-body">
                {% set completion_rate = (stats.completed_sessions / stats.total_sessions * 100) if stats.total_sessions > 0 else 0 %}
                {% set cancellation_rate = (stats.cancelled_sessions / stats.total_sessions * 100) if stats.total_sessions > 0 else 0 %}
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">معدل إكمال الحصص</span>
                        <span class="small fw-bold">{{ "%.1f"|format(completion_rate) }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ completion_rate }}%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">معدل إلغاء الحصص</span>
                        <span class="small fw-bold">{{ "%.1f"|format(stats.cancellation_rate) }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-danger" role="progressbar" style="width: {{ stats.cancellation_rate }}%"></div>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h5 text-success">{{ stats.completed_sessions }}</div>
                        <div class="small text-muted">حصص مكتملة</div>
                    </div>
                    <div class="col-6">
                        <div class="h5 text-primary">{{ stats.total_sessions }}</div>
                        <div class="small text-muted">إجمالي الحصص</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export and Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download me-2"></i>تصدير التقارير
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-success w-100" onclick="exportReport('financial')">
                            <i class="fas fa-file-excel me-2"></i>التقرير المالي
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="exportReport('sessions')">
                            <i class="fas fa-file-pdf me-2"></i>تقرير الحصص
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-info w-100" onclick="exportReport('users')">
                            <i class="fas fa-file-csv me-2"></i>تقرير المستخدمين
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-warning w-100" onclick="exportReport('complete')">
                            <i class="fas fa-file-archive me-2"></i>تقرير شامل
                        </button>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>فترة التقرير</h6>
                        <div class="row">
                            <div class="col-6">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date">
                            </div>
                            <div class="col-6">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>خيارات التصدير</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_charts" checked>
                            <label class="form-check-label" for="include_charts">
                                تضمين الرسوم البيانية
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_details" checked>
                            <label class="form-check-label" for="include_details">
                                تضمين التفاصيل
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Settings Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>حالة إعدادات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <div class="display-6 text-primary mb-2">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <h6>إعدادات الدفع</h6>
                            <span class="badge bg-success">مُكوّنة</span>
                            <div class="mt-2">
                                <small class="text-muted">Stripe & PayPal</small>
                            </div>
                            <a href="{{ url_for('admin.payment_settings') }}" class="btn btn-outline-primary btn-sm mt-2">
                                <i class="fas fa-cog me-1"></i>إدارة
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <div class="display-6 text-success mb-2">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h6>إعدادات البريد</h6>
                            <span class="badge bg-warning">يحتاج تكوين</span>
                            <div class="mt-2">
                                <small class="text-muted">SMTP & إشعارات</small>
                            </div>
                            <a href="{{ url_for('admin.email_settings') }}" class="btn btn-outline-success btn-sm mt-2">
                                <i class="fas fa-cog me-1"></i>إدارة
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center p-3 border rounded">
                            <div class="display-6 text-warning mb-2">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h6>إعدادات الأمان</h6>
                            <span class="badge bg-success">آمن</span>
                            <div class="mt-2">
                                <small class="text-muted">كلمات مرور قوية</small>
                            </div>
                            <a href="{{ url_for('admin.security_settings') }}" class="btn btn-outline-warning btn-sm mt-2">
                                <i class="fas fa-cog me-1"></i>إدارة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add some interactivity to progress bars
    document.addEventListener('DOMContentLoaded', function() {
        // Animate progress bars on load
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(function(bar) {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(function() {
                bar.style.transition = 'width 1s ease-in-out';
                bar.style.width = width;
            }, 100);
        });

        // Add hover effects to cards
        const cards = document.querySelectorAll('.card');
        cards.forEach(function(card) {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });
    });
    
    // Export functions
    function exportReport(type) {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        // Show loading
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
        btn.disabled = true;

        // Create export URL based on type
        let exportUrl = '';
        switch(type) {
            case 'المستخدمين':
                exportUrl = '/admin/export/users';
                break;
            case 'الحصص':
                exportUrl = '/admin/export/sessions';
                break;
            case 'المدفوعات':
                exportUrl = '/admin/export/payments';
                break;
            default:
                exportUrl = '/admin/custom-report';
        }

        // Add date parameters if provided
        if (startDate && endDate) {
            exportUrl += `?start_date=${startDate}&end_date=${endDate}`;
        }

        // Redirect to export URL
        window.location.href = exportUrl;

        // Reset button after a short delay
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 1000);
    }
    
    // Set default dates (last month)
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
    
    document.getElementById('start_date').value = lastMonth.toISOString().split('T')[0];
    document.getElementById('end_date').value = endOfLastMonth.toISOString().split('T')[0];
</script>
{% endblock %}
