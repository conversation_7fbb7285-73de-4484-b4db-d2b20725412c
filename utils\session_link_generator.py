import hashlib
import uuid
from datetime import datetime

class SessionLinkGenerator:
    """Service for generating unique session links"""
    
    @staticmethod
    def generate_unique_id(session_id, date_time=None, include_time=False):
        """Generate unique identifier for session"""
        if date_time is None:
            date_time = datetime.now()
        
        # Create base string
        base_string = f"session_{session_id}_{date_time.strftime('%Y%m%d')}"
        
        if include_time:
            base_string += f"_{date_time.strftime('%H%M')}"
        
        # Add random component for uniqueness
        random_component = str(uuid.uuid4())[:8]
        base_string += f"_{random_component}"
        
        # Create hash for shorter ID
        hash_object = hashlib.md5(base_string.encode())
        return hash_object.hexdigest()[:12]
    

    
    @staticmethod
    def generate_jitsi_link(session_id, session_date=None, settings=None):
        """Generate Jitsi Meet link"""
        if settings is None:
            from models import SessionSettings
            settings = SessionSettings.get_settings()

        if not settings.jitsi_enabled:
            return None

        unique_id = SessionLinkGenerator.generate_unique_id(
            session_id,
            session_date,
            settings.include_time
        )

        # Format: https://meet.jit.si/AcademySession123abc
        # Jitsi allows custom room names, so this will work reliably
        room_name = f"{settings.jitsi_prefix}Session{unique_id}"
        return f"https://{settings.jitsi_domain}/{room_name}"



    @staticmethod
    def generate_session_link(session_id, provider=None, session_date=None):
        """Generate session link based on provider"""
        from models import SessionSettings
        settings = SessionSettings.get_settings()

        if provider is None:
            provider = settings.default_provider

        if not settings.auto_generate_links:
            return None

        if provider == 'google_meet':
            # Google Meet links are now handled via Google Calendar
            # Return placeholder for manual setup
            return 'https://meet.google.com/new'
        elif provider == 'jitsi':
            return SessionLinkGenerator.generate_jitsi_link(session_id, session_date, settings)
        else:
            return None
    
    @staticmethod
    def get_provider_display_name(provider):
        """Get display name for provider"""
        providers = {
            'google_meet': 'Google Meet',
            'jitsi': 'Jitsi Meet',
            'zoom': 'Zoom'
        }
        return providers.get(provider, provider)
    
    @staticmethod
    def get_available_providers():
        """Get list of available providers"""
        from models import SessionSettings
        settings = SessionSettings.get_settings()
        providers = []

        # Google Meet is always available (via Google Calendar)
        if settings.use_google_calendar:
            providers.append({
                'value': 'google_meet',
                'name': 'Google Meet (via Calendar)',
                'icon': 'fab fa-google',
                'color': '#4285f4'
            })

        if settings.jitsi_enabled:
            providers.append({
                'value': 'jitsi',
                'name': 'Jitsi Meet',
                'icon': 'fas fa-video',
                'color': '#1d76ba'
            })

        return providers

    @staticmethod
    def get_provider_display_name(provider):
        """Get display name for provider"""
        provider_names = {
            'google_meet': 'Google Meet',
            'jitsi': 'Jitsi Meet'
        }
        return provider_names.get(provider, provider)
