{% extends "base.html" %}

{% block title %}إعدادات الدفع - أكاديمية القرآن الكريم{% endblock %}
{% block page_title %}إعدادات الدفع{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('admin.settings') }}">الإعدادات</a></li>
                <li class="breadcrumb-item active" aria-current="page">إعدادات الدفع</li>
            </ol>
        </nav>

        <!-- Back Button -->
        <div class="mb-3">
            <a href="{{ url_for('admin.settings') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للإعدادات
            </a>
        </div>

        <form method="POST" action="{{ url_for('admin.payment_settings') }}" id="paymentSettingsForm">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            
            <!-- General Payment Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>الإعدادات العامة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_currency" class="form-label">العملة الافتراضية</label>
                                <select class="form-select" id="default_currency" name="default_currency">
                                    <option value="USD" {% if academy_settings and academy_settings.currency == 'USD' %}selected{% endif %}>دولار أمريكي (USD)</option>
                                    <option value="EUR" {% if academy_settings and academy_settings.currency == 'EUR' %}selected{% endif %}>يورو (EUR)</option>
                                    <option value="SAR" {% if academy_settings and academy_settings.currency == 'SAR' %}selected{% endif %}>ريال سعودي (SAR)</option>
                                    <option value="AED" {% if academy_settings and academy_settings.currency == 'AED' %}selected{% endif %}>درهم إماراتي (AED)</option>
                                    <option value="EGP" {% if academy_settings and academy_settings.currency == 'EGP' %}selected{% endif %}>جنيه مصري (EGP)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stripe Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fab fa-stripe me-2"></i>إعدادات Stripe
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="stripe_enabled" name="stripe_enabled" 
                                       {% if stripe_gateway and stripe_gateway.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="stripe_enabled">
                                    تفعيل Stripe
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="stripe_config" style="{% if not stripe_gateway or not stripe_gateway.is_active %}display: none;{% endif %}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stripe_publishable_key" class="form-label">Publishable Key</label>
                                    <input type="text" class="form-control" id="stripe_publishable_key" name="stripe_publishable_key" 
                                           value="{{ stripe_gateway.api_key if stripe_gateway else '' }}" 
                                           placeholder="pk_test_...">
                                    <div class="form-text">المفتاح العام لـ Stripe</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stripe_secret_key" class="form-label">Secret Key</label>
                                    <input type="password" class="form-control" id="stripe_secret_key" name="stripe_secret_key" 
                                           value="{{ stripe_gateway.secret_key if stripe_gateway else '' }}" 
                                           placeholder="sk_test_...">
                                    <div class="form-text">المفتاح السري لـ Stripe</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> يمكنك الحصول على مفاتيح Stripe من 
                            <a href="https://dashboard.stripe.com/apikeys" target="_blank">لوحة تحكم Stripe</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PayPal Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fab fa-paypal me-2"></i>إعدادات PayPal
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="paypal_enabled" name="paypal_enabled" 
                                       {% if paypal_gateway and paypal_gateway.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="paypal_enabled">
                                    تفعيل PayPal
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="paypal_config" style="{% if not paypal_gateway or not paypal_gateway.is_active %}display: none;{% endif %}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="paypal_client_id" class="form-label">Client ID</label>
                                    <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" 
                                           value="{{ paypal_gateway.api_key if paypal_gateway else '' }}" 
                                           placeholder="AYSq3RDGsmBLJE-otTkBtM-jBRd1TCQwFf9RGfwddNXWz0uFU9ztymylOhRS">
                                    <div class="form-text">معرف العميل لـ PayPal</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="paypal_client_secret" class="form-label">Client Secret</label>
                                    <input type="password" class="form-control" id="paypal_client_secret" name="paypal_client_secret" 
                                           value="{{ paypal_gateway.secret_key if paypal_gateway else '' }}" 
                                           placeholder="EGnHDxD_qRPdaLdHGkioDmfKdqtBIibiSxijqaNjSEi-QJALD-L6OdHu6B1_FwVz6BKW3K-W7Wk7HcXSn3bNq7hQrds">
                                    <div class="form-text">المفتاح السري لـ PayPal</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> يمكنك الحصول على بيانات PayPal من 
                            <a href="https://developer.paypal.com/developer/applications/" target="_blank">PayPal Developer</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>حالة بوابات الدفع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <i class="fab fa-stripe fa-2x text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Stripe</h6>
                                    <span class="badge {% if stripe_gateway and stripe_gateway.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                        {% if stripe_gateway and stripe_gateway.is_active %}نشط{% else %}غير نشط{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <i class="fab fa-paypal fa-2x text-info"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">PayPal</h6>
                                    <span class="badge {% if paypal_gateway and paypal_gateway.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                        {% if paypal_gateway and paypal_gateway.is_active %}نشط{% else %}غير نشط{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i>إعدادات ذات صلة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{{ url_for('admin.email_settings') }}" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-envelope me-2"></i>إعدادات البريد الإلكتروني
                                <div class="small text-muted">لإرسال إشعارات الدفع</div>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ url_for('admin.security_settings') }}" class="btn btn-outline-warning w-100 mb-2">
                                <i class="fas fa-shield-alt me-2"></i>إعدادات الأمان
                                <div class="small text-muted">لحماية المعاملات</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>حفظ إعدادات الدفع
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Toggle Stripe configuration
    document.getElementById('stripe_enabled').addEventListener('change', function() {
        const config = document.getElementById('stripe_config');
        if (this.checked) {
            config.style.display = 'block';
        } else {
            config.style.display = 'none';
        }
    });

    // Toggle PayPal configuration
    document.getElementById('paypal_enabled').addEventListener('change', function() {
        const config = document.getElementById('paypal_config');
        if (this.checked) {
            config.style.display = 'block';
        } else {
            config.style.display = 'none';
        }
    });

    // Form validation
    document.getElementById('paymentSettingsForm').addEventListener('submit', function(e) {
        const stripeEnabled = document.getElementById('stripe_enabled').checked;
        const paypalEnabled = document.getElementById('paypal_enabled').checked;
        
        if (stripeEnabled) {
            const publishableKey = document.getElementById('stripe_publishable_key').value.trim();
            const secretKey = document.getElementById('stripe_secret_key').value.trim();
            
            if (!publishableKey || !secretKey) {
                e.preventDefault();
                alert('يرجى إدخال مفاتيح Stripe المطلوبة');
                return;
            }
        }
        
        if (paypalEnabled) {
            const clientId = document.getElementById('paypal_client_id').value.trim();
            const clientSecret = document.getElementById('paypal_client_secret').value.trim();
            
            if (!clientId || !clientSecret) {
                e.preventDefault();
                alert('يرجى إدخال بيانات PayPal المطلوبة');
                return;
            }
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
        submitBtn.disabled = true;
        
        // Re-enable button after 3 seconds (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });
</script>
{% endblock %}
