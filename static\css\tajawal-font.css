/* <PERSON><PERSON><PERSON> Font Styles for Quran LMS */

/* Import <PERSON><PERSON><PERSON> from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* Global Font Application */
* {
    font-family: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Body and Base Elements */
body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif !important;
    font-weight: 400;
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

/* Headings */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: 'Ta<PERSON>wal', sans-serif !important;
    font-weight: 700;
    line-height: 1.4;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif !important;
    font-weight: 800;
    line-height: 1.2;
}

/* Navigation Elements */
.navbar-brand {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif !important;
    font-weight: 800;
    font-size: 1.5rem;
}

.nav-link {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

.navbar-nav .nav-link {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

.sidebar-nav .nav-link {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

/* Buttons */
.btn {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.btn-lg {
    font-weight: 700;
}

.btn-sm {
    font-weight: 500;
}

/* Form Elements */
.form-control, 
.form-select, 
.form-check-label,
.form-label {
    font-family: 'Tajawal', sans-serif !important;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    font-family: 'Tajawal', sans-serif !important;
    font-size: 0.875rem;
}

input, textarea, select {
    font-family: 'Tajawal', sans-serif !important;
}

/* Table Elements */
.table, 
.table th, 
.table td {
    font-family: 'Tajawal', sans-serif !important;
}

.table th {
    font-weight: 700;
}

.table td {
    font-weight: 400;
}

/* Card Elements */
.card-title {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
}

.card-text {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

.card-header {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

/* Badge and Labels */
.badge {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.label {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

/* Alert Components */
.alert {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

.alert-heading {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
}

/* Modal Components */
.modal-title {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
}

.modal-body {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

.modal-header {
    font-family: 'Tajawal', sans-serif !important;
}

/* Breadcrumb */
.breadcrumb {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

.breadcrumb-item {
    font-family: 'Tajawal', sans-serif !important;
}

/* Pagination */
.page-link {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

.page-item.active .page-link {
    font-weight: 700;
}

/* List Group */
.list-group-item {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

.list-group-item-heading {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
}

/* Dropdown */
.dropdown-menu {
    font-family: 'Tajawal', sans-serif !important;
}

.dropdown-item {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

.dropdown-header {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
}

/* Tooltip and Popover */
.tooltip {
    font-family: 'Tajawal', sans-serif !important;
}

.popover {
    font-family: 'Tajawal', sans-serif !important;
}

.popover-header {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
}

.popover-body {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

/* Text Utilities */
.text-muted {
    font-family: 'Tajawal', sans-serif !important;
}

.small, small {
    font-family: 'Tajawal', sans-serif !important;
    font-size: 0.875rem;
}

.lead {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
    font-size: 1.25rem;
}

/* Custom Components */
.notification-badge {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
    font-size: 0.75rem;
}

.stats-card {
    font-family: 'Tajawal', sans-serif !important;
}

.stats-card .h5 {
    font-weight: 800;
}

/* Progress Components */
.progress {
    font-family: 'Tajawal', sans-serif !important;
}

/* Tab Components */
.nav-tabs .nav-link {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

.nav-pills .nav-link {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

/* Accordion */
.accordion-button {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

.accordion-body {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

/* Offcanvas */
.offcanvas-title {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
}

.offcanvas-body {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

/* Toast */
.toast-header {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 500;
}

.toast-body {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 400;
}

/* Font Weight Utilities */
.fw-light {
    font-weight: 200 !important;
}

.fw-normal {
    font-weight: 400 !important;
}

.fw-medium {
    font-weight: 500 !important;
}

.fw-semibold {
    font-weight: 700 !important;
}

.fw-bold {
    font-weight: 800 !important;
}

.fw-bolder {
    font-weight: 900 !important;
}

/* Arabic Text Optimization */
.arabic-text {
    font-family: 'Tajawal', sans-serif !important;
    line-height: 1.8;
    letter-spacing: 0.025em;
}

.arabic-heading {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700;
    line-height: 1.4;
}

/* Responsive Font Sizes */
@media (max-width: 768px) {
    body {
        font-size: 0.9rem;
    }
    
    h1, .h1 {
        font-size: 1.75rem;
    }
    
    h2, .h2 {
        font-size: 1.5rem;
    }
    
    h3, .h3 {
        font-size: 1.25rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Print Styles */
@media print {
    * {
        font-family: 'Tajawal', serif !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.5;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
}
