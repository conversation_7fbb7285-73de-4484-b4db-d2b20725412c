{% extends "base.html" %}

{% block title %}تفاصيل الحصة - {{ academy_name }}{% endblock %}
{% block page_title %}تفاصيل الحصة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>تفاصيل الحصة
                </h5>
                <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                    {% if session.status == 'completed' %}مكتملة
                    {% elif session.status == 'scheduled' %}مجدولة
                    {% elif session.status == 'cancelled' %}ملغية
                    {% else %}فائتة{% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الحصة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الطالب:</strong></td>
                                <td>{{ session.student.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع الحصة:</strong></td>
                                <td>
                                    {% if session.session_type == 'trial' %}حصة تجريبية
                                    {% elif session.session_type == 'makeup' %}حصة تعويضية
                                    {% else %}حصة مجدولة{% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td><strong>الوقت:</strong></td>
                                <td>{{ session.scheduled_datetime.strftime('%H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>المدة:</strong></td>
                                <td>{{ session.duration_minutes }} دقيقة</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>حالة الحصة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                                        {% if session.status == 'completed' %}مكتملة
                                        {% elif session.status == 'scheduled' %}مجدولة
                                        {% elif session.status == 'cancelled' %}ملغية
                                        {% else %}فائتة{% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% if session.status == 'completed' %}
                            <tr>
                                <td><strong>حضور الطالب:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.student_attended else 'bg-danger' }}">
                                        {{ 'نعم' if session.student_attended else 'لا' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حضور المعلم:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.teacher_attended else 'bg-danger' }}">
                                        {{ 'نعم' if session.teacher_attended else 'لا' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإكمال:</strong></td>
                                <td>{{ session.completed_at.strftime('%Y-%m-%d %H:%M') if session.completed_at else '-' }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <!-- Meeting Link Section -->
                {% if session.status == 'scheduled' %}
                <div class="mt-4">
                    <h6>رابط الحصة</h6>
                    <div class="card border-primary">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <h6 class="mb-1">
                                        <i class="fas fa-video me-2 text-primary"></i>
                                        {{ session.get_meeting_provider_name() }}
                                    </h6>
                                    <small class="text-muted">انقر لبدء الحصة</small>
                                </div>
                                <div>
                                    {% if session.teacher_attended %}
                                        {% if session.meeting_link %}
                                            <a href="{{ session.meeting_link }}" target="_blank"
                                               class="btn btn-primary btn-lg">
                                                <i class="fas fa-video me-2"></i>دخول للحصة
                                            </a>
                                        {% else %}
                                            <form method="POST" action="{{ url_for('teacher.start_session', session_id=session.id) }}" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                <button type="submit" class="btn btn-primary btn-lg">
                                                    <i class="fas fa-video me-2"></i>دخول للحصة
                                                </button>
                                            </form>
                                        {% endif %}
                                        <div class="mt-2">
                                            <small class="text-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                تم تسجيل حضورك
                                            </small>
                                            {% if session.meeting_provider == 'google_meet' %}
                                                <div class="mt-1">
                                                    <small class="text-info">
                                                        <i class="fab fa-google me-1"></i>
                                                        سيتم إنشاء اجتماع جديد، شارك رمز الغرفة مع الطلاب
                                                    </small>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <form method="POST" action="{{ url_for('teacher.start_session', session_id=session.id) }}" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-video me-2"></i>بدء الحصة
                                            </button>
                                        </form>
                                        <div class="mt-2">
                                            <small class="text-warning">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                اضغط لتسجيل الحضور وبدء الحصة
                                            </small>
                                            {% if session.meeting_provider == 'google_meet' and session.meeting_link == 'https://meet.google.com/new' %}
                                                <div class="mt-1">
                                                    <small class="text-info">
                                                        <i class="fab fa-google me-1"></i>
                                                        ملاحظة: ستحتاج لإنشاء غرفة جديدة ومشاركة الرابط مع الطالب
                                                    </small>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-8">
                                        <small class="text-muted">
                                            <i class="fas fa-link me-1"></i>
                                            <strong>الرابط:</strong> {{ session.meeting_link }}
                                        </small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-secondary btn-sm"
                                                onclick="copyToClipboard('{{ session.meeting_link }}')">
                                            <i class="fas fa-copy me-1"></i>نسخ الرابط
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if session.subscription %}
                <div class="mt-4">
                    <h6>معلومات الاشتراك</h6>
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-box fa-2x me-3"></i>
                            <div>
                                <strong>{{ session.subscription.package.name }}</strong><br>
                                <small>الحصص المتبقية: {{ session.subscription.sessions_remaining or 0 }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if session.notes %}
                <div class="mt-4">
                    <h6>ملاحظاتي</h6>
                    <div class="alert alert-light">
                        <p class="mb-0">{{ session.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                <!-- Action Buttons -->
                <div class="mt-4 d-flex gap-2 flex-wrap">
                    {% if session.status == 'scheduled' %}
                    <button type="button" class="btn btn-warning"
                            data-bs-toggle="modal" data-bs-target="#completeModal">
                        <i class="fas fa-check me-2"></i>إكمال الحصة
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('teacher.sessions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للحصص
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Student Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>معلومات الطالب
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto"
                     style="width: 80px; height: 80px; font-size: 24px;">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <h6>{{ session.student.full_name }}</h6>
                <p class="text-muted">{{ session.student.email }}</p>
                {% if session.student.phone %}
                <p class="text-muted">
                    <i class="fas fa-phone me-1"></i>{{ session.student.phone }}
                </p>
                {% endif %}
            </div>
        </div>
        
        <!-- Session Rating -->
        {% if rating %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>تقييم الطالب
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="h3 text-warning">{{ rating.rating }}/5</div>
                <div class="rating-display mb-3">
                    {% for i in range(1, 6) %}
                        <i class="fas fa-star {{ 'text-warning' if i <= rating.rating else 'text-muted' }}"></i>
                    {% endfor %}
                </div>
                {% if rating.comment %}
                <p class="text-muted">{{ rating.comment }}</p>
                {% endif %}
                <small class="text-muted">{{ rating.created_at.strftime('%Y-%m-%d') }}</small>
            </div>
        </div>
        {% endif %}
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('teacher.sessions') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-calendar-alt me-2"></i>جميع حصصي
                    </a>
                    <a href="{{ url_for('teacher.students') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-user-graduate me-2"></i>طلابي
                    </a>
                    <a href="{{ url_for('teacher.sessions') }}?student={{ session.student.id }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-history me-2"></i>حصص هذا الطالب
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Complete Session Modal -->
{% if session.status == 'scheduled' %}
<div class="modal fade" id="completeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إكمال الحصة مع {{ session.student.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('teacher.complete_session', session_id=session.id) }}">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="student_attended" 
                                   name="student_attended" checked>
                            <label class="form-check-label" for="student_attended">
                                الطالب حضر الحصة
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات الحصة</label>
                        <textarea class="form-control" id="notes" name="notes" 
                                  rows="4" placeholder="اكتب ملاحظاتك حول الحصة...">{{ session.notes or '' }}</textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إرسال إشعار للطالب بإكمال الحصة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إكمال الحصة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .rating-display {
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Confirm session completion
    document.querySelector('#completeModal form').addEventListener('submit', function(e) {
        if (!confirm('هل أنت متأكد من إكمال هذه الحصة؟ لا يمكن التراجع عن هذا الإجراء.')) {
            e.preventDefault();
        }
    });

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>تم نسخ الرابط بنجاح!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        console.error('فشل في نسخ الرابط: ', err);
        alert('فشل في نسخ الرابط. يرجى نسخه يدوياً.');
    });
}
</script>
{% endblock %}
