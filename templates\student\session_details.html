{% extends "base.html" %}

{% block title %}تفاصيل الحصة - {{ academy_name }}{% endblock %}
{% block page_title %}تفاصيل الحصة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>تفاصيل الحصة
                </h5>
                <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                    {% if session.status == 'completed' %}مكتملة
                    {% elif session.status == 'scheduled' %}مجدولة
                    {% elif session.status == 'cancelled' %}ملغية
                    {% else %}فائتة{% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الحصة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المعلم:</strong></td>
                                <td>{{ session.teacher.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع الحصة:</strong></td>
                                <td>
                                    {% if session.session_type == 'trial' %}حصة تجريبية
                                    {% elif session.session_type == 'makeup' %}حصة تعويضية
                                    {% else %}حصة مجدولة{% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>التاريخ:</strong></td>
                                <td>{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td><strong>الوقت:</strong></td>
                                <td>{{ session.scheduled_datetime.strftime('%H:%M') }}</td>
                            </tr>
                            <tr>
                                <td><strong>المدة:</strong></td>
                                <td>{{ session.duration_minutes }} دقيقة</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>حالة الحصة</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                                        {% if session.status == 'completed' %}مكتملة
                                        {% elif session.status == 'scheduled' %}مجدولة
                                        {% elif session.status == 'cancelled' %}ملغية
                                        {% else %}فائتة{% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% if session.status == 'completed' %}
                            <tr>
                                <td><strong>حضور الطالب:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.student_attended else 'bg-danger' }}">
                                        {{ 'نعم' if session.student_attended else 'لا' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حضور المعلم:</strong></td>
                                <td>
                                    <span class="badge {{ 'bg-success' if session.teacher_attended else 'bg-danger' }}">
                                        {{ 'نعم' if session.teacher_attended else 'لا' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإكمال:</strong></td>
                                <td>{{ session.completed_at.strftime('%Y-%m-%d %H:%M') if session.completed_at else '-' }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <!-- Meeting Link Section -->
                {% if session.status == 'scheduled' %}
                <div class="mt-4">
                    <h6>رابط الحصة</h6>
                    <div class="card border-success">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <h6 class="mb-1">
                                        <i class="fas fa-video me-2 text-success"></i>
                                        {{ session.get_meeting_provider_name() }}
                                    </h6>
                                    <small class="text-muted">انقر للانضمام إلى الحصة</small>
                                </div>
                                <div>
                                    {% if session.student_attended %}
                                        {% if session.meeting_link %}
                                            <a href="{{ session.meeting_link }}" target="_blank"
                                               class="btn btn-success btn-lg">
                                                <i class="fas fa-video me-2"></i>دخول للحصة
                                            </a>
                                        {% else %}
                                            <form method="POST" action="{{ url_for('student.join_session', session_id=session.id) }}" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                <button type="submit" class="btn btn-success btn-lg">
                                                    <i class="fas fa-video me-2"></i>دخول للحصة
                                                </button>
                                            </form>
                                        {% endif %}
                                        <div class="mt-2">
                                            <small class="text-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                تم تسجيل حضورك
                                            </small>
                                        </div>
                                    {% else %}
                                        <form method="POST" action="{{ url_for('student.join_session', session_id=session.id) }}" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                            <button type="submit" class="btn btn-success btn-lg">
                                                <i class="fas fa-video me-2"></i>انضمام للحصة
                                            </button>
                                        </form>
                                        <div class="mt-2">
                                            <small class="text-warning">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                اضغط لتسجيل الحضور والانضمام
                                            </small>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    سيتم فتح الرابط في نافذة جديدة
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if session.subscription %}
                <div class="mt-4">
                    <h6>معلومات الاشتراك</h6>
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-box fa-2x me-3"></i>
                            <div>
                                <strong>{{ session.subscription.package.name }}</strong><br>
                                <small>الحصص المتبقية: {{ session.subscription.sessions_remaining or 0 }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if session.notes %}
                <div class="mt-4">
                    <h6>ملاحظات المعلم</h6>
                    <div class="alert alert-light">
                        <p class="mb-0">{{ session.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                <!-- Action Buttons -->
                <div class="mt-4 d-flex gap-2 flex-wrap">
                    
                    {% if session.status == 'completed' and not existing_rating %}
                    <button type="button" class="btn btn-warning" 
                            data-bs-toggle="modal" data-bs-target="#ratingModal">
                        <i class="fas fa-star me-2"></i>تقييم الحصة
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('student.sessions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للحصص
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Teacher Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chalkboard-teacher me-2"></i>معلومات المعلم
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto"
                     style="width: 80px; height: 80px; font-size: 24px;">
                    <i class="fas fa-chalkboard-teacher"></i>
                </div>
                <h6>{{ session.teacher.full_name }}</h6>
                <p class="text-muted">{{ session.teacher.email }}</p>
                {% if session.teacher.phone %}
                <p class="text-muted">
                    <i class="fas fa-phone me-1"></i>{{ session.teacher.phone }}
                </p>
                {% endif %}
            </div>
        </div>
        
        <!-- Session Rating -->
        {% if existing_rating %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>تقييمك للحصة
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="h3 text-warning">{{ existing_rating.rating }}/5</div>
                <div class="rating-display mb-3">
                    {% for i in range(1, 6) %}
                        <i class="fas fa-star {{ 'text-warning' if i <= existing_rating.rating else 'text-muted' }}"></i>
                    {% endfor %}
                </div>
                {% if existing_rating.comment %}
                <p class="text-muted">{{ existing_rating.comment }}</p>
                {% endif %}
                <small class="text-muted">{{ existing_rating.created_at.strftime('%Y-%m-%d') }}</small>
            </div>
        </div>
        {% endif %}
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('student.sessions') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-calendar-alt me-2"></i>جميع حصصي
                    </a>
                    <a href="{{ url_for('student.teachers') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-chalkboard-teacher me-2"></i>معلميّ
                    </a>
                    <a href="{{ url_for('student.subscriptions') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-credit-card me-2"></i>اشتراكاتي
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rating Modal -->
{% if session.status == 'completed' and not existing_rating %}
<div class="modal fade" id="ratingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم الحصة مع {{ session.teacher.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('student.rate_session', session_id=session.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">

                    
                    <div class="mb-3">
                        <label class="form-label">التقييم (من 1 إلى 5 نجوم)</label>
                        <div class="rating-stars text-center" data-rating="0">
                            {% for i in range(1, 6) %}
                            <i class="fas fa-star star" data-value="{{ i }}" style="font-size: 2rem; cursor: pointer; color: #ddd; margin: 0 5px;"></i>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="rating" id="rating" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="comment" class="form-label">تعليق (اختياري)</label>
                        <textarea class="form-control" id="comment" name="comment" 
                                  rows="3" placeholder="شاركنا رأيك في الحصة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إرسال التقييم</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .rating-display {
        font-size: 1.2rem;
    }
    
    .rating-stars .star {
        transition: color 0.2s ease;
    }
    
    .rating-stars .star:hover,
    .rating-stars .star.active {
        color: #ffc107 !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Rating stars functionality
    const ratingContainer = document.querySelector('.rating-stars');
    if (ratingContainer) {
        const stars = ratingContainer.querySelectorAll('.star');
        const hiddenInput = document.getElementById('rating');
        
        stars.forEach((star, index) => {
            star.addEventListener('click', function() {
                const rating = index + 1;
                hiddenInput.value = rating;
                ratingContainer.setAttribute('data-rating', rating);
                
                stars.forEach((s, i) => {
                    if (i < rating) {
                        s.classList.add('active');
                        s.style.color = '#ffc107';
                    } else {
                        s.classList.remove('active');
                        s.style.color = '#ddd';
                    }
                });
            });
            
            star.addEventListener('mouseenter', function() {
                const rating = index + 1;
                stars.forEach((s, i) => {
                    if (i < rating) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        ratingContainer.addEventListener('mouseleave', function() {
            const currentRating = parseInt(ratingContainer.getAttribute('data-rating'));
            stars.forEach((s, i) => {
                if (i < currentRating) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    }


</script>
{% endblock %}
