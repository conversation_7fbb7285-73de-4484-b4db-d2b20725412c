from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_required, current_user
from functools import wraps
from models import User, Session, SessionRating, Notification, UserNotificationSettings, db
from datetime import datetime, timedelta
from sqlalchemy import func

teacher_bp = Blueprint('teacher', __name__)

def teacher_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'teacher':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@teacher_bp.route('/dashboard')
@login_required
@teacher_required
def dashboard():
    # Statistics for teacher dashboard
    total_sessions = Session.query.filter_by(teacher_id=current_user.id).count()
    completed_sessions = Session.query.filter_by(
        teacher_id=current_user.id, 
        status='completed'
    ).count()
    
    scheduled_sessions = Session.query.filter(
        Session.teacher_id == current_user.id,
        Session.status == 'scheduled',
        Session.scheduled_datetime >= datetime.now()
    ).count()
    
    # Today's sessions
    today = datetime.now().date()
    today_sessions = Session.query.filter(
        Session.teacher_id == current_user.id,
        func.date(Session.scheduled_datetime) == today,
        Session.status == 'scheduled'
    ).order_by(Session.scheduled_datetime).all()

    # This week's sessions
    week_start = datetime.now() - timedelta(days=datetime.now().weekday())
    week_end = week_start + timedelta(days=7)
    
    week_sessions = Session.query.filter(
        Session.teacher_id == current_user.id,
        Session.scheduled_datetime >= week_start,
        Session.scheduled_datetime < week_end,
        Session.status == 'scheduled'
    ).order_by(Session.scheduled_datetime).all()
    
    # Recent completed sessions
    recent_completed = Session.query.filter_by(
        teacher_id=current_user.id,
        status='completed'
    ).order_by(Session.completed_at.desc()).limit(5).all()
    
    # Students count
    students_count = db.session.query(Session.student_id).filter_by(
        teacher_id=current_user.id
    ).distinct().count()
    
    # Average rating
    avg_rating = db.session.query(func.avg(SessionRating.rating)).join(
        Session, SessionRating.session_id == Session.id
    ).filter(
        Session.teacher_id == current_user.id
    ).scalar() or 0
    
    # New session type statistics
    trial_sessions = Session.get_trial_sessions_count(current_user.id, 'teacher')
    makeup_sessions = Session.get_makeup_sessions_count(current_user.id, 'teacher')
    cancelled_sessions = Session.get_cancelled_sessions_count(current_user.id, 'teacher')

    # Subscription sessions (sessions linked to student subscriptions)
    subscription_sessions = Session.query.filter(
        Session.teacher_id == current_user.id,
        Session.status == 'scheduled',
        Session.subscription_id.isnot(None),  # Sessions linked to a subscription
        Session.scheduled_datetime >= datetime.now()
    ).count()

    stats = {
        'total_sessions': total_sessions,
        'completed_sessions': completed_sessions,
        'trial_sessions': trial_sessions,
        'makeup_sessions': makeup_sessions,
        'cancelled_sessions': cancelled_sessions,
        'scheduled_sessions': scheduled_sessions,
        'subscription_sessions': subscription_sessions,
        'students_count': students_count,
        'avg_rating': round(avg_rating, 1) if avg_rating else 0
    }
    
    return render_template('teacher/dashboard.html',
                         stats=stats,
                         today_sessions=today_sessions,
                         week_sessions=week_sessions,
                         recent_completed=recent_completed)

@teacher_bp.route('/sessions')
@login_required
@teacher_required
def sessions():
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    date_filter = request.args.get('date', '')
    type_filter = request.args.get('type', '')

    query = Session.query.filter_by(teacher_id=current_user.id)

    if status_filter:
        query = query.filter_by(status=status_filter)

    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(func.date(Session.scheduled_datetime) == filter_date)
        except ValueError:
            pass

    if type_filter == 'subscription':
        query = query.filter(Session.subscription_id.isnot(None))
    elif type_filter == 'trial':
        query = query.filter_by(session_type='trial')
    elif type_filter == 'makeup':
        query = query.filter_by(session_type='makeup')

    sessions = query.order_by(Session.scheduled_datetime.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('teacher/sessions.html',
                         sessions=sessions,
                         status_filter=status_filter,
                         date_filter=date_filter,
                         type_filter=type_filter)

@teacher_bp.route('/sessions/<int:session_id>/start', methods=['POST'])
@login_required
@teacher_required
def start_session(session_id):
    session = Session.query.filter_by(
        id=session_id,
        teacher_id=current_user.id
    ).first_or_404()

    if session.status != 'scheduled':
        flash('لا يمكن بدء هذه الحصة.', 'danger')
        return redirect(url_for('teacher.sessions'))

    # Mark teacher as attended
    session.teacher_attended = True
    db.session.commit()

    # Ensure meeting link is up to date
    if not session.meeting_link:
        session.generate_meeting_link()
        db.session.commit()
    elif session.meeting_provider == 'google_meet' and session.calendar_event_id:
        # Update meeting link from calendar if it's Google Meet
        session._update_meeting_link_from_calendar()
        db.session.commit()

    # Flash success message and redirect to meeting link
    flash('تم تسجيل حضورك للحصة بنجاح. سيتم توجيهك للحصة الآن...', 'success')

    # If meeting link exists, redirect to it
    if session.meeting_link:
        return f'''
        <script>
            setTimeout(function() {{
                window.open('{session.meeting_link}', '_blank');
                window.location.href = '{url_for('teacher.session_details', session_id=session_id)}';
            }}, 1000);
        </script>
        <div style="text-align: center; padding: 50px; font-family: Arial;">
            <h3>🎉 تم تسجيل حضورك بنجاح!</h3>
            <p>سيتم توجيهك للحصة خلال ثانية واحدة...</p>
            <p><a href="{session.meeting_link}" target="_blank">انقر هنا إذا لم يتم التوجيه تلقائياً</a></p>
        </div>
        '''
    else:
        flash('لم يتم العثور على رابط الحصة. يرجى المحاولة مرة أخرى.', 'warning')
        return redirect(url_for('teacher.session_details', session_id=session_id))

@teacher_bp.route('/sessions/<int:session_id>/complete', methods=['POST'])
@login_required
@teacher_required
def complete_session(session_id):
    session = Session.query.filter_by(
        id=session_id,
        teacher_id=current_user.id
    ).first_or_404()
    
    notes = request.form.get('notes', '')
    student_attended = bool(request.form.get('student_attended'))
    
    session.student_attended = student_attended
    session.notes = notes

    # Mark session as completed (this will send notification)
    session.mark_completed()
    
    db.session.commit()
    
    # Create notification for student
    notification = Notification(
        user_id=session.student_id,
        title='تم إكمال الحصة',
        message=f'تم إكمال حصتك مع الأستاذ {current_user.full_name}',
        notification_type='session'
    )
    db.session.add(notification)
    db.session.commit()
    
    flash('تم إكمال الحصة بنجاح.', 'success')
    return redirect(url_for('teacher.sessions'))

@teacher_bp.route('/sessions/<int:session_id>')
@login_required
@teacher_required
def session_details(session_id):
    session = Session.query.filter_by(
        id=session_id,
        teacher_id=current_user.id
    ).first_or_404()
    
    # Get session rating if exists
    rating = SessionRating.query.filter_by(
        session_id=session_id,
        rater_id=session.student_id
    ).first()
    
    return render_template('teacher/session_details.html',
                         session=session,
                         rating=rating)

@teacher_bp.route('/students')
@login_required
@teacher_required
def students():
    try:
        # Get all sessions for this teacher
        teacher_sessions = Session.query.filter_by(teacher_id=current_user.id).all()

        # Get unique student IDs
        student_ids = list(set([s.student_id for s in teacher_sessions]))

        # Get student objects
        students = User.query.filter(User.id.in_(student_ids)).all()

        students_data = []
        for student in students:
            total_sessions = len([s for s in teacher_sessions if s.student_id == student.id])
            completed_sessions = len([s for s in teacher_sessions if s.student_id == student.id and s.status == 'completed'])
            upcoming_sessions = len([s for s in teacher_sessions if s.student_id == student.id and s.status == 'scheduled' and s.scheduled_datetime >= datetime.now()])

            students_data.append({
                'student': student,
                'total_sessions': total_sessions,
                'completed_sessions': completed_sessions,
                'upcoming_sessions': upcoming_sessions
            })

        return render_template('teacher/students.html',
                             students_data=students_data,
                             students=students_data)
    except Exception as e:
        flash(f'حدث خطأ في تحميل بيانات الطلاب: {str(e)}', 'danger')
        return redirect(url_for('teacher.dashboard'))

@teacher_bp.route('/students/<int:student_id>')
@login_required
@teacher_required
def student_details(student_id):
    student = User.query.get_or_404(student_id)
    
    # Verify this teacher has sessions with this student
    session_exists = Session.query.filter_by(
        teacher_id=current_user.id,
        student_id=student_id
    ).first()
    
    if not session_exists:
        flash('ليس لديك صلاحية لعرض بيانات هذا الطالب.', 'danger')
        return redirect(url_for('teacher.students'))
    
    # Get all sessions with this student
    sessions = Session.query.filter_by(
        teacher_id=current_user.id,
        student_id=student_id
    ).order_by(Session.scheduled_datetime.desc()).all()
    
    # Calculate statistics
    total_sessions = len(sessions)
    completed_sessions = len([s for s in sessions if s.status == 'completed'])
    attendance_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
    
    return render_template('teacher/student_details.html',
                         student=student,
                         sessions=sessions,
                         total_sessions=total_sessions,
                         completed_sessions=completed_sessions,
                         attendance_rate=round(attendance_rate, 1))

@teacher_bp.route('/reports')
@login_required
@teacher_required
def reports():
    try:
        # Monthly sessions report
        current_month = datetime.now().replace(day=1)
        next_month = (current_month + timedelta(days=32)).replace(day=1)

        monthly_sessions = Session.query.filter(
            Session.teacher_id == current_user.id,
            Session.scheduled_datetime >= current_month,
            Session.scheduled_datetime < next_month
        ).all()

        monthly_stats = {
            'total': len(monthly_sessions),
            'completed': len([s for s in monthly_sessions if s.status == 'completed']),
            'scheduled': len([s for s in monthly_sessions if s.status == 'scheduled']),
            'cancelled': len([s for s in monthly_sessions if s.status == 'cancelled'])
        }

        # Weekly sessions for chart
        weeks_data = []
        for i in range(4):
            week_start = current_month + timedelta(weeks=i)
            week_end = week_start + timedelta(days=7)

            week_sessions = Session.query.filter(
                Session.teacher_id == current_user.id,
                Session.scheduled_datetime >= week_start,
                Session.scheduled_datetime < week_end,
                Session.status == 'completed'
            ).count()

            weeks_data.append({
                'week': f'الأسبوع {i+1}',
                'sessions': week_sessions
            })

        # Overall stats for the template
        total_sessions = Session.query.filter_by(teacher_id=current_user.id).count()
        completed_sessions = Session.query.filter_by(
            teacher_id=current_user.id,
            status='completed'
        ).count()

        # Calculate average rating for this teacher
        avg_rating = db.session.query(func.avg(SessionRating.rating)).join(
            Session, SessionRating.session_id == Session.id
        ).filter(
            Session.teacher_id == current_user.id
        ).scalar() or 0

        stats = {
            'total_sessions': total_sessions,
            'completed_sessions': completed_sessions,
            'monthly_total': monthly_stats['total'],
            'monthly_completed': monthly_stats['completed'],
            'monthly_sessions': monthly_stats['total'],  # إضافة الحقل المطلوب
            'avg_rating': round(avg_rating, 1) if avg_rating else 0
        }

        return render_template('teacher/reports.html',
                             stats=stats,
                             monthly_stats=monthly_stats,
                             weeks_data=weeks_data)
    except Exception as e:
        flash(f'حدث خطأ في تحميل التقارير: {str(e)}', 'danger')
        return redirect(url_for('teacher.dashboard'))

@teacher_bp.route('/profile')
@login_required
@teacher_required
def profile():
    """Teacher profile page"""
    # Get teacher statistics
    total_sessions = Session.query.filter_by(teacher_id=current_user.id).count()
    completed_sessions = Session.query.filter_by(
        teacher_id=current_user.id,
        status='completed'
    ).count()

    # Get unique students count
    unique_students = db.session.query(Session.student_id).filter_by(
        teacher_id=current_user.id
    ).distinct().count()

    # Get recent sessions
    recent_sessions = Session.query.filter_by(
        teacher_id=current_user.id
    ).order_by(Session.scheduled_datetime.desc()).limit(5).all()

    # Calculate average rating (ratings given by students to this teacher)
    avg_rating = db.session.query(func.avg(SessionRating.rating)).join(
        Session, SessionRating.session_id == Session.id
    ).filter(
        Session.teacher_id == current_user.id,
        SessionRating.rater_id == Session.student_id  # Rating given by student
    ).scalar() or 0

    # Calculate completion rate
    completion_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0

    return render_template('teacher/profile.html',
                         total_sessions=total_sessions,
                         completed_sessions=completed_sessions,
                         unique_students=unique_students,
                         recent_sessions=recent_sessions,
                         avg_rating=round(avg_rating, 1),
                         completion_rate=completion_rate)

@teacher_bp.route('/profile/update', methods=['POST'])
@login_required
@teacher_required
def update_profile():
    """Update teacher profile"""
    try:
        # Update basic info
        current_user.first_name = request.form.get('first_name', '').strip()
        current_user.last_name = request.form.get('last_name', '').strip()
        current_user.phone = request.form.get('phone', '').strip()

        # Validate required fields
        if not current_user.first_name or not current_user.last_name:
            flash('الاسم الأول والأخير مطلوبان.', 'error')
            return redirect(url_for('teacher.profile'))

        # Update password if provided
        new_password = request.form.get('new_password')
        if new_password:
            current_password = request.form.get('current_password')
            if not current_user.check_password(current_password):
                flash('كلمة المرور الحالية غير صحيحة.', 'error')
                return redirect(url_for('teacher.profile'))

            # Validate new password
            if len(new_password) < 6:
                flash('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل.', 'error')
                return redirect(url_for('teacher.profile'))

            current_user.set_password(new_password)
            flash('تم تحديث كلمة المرور بنجاح.', 'success')

        db.session.commit()
        flash('تم تحديث الملف الشخصي بنجاح.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الملف الشخصي.', 'error')

    return redirect(url_for('teacher.profile'))

@teacher_bp.route('/settings')
@login_required
@teacher_required
def settings():
    """Teacher settings page"""
    # Get teacher statistics for account tab
    total_sessions = Session.query.filter_by(teacher_id=current_user.id).count()
    completed_sessions = Session.query.filter_by(
        teacher_id=current_user.id,
        status='completed'
    ).count()

    unique_students = db.session.query(Session.student_id).filter_by(
        teacher_id=current_user.id
    ).distinct().count()

    # Calculate average rating
    avg_rating = db.session.query(func.avg(SessionRating.rating)).join(
        Session, SessionRating.session_id == Session.id
    ).filter(
        Session.teacher_id == current_user.id,
        SessionRating.rater_id == Session.student_id
    ).scalar() or 0

    # Get or create notification settings
    notification_settings = UserNotificationSettings.get_or_create_for_user(current_user.id)

    return render_template('teacher/settings.html',
                         total_sessions=total_sessions,
                         completed_sessions=completed_sessions,
                         unique_students=unique_students,
                         avg_rating=round(avg_rating, 1),
                         notification_settings=notification_settings)

@teacher_bp.route('/settings/notifications', methods=['POST'])
@login_required
@teacher_required
def update_notification_settings():
    """Update notification settings"""
    try:
        # Get or create notification settings
        notification_settings = UserNotificationSettings.get_or_create_for_user(current_user.id)

        # Update settings from form
        notification_settings.email_notifications = request.form.get('email_notifications') == 'on'
        notification_settings.session_reminders = request.form.get('session_reminders') == 'on'
        notification_settings.student_notifications = request.form.get('student_notifications') == 'on'

        db.session.commit()
        flash('تم تحديث إعدادات الإشعارات بنجاح.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الإعدادات.', 'error')

    return redirect(url_for('teacher.settings'))

@teacher_bp.route('/delete-account', methods=['POST'])
@login_required
@teacher_required
def delete_account():
    """Delete teacher account permanently"""
    try:
        user_id = current_user.id
        user_name = current_user.full_name

        # Delete related data first (to avoid foreign key constraints)
        # Delete sessions
        Session.query.filter_by(teacher_id=user_id).delete()

        # Delete session ratings given to this teacher
        session_ids = [s.id for s in Session.query.filter_by(teacher_id=user_id).all()]
        if session_ids:
            SessionRating.query.filter(SessionRating.session_id.in_(session_ids)).delete(synchronize_session=False)

        # Delete notifications
        Notification.query.filter_by(user_id=user_id).delete()

        # Delete notification settings
        UserNotificationSettings.query.filter_by(user_id=user_id).delete()

        # Finally delete the user
        db.session.delete(current_user)
        db.session.commit()

        # Log out the user
        from flask_login import logout_user
        logout_user()

        flash(f'تم حذف حساب المعلم {user_name} نهائياً. شكراً لخدمتك في الأكاديمية!', 'info')
        return redirect(url_for('auth.login'))

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حذف الحساب. يرجى المحاولة مرة أخرى أو التواصل مع الإدارة.', 'error')
        return redirect(url_for('teacher.settings'))
