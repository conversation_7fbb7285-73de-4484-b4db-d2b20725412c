{% extends "base.html" %}

{% block title %}الباقات المتاحة - {{ academy_name }}{% endblock %}
{% block page_title %}الباقات المتاحة{% endblock %}

{% block content %}
<!-- Pending Subscription Alert -->
{% if pending_subscription %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-clock fa-2x me-3"></i>
                <div>
                    <h5 class="alert-heading">طلب اشتراك معلق</h5>
                    <p class="mb-0">لديك طلب اشتراك في باقة <strong>{{ pending_subscription.package.name }}</strong> قيد المراجعة من قبل الإدارة.</p>
                    <small class="text-muted">تاريخ الطلب: {{ pending_subscription.purchase_date.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="text-center">
            <h2 class="fw-bold text-primary">اختر الباقة المناسبة لك</h2>
            <p class="text-muted">باقات متنوعة تناسب جميع المستويات والاحتياجات</p>
        </div>
    </div>
</div>

<!-- Packages Grid -->
<div class="row justify-content-center">
    {% for package in packages %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 package-card {{ 'border-primary shadow-lg' if loop.index == 2 else 'border-light' }}">
            {% if loop.index == 2 %}
            <div class="card-header bg-primary text-white text-center">
                <span class="badge bg-warning text-dark">الأكثر شعبية</span>
            </div>
            {% endif %}
            
            <div class="card-body text-center">
                <h4 class="card-title text-primary">{{ package.name }}</h4>
                <div class="mb-3">
                    <div class="h1 text-primary">{{ format_currency(package.price, false) }}</div>
                    <div class="text-muted">لمدة {{ package.duration_days }} يوم</div>
                </div>
                
                <p class="card-text text-muted">{{ package.description }}</p>
                
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <span><i class="fas fa-calendar-alt text-primary me-2"></i>عدد الحصص</span>
                        <span class="fw-bold">{{ package.sessions_count }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <span><i class="fas fa-clock text-primary me-2"></i>مدة الصلاحية</span>
                        <span class="fw-bold">{{ package.duration_days }} يوم</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                        <span><i class="fas fa-money-bill-wave text-primary me-2"></i>سعر الحصة</span>
                        <span class="fw-bold">{{ format_currency(package.price / package.sessions_count) }}</span>
                    </div>
                </div>
                
                {% if package.features %}
                <div class="mb-4 text-start">
                    <h6 class="text-center mb-3">المميزات المتضمنة:</h6>
                    {% for feature in package.features.split('\n') %}
                        {% if feature.strip() %}
                        <div class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <span class="small">{{ feature.strip() }}</span>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer bg-transparent">
                {% if pending_subscription %}
                    <button class="btn btn-secondary w-100" disabled>
                        <i class="fas fa-clock me-2"></i>لديك طلب معلق
                    </button>
                {% else %}
                    <form method="POST" action="{{ url_for('student.purchase_package', package_id=package.id) }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <button type="submit" class="btn {{ 'btn-primary' if loop.index == 2 else 'btn-outline-primary' }} w-100"
                                onclick="return confirm('هل أنت متأكد من شراء باقة {{ package.name }}؟')">
                            <i class="fas fa-shopping-cart me-2"></i>اشترك الآن
                        </button>
                    </form>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
    
    {% if not packages %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-box fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد باقات متاحة</h5>
            <p class="text-muted">لا توجد باقات متاحة للشراء حالياً. يرجى المحاولة لاحقاً.</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- Features Comparison -->
{% if packages|length > 1 %}
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 text-center">
                    <i class="fas fa-balance-scale me-2"></i>مقارنة الباقات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover text-center">
                        <thead class="table-primary">
                            <tr>
                                <th>المميزة</th>
                                {% for package in packages %}
                                <th>{{ package.name }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="fw-bold">السعر</td>
                                {% for package in packages %}
                                <td class="text-primary fw-bold">{{ format_currency(package.price, false) }}</td>
                                {% endfor %}
                            </tr>
                            <tr>
                                <td class="fw-bold">عدد الحصص</td>
                                {% for package in packages %}
                                <td>{{ package.sessions_count }}</td>
                                {% endfor %}
                            </tr>
                            <tr>
                                <td class="fw-bold">مدة الصلاحية</td>
                                {% for package in packages %}
                                <td>{{ package.duration_days }} يوم</td>
                                {% endfor %}
                            </tr>
                            <tr>
                                <td class="fw-bold">سعر الحصة الواحدة</td>
                                {% for package in packages %}
                                <td>{{ format_currency(package.price / package.sessions_count) }}</td>
                                {% endfor %}
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- FAQ Section -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 text-center">
                    <i class="fas fa-question-circle me-2"></i>الأسئلة الشائعة
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                كيف يتم تفعيل الاشتراك؟
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                بعد شراء الباقة، سيتم مراجعة طلبك من قبل الإدارة وتفعيل الاشتراك خلال 24 ساعة.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                هل يمكنني تغيير الباقة بعد الشراء؟
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                يمكنك التواصل مع الإدارة لمناقشة إمكانية تغيير الباقة حسب الظروف.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                ماذا يحدث إذا لم أستخدم جميع الحصص؟
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                الحصص غير المستخدمة تنتهي صلاحيتها مع انتهاء مدة الباقة. ننصح بجدولة الحصص مبكراً.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .package-card {
        transition: all 0.3s ease;
        position: relative;
    }
    
    .package-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
    }
    
    .package-card.border-primary {
        transform: scale(1.05);
    }
    
    .package-card.border-primary:hover {
        transform: scale(1.05) translateY(-10px);
    }
    
    @media (max-width: 768px) {
        .package-card.border-primary {
            transform: none;
        }
        
        .package-card.border-primary:hover {
            transform: translateY(-10px);
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Add animation to cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.package-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Smooth scroll to comparison table
    function scrollToComparison() {
        document.querySelector('.table-responsive').scrollIntoView({
            behavior: 'smooth'
        });
    }
</script>
{% endblock %}
