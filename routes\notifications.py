"""
Routes للإشعارات الداخلية
Internal Notifications Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import Notification, db
from utils.notification_service import NotificationService
from datetime import datetime

notifications_bp = Blueprint('notifications', __name__, url_prefix='/notifications')


@notifications_bp.route('/')
@login_required
def index():
    """صفحة الإشعارات الرئيسية"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # جلب الإشعارات مع التصفح
    notifications = Notification.query.filter_by(
        user_id=current_user.id
    ).order_by(
        Notification.created_at.desc()
    ).paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    # إحصائيات
    total_count = Notification.query.filter_by(user_id=current_user.id).count()
    unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    
    return render_template('notifications/index.html',
                         notifications=notifications,
                         total_count=total_count,
                         unread_count=unread_count)


@notifications_bp.route('/api/count')
@login_required
def get_unread_count():
    """API لجلب عدد الإشعارات غير المقروءة"""
    count = Notification.get_unread_count_for_user(current_user.id)
    return jsonify({'count': count})


@notifications_bp.route('/api/recent')
@login_required
def get_recent():
    """API لجلب الإشعارات الأخيرة"""
    limit = request.args.get('limit', 10, type=int)
    notifications = NotificationService.get_user_notifications(
        current_user.id, 
        limit=limit
    )
    
    return jsonify({
        'notifications': [n.to_dict() for n in notifications],
        'unread_count': Notification.get_unread_count_for_user(current_user.id)
    })


@notifications_bp.route('/mark-read/<int:notification_id>', methods=['POST'])
@login_required
def mark_as_read(notification_id):
    """تحديد إشعار كمقروء"""
    success = NotificationService.mark_as_read(notification_id, current_user.id)
    
    if request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': success})
    
    if success:
        flash('تم تحديد الإشعار كمقروء.', 'success')
    else:
        flash('لم يتم العثور على الإشعار.', 'error')
    
    return redirect(url_for('notifications.index'))


@notifications_bp.route('/mark-all-read', methods=['POST'])
@login_required
def mark_all_as_read():
    """تحديد جميع الإشعارات كمقروءة"""
    count = NotificationService.mark_all_as_read(current_user.id)
    
    if request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': True, 'count': count})
    
    flash(f'تم تحديد {count} إشعار كمقروء.', 'success')
    return redirect(url_for('notifications.index'))


@notifications_bp.route('/delete/<int:notification_id>', methods=['POST'])
@login_required
def delete_notification(notification_id):
    """حذف إشعار واحد"""
    success = NotificationService.delete_notification(notification_id, current_user.id)
    
    if request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': success})
    
    if success:
        flash('تم حذف الإشعار.', 'success')
    else:
        flash('لم يتم العثور على الإشعار.', 'error')
    
    return redirect(url_for('notifications.index'))


@notifications_bp.route('/delete-selected', methods=['POST'])
@login_required
def delete_selected():
    """حذف إشعارات محددة"""
    notification_ids = request.form.getlist('notification_ids')
    
    if not notification_ids:
        flash('لم يتم تحديد أي إشعارات.', 'warning')
        return redirect(url_for('notifications.index'))
    
    deleted_count = 0
    for notification_id in notification_ids:
        try:
            if NotificationService.delete_notification(int(notification_id), current_user.id):
                deleted_count += 1
        except ValueError:
            continue
    
    if request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': True, 'count': deleted_count})
    
    flash(f'تم حذف {deleted_count} إشعار.', 'success')
    return redirect(url_for('notifications.index'))


@notifications_bp.route('/delete-all', methods=['POST'])
@login_required
def delete_all():
    """حذف جميع الإشعارات"""
    count = NotificationService.delete_all_notifications(current_user.id)
    
    if request.headers.get('Content-Type') == 'application/json':
        return jsonify({'success': True, 'count': count})
    
    flash(f'تم حذف {count} إشعار.', 'success')
    return redirect(url_for('notifications.index'))


@notifications_bp.route('/view/<int:notification_id>')
@login_required
def view_notification(notification_id):
    """عرض إشعار وتحديده كمقروء"""
    notification = Notification.query.filter_by(
        id=notification_id,
        user_id=current_user.id
    ).first_or_404()
    
    # تحديد كمقروء
    if not notification.is_read:
        notification.mark_as_read()
    
    # إعادة توجيه إلى الرابط المحدد أو صفحة الإشعارات
    if notification.action_url:
        return redirect(notification.action_url)
    else:
        return redirect(url_for('notifications.index'))


@notifications_bp.route('/filter')
@login_required
def filter_notifications():
    """تصفية الإشعارات"""
    notification_type = request.args.get('type', 'all')
    status = request.args.get('status', 'all')  # all, read, unread
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # بناء الاستعلام
    query = Notification.query.filter_by(user_id=current_user.id)
    
    if notification_type != 'all':
        query = query.filter_by(notification_type=notification_type)
    
    if status == 'read':
        query = query.filter_by(is_read=True)
    elif status == 'unread':
        query = query.filter_by(is_read=False)
    
    notifications = query.order_by(
        Notification.created_at.desc()
    ).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )
    
    # إحصائيات
    total_count = Notification.query.filter_by(user_id=current_user.id).count()
    unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
    
    # أنواع الإشعارات المتاحة
    notification_types = db.session.query(
        Notification.notification_type
    ).filter_by(
        user_id=current_user.id
    ).distinct().all()
    
    available_types = [t[0] for t in notification_types if t[0]]
    
    return render_template('notifications/index.html',
                         notifications=notifications,
                         total_count=total_count,
                         unread_count=unread_count,
                         available_types=available_types,
                         current_type=notification_type,
                         current_status=status)


# API للاستخدام مع AJAX
@notifications_bp.route('/api/mark-read-bulk', methods=['POST'])
@login_required
def mark_read_bulk():
    """تحديد إشعارات متعددة كمقروءة"""
    data = request.get_json()
    notification_ids = data.get('notification_ids', [])
    
    if not notification_ids:
        return jsonify({'success': False, 'message': 'لم يتم تحديد أي إشعارات'})
    
    marked_count = 0
    for notification_id in notification_ids:
        if NotificationService.mark_as_read(notification_id, current_user.id):
            marked_count += 1
    
    return jsonify({
        'success': True,
        'count': marked_count,
        'unread_count': Notification.get_unread_count_for_user(current_user.id)
    })


@notifications_bp.route('/api/delete-bulk', methods=['POST'])
@login_required
def delete_bulk():
    """حذف إشعارات متعددة"""
    data = request.get_json()
    notification_ids = data.get('notification_ids', [])
    
    if not notification_ids:
        return jsonify({'success': False, 'message': 'لم يتم تحديد أي إشعارات'})
    
    deleted_count = 0
    for notification_id in notification_ids:
        if NotificationService.delete_notification(notification_id, current_user.id):
            deleted_count += 1
    
    return jsonify({
        'success': True,
        'count': deleted_count,
        'unread_count': Notification.get_unread_count_for_user(current_user.id)
    })


# دالة مساعدة لإنشاء إشعارات تجريبية (للاختبار فقط)
@notifications_bp.route('/create-test', methods=['POST'])
@login_required
def create_test_notification():
    """إنشاء إشعار تجريبي للاختبار"""
    if current_user.role != 'admin':
        flash('غير مسموح لك بهذا الإجراء.', 'error')
        return redirect(url_for('notifications.index'))
    
    NotificationService.create_notification(
        user_id=current_user.id,
        title='إشعار تجريبي',
        message='هذا إشعار تجريبي لاختبار النظام',
        notification_type='system',
        priority='normal',
        icon='fas fa-flask'
    )
    
    flash('تم إنشاء إشعار تجريبي.', 'success')
    return redirect(url_for('notifications.index'))
