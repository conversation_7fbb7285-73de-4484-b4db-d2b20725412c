"""
نظام التنصيب التلقائي لجميع قوالب البريد الإلكتروني في الإنتاج
يحتوي على جميع القوالب الـ 24 المطلوبة للنظام
"""

from models import EmailTemplate, db


def get_all_production_templates():
    """الحصول على جميع قوالب الإنتاج - جميع القوالب الـ 24"""
    
    # استيراد القوالب من الملفات الأخرى
    from utils.email_service import get_default_templates, get_user_management_templates
    
    # جمع جميع القوالب
    all_templates = []
    
    # 1. القوالب الأساسية (5 قوالب)
    default_templates = get_default_templates()
    all_templates.extend(default_templates)
    
    # 2. قوالب إدارة المستخدمين (8 قوالب)  
    user_management_templates = get_user_management_templates()
    all_templates.extend(user_management_templates)
    
    # 3. القوالب الإضافية (4 قوالب)
    additional_templates = get_additional_templates_list()
    all_templates.extend(additional_templates)
    
    # 4. قوالب الإنتاج الإضافية (7 قوالب)
    production_specific_templates = get_production_specific_templates()
    all_templates.extend(production_specific_templates)
    
    return all_templates


def get_additional_templates_list():
    """الحصول على القوالب الإضافية كقائمة (4 قوالب)"""
    return [
        {
            'name': 'payment_confirmation',
            'display_name': 'تأكيد الدفع',
            'subject': 'تأكيد استلام الدفعة - {{ academy_name }}',
            'template_type': 'payment',
            'is_system': True,
            'variables': ['user_name', 'amount', 'payment_date', 'package_name', 'academy_name'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px;">
                        <h2 style="color: #155724;">تأكيد استلام الدفعة</h2>
                        <p>عزيزي/عزيزتي {{ user_name }}،</p>
                        <p>نؤكد استلام دفعتك بنجاح:</p>
                        <ul>
                            <li><strong>المبلغ:</strong> {{ amount }} ريال</li>
                            <li><strong>التاريخ:</strong> {{ payment_date }}</li>
                            <li><strong>الباقة:</strong> {{ package_name }}</li>
                        </ul>
                        <p>شكراً لك على ثقتك في {{ academy_name }}</p>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،
            
            نؤكد استلام دفعتك بنجاح:
            
            المبلغ: {{ amount }} ريال
            التاريخ: {{ payment_date }}
            الباقة: {{ package_name }}
            
            شكراً لك على ثقتك في {{ academy_name }}
            '''
        },
        {
            'name': 'session_completed',
            'display_name': 'إشعار انتهاء الحصة',
            'subject': 'تم إنهاء حصتك مع {{ teacher_name }}',
            'template_type': 'notification',
            'is_system': True,
            'variables': ['user_name', 'teacher_name', 'session_date', 'session_duration', 'academy_name'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #cce5ff; border: 1px solid #99ccff; padding: 20px; border-radius: 5px;">
                        <h2 style="color: #0066cc;">تم إنهاء الحصة</h2>
                        <p>عزيزي/عزيزتي {{ user_name }}،</p>
                        <p>تم إنهاء حصتك بنجاح:</p>
                        <ul>
                            <li><strong>المعلم:</strong> {{ teacher_name }}</li>
                            <li><strong>التاريخ:</strong> {{ session_date }}</li>
                            <li><strong>المدة:</strong> {{ session_duration }} دقيقة</li>
                        </ul>
                        <p>نتمنى أن تكون الحصة مفيدة ونتطلع للقائك في الحصة القادمة.</p>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،
            
            تم إنهاء حصتك بنجاح:
            
            المعلم: {{ teacher_name }}
            التاريخ: {{ session_date }}
            المدة: {{ session_duration }} دقيقة
            
            نتمنى أن تكون الحصة مفيدة ونتطلع للقائك في الحصة القادمة.
            '''
        },
        {
            'name': 'session_reminder_1_day',
            'display_name': 'تذكير بالحصة قبل يوم',
            'subject': '📅 تذكير: حصتك غداً في {{ session_time }} - {{ academy_name }}',
            'template_type': 'reminder',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #ffc107; margin-bottom: 10px;">⏰ تذكير بحصتك غداً</h1>
                        </div>
                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نذكرك بأن لديك حصة مجدولة غداً. نرجو منك الاستعداد والحضور في الموعد المحدد.
                        </p>
                        <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="color: #856404; margin-top: 0;">📅 تفاصيل الحصة:</h3>
                            <p><strong>التاريخ:</strong> {{ session_date }} (غداً)</p>
                            <p><strong>الوقت:</strong> {{ session_time }}</p>
                            <p><strong>المدة:</strong> {{ session_duration }}</p>
                            <p><strong>المعلم:</strong> {{ teacher_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            نذكرك بأن لديك حصة مجدولة غداً. نرجو منك الاستعداد والحضور في الموعد المحدد.

            تفاصيل الحصة:
            التاريخ: {{ session_date }} (غداً)
            الوقت: {{ session_time }}
            المدة: {{ session_duration }}
            المعلم: {{ teacher_name }}

            مع أطيب التحيات،
            {{ academy_name }}
            '''
        },
        {
            'name': 'session_reminder_5_minutes',
            'display_name': 'تذكير بالحصة قبل 5 دقائق',
            'subject': '🔔 حصتك ستبدأ خلال 5 دقائق - {{ academy_name }}',
            'template_type': 'reminder',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #dc3545; margin-bottom: 10px;">🚨 حصتك ستبدأ خلال 5 دقائق!</h1>
                        </div>
                        <p style="font-size: 18px; line-height: 1.6; text-align: center; color: #dc3545; font-weight: bold;">
                            عزيزي/عزيزتي {{ user_name }}،<br>
                            حصتك ستبدأ خلال 5 دقائق فقط!
                        </p>
                        <div style="background-color: #f8d7da; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="color: #721c24; margin-top: 0;">⏰ تفاصيل الحصة الآن:</h3>
                            <p><strong>الوقت:</strong> {{ session_time }} (خلال 5 دقائق)</p>
                            <p><strong>المدة:</strong> {{ session_duration }}</p>
                            <p><strong>المعلم:</strong> {{ teacher_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            🚨 حصتك ستبدأ خلال 5 دقائق فقط!

            تفاصيل الحصة:
            الوقت: {{ session_time }} (خلال 5 دقائق)
            المدة: {{ session_duration }}
            المعلم: {{ teacher_name }}

            {{ academy_name }}
            '''
        }
    ]


def get_production_specific_templates():
    """الحصول على قوالب الإنتاج الإضافية (7 قوالب)"""
    return [
        # قالب ترحيب التسجيل الجديد
        {
            'name': 'user_registration_welcome',
            'display_name': 'ترحيب بالمستخدم الجديد',
            'subject': '🎉 مرحباً بك في {{ academy_name }} - تم إنشاء حسابك بنجاح!',
            'template_type': 'auth',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_role', 'registration_date', 'academy_name', 'academy_email', 'academy_phone', 'academy_website', 'login_url', 'admin_approval_required'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">🎉 مرحباً بك في {{ academy_name }}!</h2>
                    <p>عزيزي {{ user_name }}، تم إنشاء حسابك بنجاح.</p>
                    {% if admin_approval_required %}
                    <p>سيتم مراجعة حسابك من قبل الإدارة وستصلك رسالة تأكيد فور الموافقة.</p>
                    {% else %}
                    <p>يمكنك الآن <a href="{{ login_url }}">تسجيل الدخول</a> والبدء في التعلم.</p>
                    {% endif %}
                </div></body></html>''',
            'body_text': 'مرحباً {{ user_name }}! تم إنشاء حسابك في {{ academy_name }} بنجاح.'
        },

        # قالب إشعار تفعيل الحساب
        {
            'name': 'account_activation_notification',
            'display_name': 'إشعار تفعيل الحساب',
            'subject': '🎉 تهانينا! تم تفعيل حسابك في {{ academy_name }}',
            'template_type': 'auth',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_role', 'activation_date', 'academy_name', 'academy_email', 'academy_phone', 'academy_website', 'login_url', 'dashboard_url', 'admin_message'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">🎉 تهانينا! تم تفعيل حسابك</h2>
                    <p>عزيزي {{ user_name }}، تم تفعيل حسابك في {{ academy_name }} بنجاح!</p>
                    <p>يمكنك الآن <a href="{{ login_url }}">تسجيل الدخول</a> والاستمتاع بجميع خدماتنا.</p>
                    {% if admin_message %}<p><strong>رسالة من الإدارة:</strong> {{ admin_message }}</p>{% endif %}
                </div></body></html>''',
            'body_text': 'تهانينا {{ user_name }}! تم تفعيل حسابك في {{ academy_name }} بنجاح.'
        },

        # قالب تفعيل الاشتراك
        {
            'name': 'subscription_activated',
            'display_name': 'تفعيل الاشتراك',
            'subject': 'تم تفعيل اشتراكك - {{ package_name }}',
            'template_type': 'subscription',
            'is_system': True,
            'variables': ['user_name', 'package_name', 'sessions_count', 'start_date', 'end_date', 'academy_name'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">تم تفعيل اشتراكك</h2>
                    <p>عزيزي {{ user_name }}، تم تفعيل اشتراكك في باقة {{ package_name }}.</p>
                    <p>يمكنك الآن البدء في حجز الحصص والاستفادة من خدماتنا.</p>
                </div></body></html>''',
            'body_text': 'تم تفعيل اشتراكك في باقة {{ package_name }}.'
        },

        # قالب إشعار الإدمن بدفعة جديدة
        {
            'name': 'admin_payment_notification',
            'display_name': 'إشعار الإدمن بدفعة جديدة',
            'subject': '💰 دفعة جديدة تحتاج مراجعة - {{ academy_name }}',
            'template_type': 'admin',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_phone', 'amount_formatted', 'payment_method', 'transaction_id', 'payment_date', 'package_name', 'academy_name', 'admin_panel_url', 'payment_status'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #ffc107;">💰 دفعة جديدة تحتاج مراجعة</h2>
                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <p><strong>تنبيه:</strong> تم استلام دفعة جديدة وتحتاج إلى مراجعة وتأكيد من الإدارة.</p>
                    </div>
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل الدفعة:</h3>
                        <p><strong>المبلغ:</strong> {{ amount_formatted }}</p>
                        <p><strong>طريقة الدفع:</strong> {{ payment_method }}</p>
                        <p><strong>رقم المعاملة:</strong> {{ transaction_id }}</p>
                        <p><strong>الباقة:</strong> {{ package_name }}</p>
                        <p><strong>العميل:</strong> {{ user_name }} ({{ user_email }})</p>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ admin_panel_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">مراجعة في لوحة الإدمن</a>
                    </div>
                </div></body></html>''',
            'body_text': 'دفعة جديدة تحتاج مراجعة. المبلغ: {{ amount_formatted }}، العميل: {{ user_name }}، الباقة: {{ package_name }}'
        },

        # قالب إشعار الإدمن بمستخدم جديد
        {
            'name': 'admin_new_user_notification',
            'display_name': 'إشعار الإدمن بمستخدم جديد',
            'subject': '👤 مستخدم جديد يحتاج مراجعة - {{ academy_name }}',
            'template_type': 'admin',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_phone', 'user_role', 'registration_date', 'academy_name', 'admin_panel_url', 'user_status'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #17a2b8;">👤 مستخدم جديد يحتاج مراجعة</h2>
                    <div style="background-color: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <p><strong>تنبيه:</strong> تم تسجيل مستخدم جديد ويحتاج إلى مراجعة وموافقة من الإدارة.</p>
                    </div>
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل المستخدم:</h3>
                        <p><strong>الاسم:</strong> {{ user_name }}</p>
                        <p><strong>البريد الإلكتروني:</strong> {{ user_email }}</p>
                        <p><strong>الهاتف:</strong> {{ user_phone }}</p>
                        <p><strong>نوع الحساب:</strong> {% if user_role == 'student' %}طالب{% elif user_role == 'teacher' %}معلم{% else %}{{ user_role }}{% endif %}</p>
                        <p><strong>تاريخ التسجيل:</strong> {{ registration_date }}</p>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ admin_panel_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">مراجعة في إدارة المستخدمين</a>
                    </div>
                </div></body></html>''',
            'body_text': 'مستخدم جديد يحتاج مراجعة. الاسم: {{ user_name }}، البريد: {{ user_email }}، النوع: {% if user_role == "student" %}طالب{% elif user_role == "teacher" %}معلم{% else %}{{ user_role }}{% endif %}'
        },

        # قالب إشعار إنشاء حصة تجريبية
        {
            'name': 'trial_session_created',
            'display_name': 'إشعار إنشاء حصة تجريبية',
            'subject': '🎯 تم جدولة حصة تجريبية لك - {{ academy_name }}',
            'template_type': 'session',
            'is_system': True,
            'variables': ['recipient_name', 'recipient_role', 'session_type_display', 'teacher_name', 'student_name', 'session_date', 'session_time', 'session_duration', 'session_notes', 'trial_instructions', 'academy_name', 'sessions_dashboard_url', 'academy_phone', 'academy_email'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">🎯 حصة تجريبية جديدة</h2>
                    <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل الحصة:</h3>
                        <p><strong>النوع:</strong> {{ session_type_display }}</p>
                        <p><strong>التاريخ:</strong> {{ session_date }}</p>
                        <p><strong>الوقت:</strong> {{ session_time }}</p>
                        <p><strong>المدة:</strong> {{ session_duration }} دقيقة</p>
                        {% if recipient_role == 'teacher' %}<p><strong>الطالب:</strong> {{ student_name }}</p>{% else %}<p><strong>المعلم:</strong> {{ teacher_name }}</p>{% endif %}
                    </div>
                    {% if trial_instructions %}<div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;"><h4>تعليمات الحصة التجريبية:</h4><p>{{ trial_instructions }}</p></div>{% endif %}
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ sessions_dashboard_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">عرض الحصص</a>
                    </div>
                </div></body></html>''',
            'body_text': '''حصة تجريبية جديدة - {{ academy_name }}

{% if recipient_role == 'teacher' %}عزيزي الأستاذ {{ recipient_name }}، تم جدولة حصة تجريبية لك مع الطالب {{ student_name }}.{% else %}عزيزي الطالب {{ recipient_name }}، تم جدولة حصة تجريبية لك مع الأستاذ {{ teacher_name }}.{% endif %}

تفاصيل الحصة: {{ session_type_display }} - {{ session_date }} {{ session_time }} ({{ session_duration }} دقيقة)
{% if trial_instructions %}تعليمات: {{ trial_instructions }}{% endif %}
رابط الحصص: {{ sessions_dashboard_url }}'''
        },

        # قالب إشعار إنشاء حصة تعويضية
        {
            'name': 'makeup_session_created',
            'display_name': 'إشعار إنشاء حصة تعويضية',
            'subject': '🔄 تم جدولة حصة تعويضية لك - {{ academy_name }}',
            'template_type': 'session',
            'is_system': True,
            'variables': ['recipient_name', 'recipient_role', 'session_type_display', 'teacher_name', 'student_name', 'session_date', 'session_time', 'session_duration', 'session_notes', 'makeup_reason', 'academy_name', 'sessions_dashboard_url', 'academy_phone', 'academy_email'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #ffc107;">🔄 حصة تعويضية جديدة</h2>
                    {% if makeup_reason %}<div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;"><h4>سبب الحصة التعويضية:</h4><p>{{ makeup_reason }}</p></div>{% endif %}
                    <div style="background-color: #fff8e1; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل الحصة الجديدة:</h3>
                        <p><strong>النوع:</strong> {{ session_type_display }}</p>
                        <p><strong>التاريخ:</strong> {{ session_date }}</p>
                        <p><strong>الوقت:</strong> {{ session_time }}</p>
                        <p><strong>المدة:</strong> {{ session_duration }} دقيقة</p>
                        {% if recipient_role == 'teacher' %}<p><strong>الطالب:</strong> {{ student_name }}</p>{% else %}<p><strong>المعلم:</strong> {{ teacher_name }}</p>{% endif %}
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ sessions_dashboard_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">عرض الحصص</a>
                    </div>
                </div></body></html>''',
            'body_text': '''حصة تعويضية جديدة - {{ academy_name }}

{% if recipient_role == 'teacher' %}عزيزي الأستاذ {{ recipient_name }}، تم جدولة حصة تعويضية لك مع الطالب {{ student_name }}.{% else %}عزيزي الطالب {{ recipient_name }}، تم جدولة حصة تعويضية لك مع الأستاذ {{ teacher_name }}.{% endif %}

{% if makeup_reason %}سبب الحصة التعويضية: {{ makeup_reason }}{% endif %}
تفاصيل الحصة: {{ session_type_display }} - {{ session_date }} {{ session_time }} ({{ session_duration }} دقيقة)
رابط الحصص: {{ sessions_dashboard_url }}'''
        }
    ]


def install_template(template_data):
    """تنصيب قالب واحد"""
    
    try:
        # فحص إذا كان القالب موجود
        existing_template = EmailTemplate.query.filter_by(name=template_data['name']).first()
        
        if existing_template:
            # تحديث القالب الموجود
            existing_template.display_name = template_data['display_name']
            existing_template.subject = template_data['subject']
            existing_template.body_html = template_data['body_html']
            existing_template.body_text = template_data['body_text']
            existing_template.template_type = template_data['template_type']
            existing_template.is_system = template_data['is_system']
            existing_template.set_variables_list(template_data['variables'])
            return 'updated'
        else:
            # إنشاء قالب جديد
            new_template = EmailTemplate(
                name=template_data['name'],
                display_name=template_data['display_name'],
                subject=template_data['subject'],
                body_html=template_data['body_html'],
                body_text=template_data['body_text'],
                template_type=template_data['template_type'],
                is_system=template_data['is_system'],
                is_active=True
            )
            new_template.set_variables_list(template_data['variables'])
            db.session.add(new_template)
            return 'created'
            
    except Exception as e:
        print(f"❌ خطأ في تنصيب القالب {template_data['name']}: {str(e)}")
        return 'error'


def auto_install_all_templates():
    """التنصيب التلقائي لجميع القوالب"""
    
    print("🚀 بدء التنصيب التلقائي لقوالب البريد الإلكتروني")
    print("=" * 60)
    
    try:
        templates = get_all_production_templates()
        
        created_count = 0
        updated_count = 0
        error_count = 0
        
        for template_data in templates:
            result = install_template(template_data)
            
            if result == 'created':
                created_count += 1
                print(f"✅ تم إنشاء: {template_data['display_name']}")
            elif result == 'updated':
                updated_count += 1
                print(f"🔄 تم تحديث: {template_data['display_name']}")
            else:
                error_count += 1
                print(f"❌ خطأ في: {template_data['display_name']}")
        
        # حفظ التغييرات
        db.session.commit()
        
        print(f"\n📊 نتائج التنصيب التلقائي:")
        print(f"   ✅ قوالب جديدة: {created_count}")
        print(f"   🔄 قوالب محدثة: {updated_count}")
        print(f"   ❌ أخطاء: {error_count}")
        print(f"   📧 المجموع: {len(templates)}")
        
        if error_count == 0:
            print(f"\n🎉 تم تنصيب جميع القوالب بنجاح!")
        else:
            print(f"\n⚠️ تم التنصيب مع بعض الأخطاء")
        
        return True
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في التنصيب التلقائي: {str(e)}")
        return False


def check_templates_installation():
    """فحص تنصيب القوالب"""
    
    try:
        templates = get_all_production_templates()
        required_templates = [t['name'] for t in templates]
        
        existing_templates = EmailTemplate.query.filter(
            EmailTemplate.name.in_(required_templates)
        ).all()
        
        existing_names = [t.name for t in existing_templates]
        missing_templates = [name for name in required_templates if name not in existing_names]
        
        print(f"📊 حالة القوالب:")
        print(f"   📧 مطلوب: {len(required_templates)}")
        print(f"   ✅ موجود: {len(existing_names)}")
        print(f"   ❌ مفقود: {len(missing_templates)}")
        
        if missing_templates:
            print(f"\n❌ القوالب المفقودة:")
            for template_name in missing_templates:
                print(f"   - {template_name}")
            return False
        else:
            print(f"\n✅ جميع القوالب موجودة!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص القوالب: {str(e)}")
        return False


if __name__ == "__main__":
    from app import app
    
    with app.app_context():
        # فحص القوالب الموجودة
        templates_ok = check_templates_installation()
        
        if not templates_ok:
            # تنصيب القوالب المفقودة
            auto_install_all_templates()
        else:
            print("ℹ️ جميع القوالب موجودة، لا حاجة للتنصيب")
