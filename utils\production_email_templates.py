"""
نظام التنصيب التلقائي لجميع قوالب البريد الإلكتروني في الإنتاج
يحتوي على جميع القوالب الـ 24 المطلوبة للنظام
"""

from models import EmailTemplate, db


def get_all_production_templates():
    """الحصول على جميع قوالب الإنتاج - جميع القوالب الـ 25"""
    
    # استيراد القوالب من الملفات الأخرى
    from utils.email_service import get_default_templates, get_user_management_templates
    
    # جمع جميع القوالب
    all_templates = []
    
    # 1. القوالب الأساسية (5 قوالب)
    default_templates = get_default_templates()
    all_templates.extend(default_templates)
    
    # 2. قوالب إدارة المستخدمين (8 قوالب)  
    user_management_templates = get_user_management_templates()
    all_templates.extend(user_management_templates)
    
    # 3. القوالب الإضافية (4 قوالب)
    additional_templates = get_additional_templates_list()
    all_templates.extend(additional_templates)
    
    # 4. قوالب الإنتاج الإضافية (7 قوالب)
    production_specific_templates = get_production_specific_templates()
    all_templates.extend(production_specific_templates)
    
    return all_templates


def get_additional_templates_list():
    """الحصول على القوالب الإضافية كقائمة (4 قوالب)"""
    return [
        {
            'name': 'payment_confirmation',
            'display_name': 'تأكيد الدفع',
            'subject': 'تأكيد استلام الدفعة - {{ academy_name }}',
            'template_type': 'payment',
            'is_system': True,
            'variables': ['user_name', 'amount', 'payment_date', 'package_name', 'academy_name'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px;">
                        <h2 style="color: #155724;">تأكيد استلام الدفعة</h2>
                        <p>عزيزي/عزيزتي {{ user_name }}،</p>
                        <p>نؤكد استلام دفعتك بنجاح:</p>
                        <ul>
                            <li><strong>المبلغ:</strong> {{ amount }} ريال</li>
                            <li><strong>التاريخ:</strong> {{ payment_date }}</li>
                            <li><strong>الباقة:</strong> {{ package_name }}</li>
                        </ul>
                        <p>شكراً لك على ثقتك في {{ academy_name }}</p>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،
            
            نؤكد استلام دفعتك بنجاح:
            
            المبلغ: {{ amount }} ريال
            التاريخ: {{ payment_date }}
            الباقة: {{ package_name }}
            
            شكراً لك على ثقتك في {{ academy_name }}
            '''
        },
        {
            'name': 'session_completed',
            'display_name': 'إشعار انتهاء الحصة',
            'subject': 'تم إنهاء حصتك مع {{ teacher_name }}',
            'template_type': 'notification',
            'is_system': True,
            'variables': ['user_name', 'teacher_name', 'session_date', 'session_duration', 'academy_name'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background-color: #cce5ff; border: 1px solid #99ccff; padding: 20px; border-radius: 5px;">
                        <h2 style="color: #0066cc;">تم إنهاء الحصة</h2>
                        <p>عزيزي/عزيزتي {{ user_name }}،</p>
                        <p>تم إنهاء حصتك بنجاح:</p>
                        <ul>
                            <li><strong>المعلم:</strong> {{ teacher_name }}</li>
                            <li><strong>التاريخ:</strong> {{ session_date }}</li>
                            <li><strong>المدة:</strong> {{ session_duration }} دقيقة</li>
                        </ul>
                        <p>نتمنى أن تكون الحصة مفيدة ونتطلع للقائك في الحصة القادمة.</p>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،
            
            تم إنهاء حصتك بنجاح:
            
            المعلم: {{ teacher_name }}
            التاريخ: {{ session_date }}
            المدة: {{ session_duration }} دقيقة
            
            نتمنى أن تكون الحصة مفيدة ونتطلع للقائك في الحصة القادمة.
            '''
        },
        {
            'name': 'session_reminder_1_day',
            'display_name': 'تذكير بالحصة قبل يوم',
            'subject': '📅 تذكير: حصتك غداً في {{ session_time }} - {{ academy_name }}',
            'template_type': 'reminder',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #ffc107; margin-bottom: 10px;">⏰ تذكير بحصتك غداً</h1>
                        </div>
                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نذكرك بأن لديك حصة مجدولة غداً. نرجو منك الاستعداد والحضور في الموعد المحدد.
                        </p>
                        <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="color: #856404; margin-top: 0;">📅 تفاصيل الحصة:</h3>
                            <p><strong>التاريخ:</strong> {{ session_date }} (غداً)</p>
                            <p><strong>الوقت:</strong> {{ session_time }}</p>
                            <p><strong>المدة:</strong> {{ session_duration }}</p>
                            <p><strong>المعلم:</strong> {{ teacher_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            نذكرك بأن لديك حصة مجدولة غداً. نرجو منك الاستعداد والحضور في الموعد المحدد.

            تفاصيل الحصة:
            التاريخ: {{ session_date }} (غداً)
            الوقت: {{ session_time }}
            المدة: {{ session_duration }}
            المعلم: {{ teacher_name }}

            مع أطيب التحيات،
            {{ academy_name }}
            '''
        },
        {
            'name': 'session_reminder_5_minutes',
            'display_name': 'تذكير بالحصة قبل 5 دقائق',
            'subject': '🔔 حصتك ستبدأ خلال 5 دقائق - {{ academy_name }}',
            'template_type': 'reminder',
            'is_system': True,
            'variables': ['user_name', 'session_date', 'session_time', 'session_duration', 'teacher_name', 'student_name', 'session_type', 'session_url', 'academy_name', 'academy_slogan', 'academy_email', 'academy_phone'],
            'body_html': '''
            <html>
            <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #dc3545; margin-bottom: 10px;">🚨 حصتك ستبدأ خلال 5 دقائق!</h1>
                        </div>
                        <p style="font-size: 18px; line-height: 1.6; text-align: center; color: #dc3545; font-weight: bold;">
                            عزيزي/عزيزتي {{ user_name }}،<br>
                            حصتك ستبدأ خلال 5 دقائق فقط!
                        </p>
                        <div style="background-color: #f8d7da; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="color: #721c24; margin-top: 0;">⏰ تفاصيل الحصة الآن:</h3>
                            <p><strong>الوقت:</strong> {{ session_time }} (خلال 5 دقائق)</p>
                            <p><strong>المدة:</strong> {{ session_duration }}</p>
                            <p><strong>المعلم:</strong> {{ teacher_name }}</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            ''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            🚨 حصتك ستبدأ خلال 5 دقائق فقط!

            تفاصيل الحصة:
            الوقت: {{ session_time }} (خلال 5 دقائق)
            المدة: {{ session_duration }}
            المعلم: {{ teacher_name }}

            {{ academy_name }}
            '''
        }
    ]


def get_production_specific_templates():
    """الحصول على قوالب الإنتاج الإضافية (8 قوالب)"""
    return [
        # قالب ترحيب التسجيل الجديد
        {
            'name': 'user_registration_welcome',
            'display_name': 'ترحيب بالمستخدم الجديد',
            'subject': '🎉 مرحباً بك في {{ academy_name }} - تم إنشاء حسابك بنجاح!',
            'template_type': 'auth',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_role', 'registration_date', 'academy_name', 'academy_email', 'academy_phone', 'academy_website', 'login_url', 'admin_approval_required'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">🎉 مرحباً بك في {{ academy_name }}!</h2>
                    <p>عزيزي {{ user_name }}، تم إنشاء حسابك بنجاح.</p>
                    {% if admin_approval_required %}
                    <p>سيتم مراجعة حسابك من قبل الإدارة وستصلك رسالة تأكيد فور الموافقة.</p>
                    {% else %}
                    <p>يمكنك الآن <a href="{{ login_url }}">تسجيل الدخول</a> والبدء في التعلم.</p>
                    {% endif %}
                </div></body></html>''',
            'body_text': 'مرحباً {{ user_name }}! تم إنشاء حسابك في {{ academy_name }} بنجاح.'
        },

        # قالب تفعيل الحساب لأول مرة (بعد التسجيل والموافقة الأولى)
        {
            'name': 'account_activation_notification',
            'display_name': 'تفعيل الحساب لأول مرة',
            'subject': '🎉 مرحباً بك! تم تفعيل حسابك - {{ academy_name }}',
            'template_type': 'auth',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_role', 'user_role_display', 'activation_date', 'admin_message', 'login_url', 'dashboard_url', 'packages_url', 'academy_name', 'academy_email', 'academy_phone', 'academy_website'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #28a745; margin-bottom: 10px;">🎉 مرحباً بك في {{ academy_name }}!</h1>
                            <p style="color: #6c757d; margin: 0;">تم تفعيل حسابك بنجاح</p>
                        </div>

                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نحن سعداء جداً لانضمامك إلى {{ academy_name }}! تم مراجعة طلب التسجيل الخاص بك والموافقة عليه.
                        </p>

                        <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #28a745;">
                            <h3 style="color: #155724; margin-top: 0; margin-bottom: 15px;">✅ تم تفعيل حسابك بنجاح!</h3>
                            <p><strong>البريد الإلكتروني:</strong> {{ user_email }}</p>
                            <p><strong>نوع الحساب:</strong> {{ user_role_display }}</p>
                            <p><strong>تاريخ التفعيل:</strong> {{ activation_date }}</p>
                        </div>

                        {% if admin_message %}
                        <div style="background-color: #e7f3ff; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #007bff;">
                            <h3 style="color: #004085; margin-top: 0; margin-bottom: 15px;">💬 رسالة من فريق الإدارة:</h3>
                            <p style="font-style: italic;">{{ admin_message }}</p>
                        </div>
                        {% endif %}

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{ login_url }}" style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold;">🚀 ابدأ رحلتك التعليمية</a>
                        </div>

                        {% if user_role == 'student' %}
                        <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #ffc107;">
                            <h3 style="color: #856404; margin-top: 0; margin-bottom: 15px;">📚 الخطوات التالية:</h3>
                            <ul style="margin: 0; padding-right: 20px;">
                                <li>تصفح الباقات التعليمية المتاحة</li>
                                <li>اختر الباقة المناسبة لك</li>
                                <li>ابدأ رحلتك في تعلم القرآن الكريم</li>
                            </ul>
                            <div style="text-align: center; margin-top: 15px;">
                                <a href="{{ packages_url }}" style="background-color: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;">عرض الباقات</a>
                            </div>
                        </div>
                        {% endif %}

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">📞 نحن هنا لمساعدتك:</h3>
                            <p><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            <p><strong>الهاتف:</strong> {{ academy_phone }}</p>
                            <p><strong>الموقع الإلكتروني:</strong> {{ academy_website }}</p>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <p style="color: #6c757d; font-size: 14px;">
                                نتطلع لرؤيتك تحقق أهدافك التعليمية معنا<br>
                                فريق {{ academy_name }}
                            </p>
                        </div>
                    </div>
                </div>
            </body></html>''',
            'body_text': '''مرحباً بك في {{ academy_name }}!

عزيزي/عزيزتي {{ user_name }}،

تم تفعيل حسابك بنجاح! نحن سعداء لانضمامك إلينا.

تفاصيل الحساب:
- البريد الإلكتروني: {{ user_email }}
- نوع الحساب: {{ user_role_display }}
- تاريخ التفعيل: {{ activation_date }}

{% if admin_message %}
رسالة من فريق الإدارة:
{{ admin_message }}
{% endif %}

يمكنك الآن تسجيل الدخول والبدء في استخدام خدماتنا:
{{ login_url }}

{% if user_role == 'student' %}
لا تنس تصفح الباقات التعليمية المتاحة:
{{ packages_url }}
{% endif %}

للتواصل معنا:
البريد الإلكتروني: {{ academy_email }}
الهاتف: {{ academy_phone }}

فريق {{ academy_name }}'''
        },

        # قالب إشعار شراء الباقة (محسن للإنتاج)
        {
            'name': 'subscription_purchased',
            'display_name': 'إشعار شراء الباقة',
            'subject': '🎉 تم شراء الباقة بنجاح - {{ package_name }} - {{ academy_name }}',
            'template_type': 'subscription',
            'is_system': True,
            'variables': ['user_name', 'package_name', 'package_price', 'package_price_formatted', 'payment_method', 'transaction_id', 'purchase_date', 'sessions_count', 'duration_days', 'approval_status', 'approval_message', 'academy_name', 'academy_email', 'academy_phone', 'academy_website'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #28a745; margin-bottom: 10px;">🎉 تم شراء الباقة بنجاح!</h1>
                            <p style="color: #6c757d; margin: 0;">{{ academy_name }}</p>
                        </div>

                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نشكرك على ثقتك في {{ academy_name }}! تم شراء الباقة بنجاح وتم استلام دفعتك.
                        </p>

                        <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #28a745;">
                            <h3 style="color: #155724; margin-top: 0; margin-bottom: 15px;">📦 تفاصيل الباقة:</h3>
                            <p><strong>اسم الباقة:</strong> {{ package_name }}</p>
                            <p><strong>السعر:</strong> {{ package_price_formatted }}</p>
                            <p><strong>عدد الحصص:</strong> {{ sessions_count }} حصة</p>
                            <p><strong>مدة الباقة:</strong> {{ duration_days }} يوم</p>
                            <p><strong>طريقة الدفع:</strong> {{ payment_method }}</p>
                            <p><strong>رقم المعاملة:</strong> {{ transaction_id }}</p>
                            <p><strong>تاريخ الشراء:</strong> {{ purchase_date }}</p>
                        </div>

                        <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #ffc107;">
                            <h3 style="color: #856404; margin-top: 0; margin-bottom: 15px;">⏳ حالة الاشتراك:</h3>
                            <p><strong>الحالة:</strong> {{ approval_status }}</p>
                            <p>{{ approval_message }}</p>
                        </div>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">📞 للتواصل معنا:</h3>
                            <p><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            <p><strong>الهاتف:</strong> {{ academy_phone }}</p>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <p style="color: #6c757d; font-size: 14px;">
                                شكراً لاختيارك {{ academy_name }}<br>
                                نتطلع لخدمتك وتقديم أفضل تجربة تعليمية لك
                            </p>
                        </div>
                    </div>
                </div>
            </body></html>''',
            'body_text': '''
            عزيزي/عزيزتي {{ user_name }}،

            تم شراء الباقة بنجاح!

            تفاصيل الباقة:
            - اسم الباقة: {{ package_name }}
            - السعر: {{ package_price_formatted }}
            - عدد الحصص: {{ sessions_count }} حصة
            - مدة الباقة: {{ duration_days }} يوم
            - طريقة الدفع: {{ payment_method }}
            - رقم المعاملة: {{ transaction_id }}
            - تاريخ الشراء: {{ purchase_date }}

            حالة الاشتراك: {{ approval_status }}
            {{ approval_message }}

            للتواصل معنا:
            البريد الإلكتروني: {{ academy_email }}
            الهاتف: {{ academy_phone }}

            شكراً لاختيارك {{ academy_name }}
            '''
        },

        # قالب إعادة تفعيل الحساب (بعد حظر أو تعليق)
        {
            'name': 'account_reactivation_notification',
            'display_name': 'إعادة تفعيل الحساب',
            'subject': '✅ تم إعادة تفعيل حسابك - {{ academy_name }}',
            'template_type': 'auth',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_role_display', 'activation_date', 'previous_status', 'admin_message', 'login_url', 'dashboard_url', 'academy_name', 'academy_email', 'academy_phone'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
                    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #28a745; margin-bottom: 10px;">✅ تم إعادة تفعيل حسابك</h1>
                            <p style="color: #6c757d; margin: 0;">{{ academy_name }}</p>
                        </div>

                        <p style="font-size: 16px; line-height: 1.6;">عزيزي/عزيزتي {{ user_name }}،</p>
                        <p style="font-size: 16px; line-height: 1.6;">
                            نحن سعداء لإبلاغك بأنه تم إعادة تفعيل حسابك بنجاح وأصبح نشطاً مرة أخرى.
                        </p>

                        <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #28a745;">
                            <h3 style="color: #155724; margin-top: 0; margin-bottom: 15px;">🔄 تفاصيل إعادة التفعيل:</h3>
                            <p><strong>البريد الإلكتروني:</strong> {{ user_email }}</p>
                            <p><strong>نوع الحساب:</strong> {{ user_role_display }}</p>
                            <p><strong>تاريخ إعادة التفعيل:</strong> {{ activation_date }}</p>
                            <p><strong>الحالة السابقة:</strong> {{ previous_status }}</p>
                        </div>

                        {% if admin_message %}
                        <div style="background-color: #e7f3ff; padding: 20px; border-radius: 8px; margin: 25px 0; border-right: 4px solid #007bff;">
                            <h3 style="color: #004085; margin-top: 0; margin-bottom: 15px;">💬 رسالة من فريق الإدارة:</h3>
                            <p style="font-style: italic;">{{ admin_message }}</p>
                        </div>
                        {% endif %}

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{ login_url }}" style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold;">🔑 تسجيل الدخول الآن</a>
                        </div>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
                            <h3 style="color: #495057; margin-top: 0; margin-bottom: 15px;">📞 للتواصل معنا:</h3>
                            <p><strong>البريد الإلكتروني:</strong> {{ academy_email }}</p>
                            <p><strong>الهاتف:</strong> {{ academy_phone }}</p>
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <p style="color: #6c757d; font-size: 14px;">
                                نتطلع لاستمرار رحلتك التعليمية معنا<br>
                                فريق {{ academy_name }}
                            </p>
                        </div>
                    </div>
                </div>
            </body></html>''',
            'body_text': '''
            تم إعادة تفعيل حسابك - {{ academy_name }}

            عزيزي/عزيزتي {{ user_name }}،

            تم إعادة تفعيل حسابك بنجاح وأصبح نشطاً مرة أخرى.

            تفاصيل إعادة التفعيل:
            - البريد الإلكتروني: {{ user_email }}
            - نوع الحساب: {{ user_role_display }}
            - تاريخ إعادة التفعيل: {{ activation_date }}
            - الحالة السابقة: {{ previous_status }}

            {% if admin_message %}
            رسالة من فريق الإدارة:
            {{ admin_message }}
            {% endif %}

            يمكنك الآن تسجيل الدخول:
            {{ login_url }}

            للتواصل معنا:
            البريد الإلكتروني: {{ academy_email }}
            الهاتف: {{ academy_phone }}

            فريق {{ academy_name }}
            '''
        },

        # قالب تفعيل الاشتراك
        {
            'name': 'subscription_activated',
            'display_name': 'تفعيل الاشتراك',
            'subject': 'تم تفعيل اشتراكك - {{ package_name }}',
            'template_type': 'subscription',
            'is_system': True,
            'variables': ['user_name', 'package_name', 'sessions_count', 'start_date', 'end_date', 'academy_name'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">تم تفعيل اشتراكك</h2>
                    <p>عزيزي {{ user_name }}، تم تفعيل اشتراكك في باقة {{ package_name }}.</p>
                    <p>يمكنك الآن البدء في حجز الحصص والاستفادة من خدماتنا.</p>
                </div></body></html>''',
            'body_text': 'تم تفعيل اشتراكك في باقة {{ package_name }}.'
        },

        # قالب إشعار الإدمن بدفعة جديدة
        {
            'name': 'admin_payment_notification',
            'display_name': 'إشعار الإدمن بدفعة جديدة',
            'subject': '💰 دفعة جديدة تحتاج مراجعة - {{ academy_name }}',
            'template_type': 'admin',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_phone', 'amount_formatted', 'payment_method', 'transaction_id', 'payment_date', 'package_name', 'academy_name', 'admin_panel_url', 'payment_status'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #ffc107;">💰 دفعة جديدة تحتاج مراجعة</h2>
                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <p><strong>تنبيه:</strong> تم استلام دفعة جديدة وتحتاج إلى مراجعة وتأكيد من الإدارة.</p>
                    </div>
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل الدفعة:</h3>
                        <p><strong>المبلغ:</strong> {{ amount_formatted }}</p>
                        <p><strong>طريقة الدفع:</strong> {{ payment_method }}</p>
                        <p><strong>رقم المعاملة:</strong> {{ transaction_id }}</p>
                        <p><strong>الباقة:</strong> {{ package_name }}</p>
                        <p><strong>العميل:</strong> {{ user_name }} ({{ user_email }})</p>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ admin_panel_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">مراجعة في لوحة الإدمن</a>
                    </div>
                </div></body></html>''',
            'body_text': 'دفعة جديدة تحتاج مراجعة. المبلغ: {{ amount_formatted }}، العميل: {{ user_name }}، الباقة: {{ package_name }}'
        },

        # قالب إشعار الإدمن بمستخدم جديد
        {
            'name': 'admin_new_user_notification',
            'display_name': 'إشعار الإدمن بمستخدم جديد',
            'subject': '👤 مستخدم جديد يحتاج مراجعة - {{ academy_name }}',
            'template_type': 'admin',
            'is_system': True,
            'variables': ['user_name', 'user_email', 'user_phone', 'user_role', 'registration_date', 'academy_name', 'admin_panel_url', 'user_status'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #17a2b8;">👤 مستخدم جديد يحتاج مراجعة</h2>
                    <div style="background-color: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;">
                        <p><strong>تنبيه:</strong> تم تسجيل مستخدم جديد ويحتاج إلى مراجعة وموافقة من الإدارة.</p>
                    </div>
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل المستخدم:</h3>
                        <p><strong>الاسم:</strong> {{ user_name }}</p>
                        <p><strong>البريد الإلكتروني:</strong> {{ user_email }}</p>
                        <p><strong>الهاتف:</strong> {{ user_phone }}</p>
                        <p><strong>نوع الحساب:</strong> {% if user_role == 'student' %}طالب{% elif user_role == 'teacher' %}معلم{% else %}{{ user_role }}{% endif %}</p>
                        <p><strong>تاريخ التسجيل:</strong> {{ registration_date }}</p>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ admin_panel_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">مراجعة في إدارة المستخدمين</a>
                    </div>
                </div></body></html>''',
            'body_text': 'مستخدم جديد يحتاج مراجعة. الاسم: {{ user_name }}، البريد: {{ user_email }}، النوع: {% if user_role == "student" %}طالب{% elif user_role == "teacher" %}معلم{% else %}{{ user_role }}{% endif %}'
        },

        # قالب إشعار إنشاء حصة تجريبية
        {
            'name': 'trial_session_created',
            'display_name': 'إشعار إنشاء حصة تجريبية',
            'subject': '🎯 تم جدولة حصة تجريبية لك - {{ academy_name }}',
            'template_type': 'session',
            'is_system': True,
            'variables': ['recipient_name', 'recipient_role', 'session_type_display', 'teacher_name', 'student_name', 'session_date', 'session_time', 'session_duration', 'session_notes', 'trial_instructions', 'academy_name', 'sessions_dashboard_url', 'academy_phone', 'academy_email'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">🎯 حصة تجريبية جديدة</h2>
                    <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل الحصة:</h3>
                        <p><strong>النوع:</strong> {{ session_type_display }}</p>
                        <p><strong>التاريخ:</strong> {{ session_date }}</p>
                        <p><strong>الوقت:</strong> {{ session_time }}</p>
                        <p><strong>المدة:</strong> {{ session_duration }} دقيقة</p>
                        {% if recipient_role == 'teacher' %}<p><strong>الطالب:</strong> {{ student_name }}</p>{% else %}<p><strong>المعلم:</strong> {{ teacher_name }}</p>{% endif %}
                    </div>
                    {% if trial_instructions %}<div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;"><h4>تعليمات الحصة التجريبية:</h4><p>{{ trial_instructions }}</p></div>{% endif %}
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ sessions_dashboard_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">عرض الحصص</a>
                    </div>
                </div></body></html>''',
            'body_text': '''حصة تجريبية جديدة - {{ academy_name }}

{% if recipient_role == 'teacher' %}عزيزي الأستاذ {{ recipient_name }}، تم جدولة حصة تجريبية لك مع الطالب {{ student_name }}.{% else %}عزيزي الطالب {{ recipient_name }}، تم جدولة حصة تجريبية لك مع الأستاذ {{ teacher_name }}.{% endif %}

تفاصيل الحصة: {{ session_type_display }} - {{ session_date }} {{ session_time }} ({{ session_duration }} دقيقة)
{% if trial_instructions %}تعليمات: {{ trial_instructions }}{% endif %}
رابط الحصص: {{ sessions_dashboard_url }}'''
        },

        # قالب إشعار إنشاء حصة تعويضية
        {
            'name': 'makeup_session_created',
            'display_name': 'إشعار إنشاء حصة تعويضية',
            'subject': '🔄 تم جدولة حصة تعويضية لك - {{ academy_name }}',
            'template_type': 'session',
            'is_system': True,
            'variables': ['recipient_name', 'recipient_role', 'session_type_display', 'teacher_name', 'student_name', 'session_date', 'session_time', 'session_duration', 'session_notes', 'makeup_reason', 'academy_name', 'sessions_dashboard_url', 'academy_phone', 'academy_email'],
            'body_html': '''<html><body style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #ffc107;">🔄 حصة تعويضية جديدة</h2>
                    {% if makeup_reason %}<div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;"><h4>سبب الحصة التعويضية:</h4><p>{{ makeup_reason }}</p></div>{% endif %}
                    <div style="background-color: #fff8e1; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3>تفاصيل الحصة الجديدة:</h3>
                        <p><strong>النوع:</strong> {{ session_type_display }}</p>
                        <p><strong>التاريخ:</strong> {{ session_date }}</p>
                        <p><strong>الوقت:</strong> {{ session_time }}</p>
                        <p><strong>المدة:</strong> {{ session_duration }} دقيقة</p>
                        {% if recipient_role == 'teacher' %}<p><strong>الطالب:</strong> {{ student_name }}</p>{% else %}<p><strong>المعلم:</strong> {{ teacher_name }}</p>{% endif %}
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <a href="{{ sessions_dashboard_url }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">عرض الحصص</a>
                    </div>
                </div></body></html>''',
            'body_text': '''حصة تعويضية جديدة - {{ academy_name }}

{% if recipient_role == 'teacher' %}عزيزي الأستاذ {{ recipient_name }}، تم جدولة حصة تعويضية لك مع الطالب {{ student_name }}.{% else %}عزيزي الطالب {{ recipient_name }}، تم جدولة حصة تعويضية لك مع الأستاذ {{ teacher_name }}.{% endif %}

{% if makeup_reason %}سبب الحصة التعويضية: {{ makeup_reason }}{% endif %}
تفاصيل الحصة: {{ session_type_display }} - {{ session_date }} {{ session_time }} ({{ session_duration }} دقيقة)
رابط الحصص: {{ sessions_dashboard_url }}'''
        }
    ]


def install_template(template_data):
    """تنصيب قالب واحد"""
    
    try:
        # فحص إذا كان القالب موجود
        existing_template = EmailTemplate.query.filter_by(name=template_data['name']).first()
        
        if existing_template:
            # تحديث القالب الموجود
            existing_template.display_name = template_data['display_name']
            existing_template.subject = template_data['subject']
            existing_template.body_html = template_data['body_html']
            existing_template.body_text = template_data['body_text']
            existing_template.template_type = template_data['template_type']
            existing_template.is_system = template_data['is_system']
            existing_template.set_variables_list(template_data['variables'])
            return 'updated'
        else:
            # إنشاء قالب جديد
            new_template = EmailTemplate(
                name=template_data['name'],
                display_name=template_data['display_name'],
                subject=template_data['subject'],
                body_html=template_data['body_html'],
                body_text=template_data['body_text'],
                template_type=template_data['template_type'],
                is_system=template_data['is_system'],
                is_active=True
            )
            new_template.set_variables_list(template_data['variables'])
            db.session.add(new_template)
            return 'created'
            
    except Exception as e:
        print(f"❌ خطأ في تنصيب القالب {template_data['name']}: {str(e)}")
        return 'error'


def auto_install_all_templates():
    """التنصيب التلقائي لجميع القوالب"""
    
    print("🚀 بدء التنصيب التلقائي لقوالب البريد الإلكتروني")
    print("=" * 60)
    
    try:
        templates = get_all_production_templates()
        
        created_count = 0
        updated_count = 0
        error_count = 0
        
        for template_data in templates:
            result = install_template(template_data)
            
            if result == 'created':
                created_count += 1
                print(f"✅ تم إنشاء: {template_data['display_name']}")
            elif result == 'updated':
                updated_count += 1
                print(f"🔄 تم تحديث: {template_data['display_name']}")
            else:
                error_count += 1
                print(f"❌ خطأ في: {template_data['display_name']}")
        
        # حفظ التغييرات
        db.session.commit()
        
        print(f"\n📊 نتائج التنصيب التلقائي:")
        print(f"   ✅ قوالب جديدة: {created_count}")
        print(f"   🔄 قوالب محدثة: {updated_count}")
        print(f"   ❌ أخطاء: {error_count}")
        print(f"   📧 المجموع: {len(templates)}")
        
        if error_count == 0:
            print(f"\n🎉 تم تنصيب جميع القوالب بنجاح!")
        else:
            print(f"\n⚠️ تم التنصيب مع بعض الأخطاء")
        
        return True
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في التنصيب التلقائي: {str(e)}")
        return False


def check_templates_installation():
    """فحص تنصيب القوالب"""
    
    try:
        templates = get_all_production_templates()
        required_templates = [t['name'] for t in templates]
        
        existing_templates = EmailTemplate.query.filter(
            EmailTemplate.name.in_(required_templates)
        ).all()
        
        existing_names = [t.name for t in existing_templates]
        missing_templates = [name for name in required_templates if name not in existing_names]
        
        print(f"📊 حالة القوالب:")
        print(f"   📧 مطلوب: {len(required_templates)}")
        print(f"   ✅ موجود: {len(existing_names)}")
        print(f"   ❌ مفقود: {len(missing_templates)}")
        
        if missing_templates:
            print(f"\n❌ القوالب المفقودة:")
            for template_name in missing_templates:
                print(f"   - {template_name}")
            return False
        else:
            print(f"\n✅ جميع القوالب موجودة!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص القوالب: {str(e)}")
        return False


if __name__ == "__main__":
    from app import app
    
    with app.app_context():
        # فحص القوالب الموجودة
        templates_ok = check_templates_installation()
        
        if not templates_ok:
            # تنصيب القوالب المفقودة
            auto_install_all_templates()
        else:
            print("ℹ️ جميع القوالب موجودة، لا حاجة للتنصيب")
