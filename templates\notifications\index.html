{% extends "base.html" %}

{% block title %}الإشعارات - {{ super() }}{% endblock %}

{% block page_title %}الإشعارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Header with Actions -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h6 class="mb-0">
                            <i class="fas fa-bell me-2"></i>إشعاراتي
                            {% if unread_count > 0 %}
                                <span class="badge bg-danger ms-2">{{ unread_count }} غير مقروء</span>
                            {% endif %}
                        </h6>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                                <i class="fas fa-check-double me-1"></i>تحديد الكل كمقروء
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteSelected()">
                                <i class="fas fa-trash me-1"></i>حذف المحدد
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteAll()">
                                <i class="fas fa-trash-alt me-1"></i>حذف الكل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card-body border-bottom">
                <form method="GET" action="{{ url_for('notifications.filter_notifications') }}" class="row g-3">
                    <div class="col-md-4">
                        <label for="type" class="form-label">نوع الإشعار</label>
                        <select class="form-select" id="type" name="type" onchange="this.form.submit()">
                            <option value="all" {% if current_type == 'all' %}selected{% endif %}>جميع الأنواع</option>
                            {% if available_types %}
                                {% for type in available_types %}
                                <option value="{{ type }}" {% if current_type == type %}selected{% endif %}>
                                    {% if type == 'session' %}الحصص
                                    {% elif type == 'payment' %}المدفوعات
                                    {% elif type == 'subscription' %}الاشتراكات
                                    {% elif type == 'user_management' %}إدارة المستخدمين
                                    {% elif type == 'system' %}النظام
                                    {% else %}{{ type }}{% endif %}
                                </option>
                                {% endfor %}
                            {% endif %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                            <option value="all" {% if current_status == 'all' %}selected{% endif %}>جميع الإشعارات</option>
                            <option value="unread" {% if current_status == 'unread' %}selected{% endif %}>غير مقروءة</option>
                            <option value="read" {% if current_status == 'read' %}selected{% endif %}>مقروءة</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="card mt-3">
            <div class="card-body p-0">
                {% if notifications.items %}
                    <!-- Select All Checkbox -->
                    <div class="p-3 border-bottom bg-light">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            <label class="form-check-label" for="selectAll">
                                تحديد الكل ({{ notifications.total }} إشعار)
                            </label>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <div class="list-group list-group-flush">
                        {% for notification in notifications.items %}
                        <div class="list-group-item {% if not notification.is_read %}bg-light border-start border-primary border-3{% endif %}">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <input class="form-check-input notification-checkbox" type="checkbox" 
                                           value="{{ notification.id }}" name="notification_ids">
                                </div>
                                <div class="col-auto">
                                    <div class="notification-icon {{ notification.get_priority_class() }}">
                                        <i class="{{ notification.get_icon_class() }} fa-lg"></i>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 {% if not notification.is_read %}fw-bold{% endif %}">
                                                {{ notification.title }}
                                                {% if not notification.is_read %}
                                                    <span class="badge bg-primary ms-2">جديد</span>
                                                {% endif %}
                                            </h6>
                                            <p class="mb-1 text-muted">{{ notification.message }}</p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>{{ notification.get_time_ago() }}
                                                {% if notification.notification_type %}
                                                    <span class="mx-2">•</span>
                                                    <span class="badge bg-secondary">
                                                        {% if notification.notification_type == 'session' %}حصة
                                                        {% elif notification.notification_type == 'payment' %}دفع
                                                        {% elif notification.notification_type == 'subscription' %}اشتراك
                                                        {% elif notification.notification_type == 'user_management' %}إدارة
                                                        {% elif notification.notification_type == 'system' %}نظام
                                                        {% else %}{{ notification.notification_type }}{% endif %}
                                                    </span>
                                                {% endif %}
                                            </small>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                    type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                {% if notification.action_url %}
                                                <li>
                                                    <a class="dropdown-item" href="{{ url_for('notifications.view_notification', notification_id=notification.id) }}">
                                                        <i class="fas fa-eye me-2"></i>عرض
                                                    </a>
                                                </li>
                                                {% endif %}
                                                {% if not notification.is_read %}
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="markAsRead({{ notification.id }})">
                                                        <i class="fas fa-check me-2"></i>تحديد كمقروء
                                                    </a>
                                                </li>
                                                {% endif %}
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteNotification({{ notification.id }})">
                                                        <i class="fas fa-trash me-2"></i>حذف
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Pagination -->
                    {% if notifications.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="تصفح الإشعارات">
                            <ul class="pagination justify-content-center mb-0">
                                {% if notifications.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('notifications.filter_notifications', page=notifications.prev_num, type=current_type, status=current_status) }}">
                                        السابق
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in notifications.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != notifications.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('notifications.filter_notifications', page=page_num, type=current_type, status=current_status) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if notifications.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('notifications.filter_notifications', page=notifications.next_num, type=current_type, status=current_status) }}">
                                        التالي
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                {% else %}
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إشعارات</h5>
                        <p class="text-muted">
                            {% if current_type != 'all' or current_status != 'all' %}
                                لا توجد إشعارات تطابق الفلاتر المحددة.
                            {% else %}
                                لم تتلق أي إشعارات بعد.
                            {% endif %}
                        </p>
                        {% if current_type != 'all' or current_status != 'all' %}
                        <button type="button" class="btn btn-outline-primary" onclick="clearFilters()">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </button>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديد/إلغاء تحديد جميع الإشعارات
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.notification-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// جلب الإشعارات المحددة
function getSelectedNotifications() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// تحديد إشعار واحد كمقروء
function markAsRead(notificationId) {
    fetch(`/notifications/mark-read/${notificationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    if (confirm('هل تريد تحديد جميع الإشعارات كمقروءة؟')) {
        fetch('/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}

// حذف إشعار واحد
function deleteNotification(notificationId) {
    if (confirm('هل تريد حذف هذا الإشعار؟')) {
        fetch(`/notifications/delete/${notificationId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}

// حذف الإشعارات المحددة
function deleteSelected() {
    const selected = getSelectedNotifications();
    
    if (selected.length === 0) {
        alert('يرجى تحديد إشعارات للحذف');
        return;
    }
    
    if (confirm(`هل تريد حذف ${selected.length} إشعار؟`)) {
        fetch('/notifications/api/delete-bulk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                notification_ids: selected
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}

// حذف جميع الإشعارات
function deleteAll() {
    if (confirm('هل تريد حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('/notifications/delete-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}

// مسح الفلاتر
function clearFilters() {
    window.location.href = '{{ url_for("notifications.index") }}';
}

// تحديث عداد الإشعارات في القائمة العلوية
function updateNotificationCount() {
    fetch('/notifications/api/count')
    .then(response => response.json())
    .then(data => {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (data.count > 0) {
                badge.textContent = data.count;
                badge.style.display = 'inline';
            } else {
                badge.style.display = 'none';
            }
        }
    });
}

// تحديث العداد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateNotificationCount);
</script>
{% endblock %}
