{% extends "base.html" %}

{% block title %}حصصي - {{ academy_name }}{% endblock %}
{% block page_title %}حصصي{% endblock %}

{% block content %}
<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">حالة الحصة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="scheduled" {{ 'selected' if status_filter == 'scheduled' }}>مجدولة</option>
                            <option value="completed" {{ 'selected' if status_filter == 'completed' }}>مكتملة</option>
                            <option value="cancelled" {{ 'selected' if status_filter == 'cancelled' }}>ملغية</option>
                            <option value="missed" {{ 'selected' if status_filter == 'missed' }}>فائتة</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{{ url_for('student.sessions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إزالة الفلاتر
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Sessions List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>قائمة الحصص
                </h5>
                <span class="badge bg-primary">{{ sessions.total }} حصة</span>
            </div>
            <div class="card-body">
                {% if sessions.items %}
                    {% for session in sessions.items %}
                    <div class="card mb-3 session-card {{ 'border-success' if session.status == 'completed' else 'border-primary' if session.status == 'scheduled' else 'border-danger' }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">{{ session.teacher.full_name }}</h6>
                                <small class="text-muted">
                                    <i class="fas fa-tag me-1"></i>
                                    {% if session.session_type == 'trial' %}حصة تجريبية
                                    {% elif session.session_type == 'makeup' %}حصة تعويضية
                                    {% else %}حصة مجدولة{% endif %}
                                </small>
                            </div>
                            <span class="badge {{ 'bg-success' if session.status == 'completed' else 'bg-primary' if session.status == 'scheduled' else 'bg-danger' }}">
                                {% if session.status == 'completed' %}مكتملة
                                {% elif session.status == 'scheduled' %}مجدولة
                                {% elif session.status == 'cancelled' %}ملغية
                                {% else %}فائتة{% endif %}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-calendar text-primary me-2"></i>
                                        <span>{{ session.scheduled_datetime.strftime('%Y-%m-%d') }}</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        <span>{{ session.scheduled_datetime.strftime('%H:%M') }}</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-hourglass-half text-primary me-2"></i>
                                        <span>{{ session.duration_minutes }} دقيقة</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    {% if session.status == 'completed' %}
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-user-check text-success me-2"></i>
                                        <span>حضور الطالب: {{ 'نعم' if session.student_attended else 'لا' }}</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-chalkboard-teacher text-success me-2"></i>
                                        <span>حضور المعلم: {{ 'نعم' if session.teacher_attended else 'لا' }}</span>
                                    </div>
                                    {% if session.completed_at %}
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>اكتملت في: {{ session.completed_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                    </div>
                                    {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if session.notes %}
                            <div class="mt-3">
                                <h6>ملاحظات المعلم:</h6>
                                <p class="text-muted">{{ session.notes }}</p>
                            </div>
                            {% endif %}
                            
                            <div class="mt-3 d-flex gap-2">
                                <a href="{{ url_for('student.session_details', session_id=session.id) }}"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>التفاصيل
                                </a>

                                {% if session.status == 'scheduled' and session.meeting_link %}
                                    <small class="text-muted d-block mt-1">
                                        <i class="fas fa-info-circle me-1"></i>{{ session.get_meeting_provider_name() }}
                                    </small>
                                {% endif %}
                                
                                {% if session.status == 'completed' %}
                                    {% set existing_rating = session.ratings|selectattr('rater_id', 'equalto', current_user.id)|first %}
                                    {% if not existing_rating %}
                                    <button type="button" class="btn btn-warning btn-sm" 
                                            data-bs-toggle="modal" data-bs-target="#ratingModal{{ session.id }}">
                                        <i class="fas fa-star me-1"></i>تقييم الحصة
                                    </button>
                                    {% else %}
                                    <span class="badge bg-warning">
                                        <i class="fas fa-star me-1"></i>تم التقييم: {{ existing_rating.rating }}/5
                                    </span>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- Pagination -->
                    {% if sessions.pages > 1 %}
                    <nav aria-label="صفحات الحصص">
                        <ul class="pagination justify-content-center">
                            {% if sessions.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('student.sessions', page=sessions.prev_num, status=status_filter) }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in sessions.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != sessions.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('student.sessions', page=page_num, status=status_filter) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if sessions.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('student.sessions', page=sessions.next_num, status=status_filter) }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد حصص</h5>
                        <p class="text-muted">لم يتم جدولة أي حصص لك بعد.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Rating Modals -->
{% for session in sessions.items %}
{% if session.status == 'completed' %}
<div class="modal fade" id="ratingModal{{ session.id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم الحصة مع {{ session.teacher.full_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('student.rate_session', session_id=session.id) }}">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <div class="mb-3">
                        <label class="form-label">التقييم (من 1 إلى 5 نجوم)</label>
                        <div class="rating-stars" data-rating="0">
                            {% for i in range(1, 6) %}
                            <i class="fas fa-star star" data-value="{{ i }}"></i>
                            {% endfor %}
                        </div>
                        <input type="hidden" name="rating" id="rating{{ session.id }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="comment{{ session.id }}" class="form-label">تعليق (اختياري)</label>
                        <textarea class="form-control" id="comment{{ session.id }}" name="comment" 
                                  rows="3" placeholder="شاركنا رأيك في الحصة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إرسال التقييم</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}

{% block extra_css %}
<style>
    .session-card {
        transition: all 0.3s ease;
    }
    
    .session-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .rating-stars {
        font-size: 1.5rem;
        color: #ddd;
        cursor: pointer;
    }
    
    .rating-stars .star {
        transition: color 0.2s ease;
    }
    
    .rating-stars .star:hover,
    .rating-stars .star.active {
        color: #ffc107;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.getElementById('status').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Rating stars functionality
    document.querySelectorAll('.rating-stars').forEach(ratingContainer => {
        const stars = ratingContainer.querySelectorAll('.star');
        const sessionId = ratingContainer.closest('.modal').id.replace('ratingModal', '');
        const hiddenInput = document.getElementById('rating' + sessionId);
        
        stars.forEach((star, index) => {
            star.addEventListener('click', function() {
                const rating = index + 1;
                hiddenInput.value = rating;
                ratingContainer.setAttribute('data-rating', rating);
                
                stars.forEach((s, i) => {
                    if (i < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
            
            star.addEventListener('mouseenter', function() {
                const rating = index + 1;
                stars.forEach((s, i) => {
                    if (i < rating) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        ratingContainer.addEventListener('mouseleave', function() {
            const currentRating = parseInt(ratingContainer.getAttribute('data-rating'));
            stars.forEach((s, i) => {
                if (i < currentRating) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });
    
    // Add animation to session cards
    document.querySelectorAll('.session-card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.5s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
</script>
{% endblock %}
