<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - أكاديمية القرآن الكريم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome - Latest Version -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" crossorigin="anonymous">

    <!-- Bootstrap Icons as Fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        /* Tajawal Font Application */
        * {
            font-family: 'Tajawal', sans-serif !important;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 700;
        }

        .btn {
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 500;
        }

        .form-control, .form-label {
            font-family: 'Tajawal', sans-serif !important;
        }

        .form-label {
            font-weight: 500;
        }

        /* Icon Fixes */
        .fas, .far, .fab, .fal, .fad, .fass, .fasr, .fasl {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands" !important;
            font-weight: 900;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }

        i[class*="fa-"],
        i[class*="bi-"],
        .fa, .fas, .far, .fab {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands", "bootstrap-icons" !important;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-form {
            padding: 3rem;
        }
        
        .login-image {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            text-align: center;
            padding: 3rem;
        }
        
        .login-image i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        
        .form-floating label {
            color: #6c757d;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 10px;
            }
            
            .login-form {
                padding: 2rem;
            }
            
            .login-image {
                padding: 2rem;
            }
            
            .login-image i {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Form -->
                <div class="col-lg-6">
                    <div class="login-form">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">تسجيل الدخول</h2>
                            <p class="text-muted">مرحباً بك في أكاديمية القرآن الكريم</p>
                        </div>
                        
                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <div class="form-floating mb-3">
                                <input type="email" class="form-control" id="email" name="email" placeholder="البريد الإلكتروني" required>
                                <label for="email"><i class="fas fa-envelope me-2"></i>البريد الإلكتروني</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                                <label for="password"><i class="fas fa-lock me-2"></i>كلمة المرور</label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </button>
                            
                            <div class="text-center">
                                <a href="{{ url_for('auth.forgot_password') }}" class="text-decoration-none">نسيت كلمة المرور؟</a>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-0">ليس لديك حساب؟</p>
                                <a href="{{ url_for('auth.register') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus me-2"></i>إنشاء حساب جديد
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Login Image -->
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="login-image">
                        <i class="fas fa-quran-book"></i>
                        <h3 class="fw-bold mb-3">{{ academy_name }}</h3>
                        <p class="lead">{{ academy_slogan or 'منصة تعليمية متكاملة لتعلم وتحفيظ القرآن الكريم' }}</p>
                        <div class="mt-4">
                            <div class="d-flex justify-content-center gap-3">
                                <div class="text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <p class="small">معلمون مؤهلون</p>
                                </div>
                                <div class="text-center">
                                    <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                    <p class="small">جدولة مرنة</p>
                                </div>
                                <div class="text-center">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <p class="small">تتبع التقدم</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
            }
        });
    </script>
</body>
</html>
