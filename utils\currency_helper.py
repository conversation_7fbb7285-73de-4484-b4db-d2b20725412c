"""
مساعد تنسيق العملة
"""

def get_currency_symbol(currency_code):
    """جلب رمز العملة"""
    currency_symbols = {
        'SAR': 'ر.س',
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'AED': 'د.إ',
        'EGP': 'ج.م',
        'JOD': 'د.أ',
        'KWD': 'د.ك',
        'QAR': 'ر.ق',
        'BHD': 'د.ب',
        'OMR': 'ر.ع'
    }
    return currency_symbols.get(currency_code, currency_code)

def format_currency(amount, currency_code='SAR'):
    """تنسيق المبلغ مع العملة"""
    try:
        amount = float(amount)
        symbol = get_currency_symbol(currency_code)
        return f"{amount:,.2f} {symbol}"
    except:
        return f"{amount} {currency_code}"

def get_system_currency():
    """جلب العملة من إعدادات النظام"""
    try:
        from models import AcademySettings
        settings = AcademySettings.query.first()
        if settings and settings.currency:
            return settings.currency
        return 'SAR'
    except:
        return 'SAR'
