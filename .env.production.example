# Production Environment Configuration
# Copy this file to .env and update with your production values

# Flask Configuration
SECRET_KEY=your-super-secret-production-key-here
DATABASE_URL=postgresql://username:password@localhost/quranlms_production

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Stripe Configuration (Production)
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# PayPal Configuration (Production)
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_MODE=live

# Application Settings
FLASK_ENV=production
FLASK_DEBUG=False

# Security Settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# Domain Configuration
DOMAIN_NAME=yourdomain.com
