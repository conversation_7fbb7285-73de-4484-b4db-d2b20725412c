from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedel<PERSON>

# Initialize db here to avoid circular imports
db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20))
    role = db.Column(db.String(20), nullable=False, default='student')  # admin, teacher, student
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, approved, rejected, suspended, banned, inactive, deleted
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    profile_image = db.Column(db.String(255))

    # New fields for user management
    suspension_end_date = db.Column(db.DateTime)  # For temporary suspension
    ban_reason = db.Column(db.Text)  # Reason for ban/suspension
    deleted_at = db.Column(db.DateTime)  # Soft delete timestamp
    can_be_restored = db.Column(db.Boolean, default=True)  # Whether deleted account can be restored

    # Password reset fields
    reset_token = db.Column(db.String(255))
    reset_token_expiry = db.Column(db.DateTime)
    
    # Relationships
    subscriptions = db.relationship('Subscription', backref='user', lazy=True)
    teacher_sessions = db.relationship('Session', foreign_keys='Session.teacher_id', backref='teacher', lazy=True)
    student_sessions = db.relationship('Session', foreign_keys='Session.student_id', backref='student', lazy=True)
    notifications = db.relationship('Notification', backref='user', lazy=True)
    ratings_given = db.relationship('SessionRating', foreign_keys='SessionRating.rater_id', backref='rater', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def get_profile_image_url(self):
        """Get user profile image URL or default avatar"""
        if self.profile_image:
            return f"/static/uploads/profiles/{self.profile_image}"
        return "/static/images/default-avatar.png"

    def get_role_display(self):
        """Get user role in Arabic"""
        role_map = {
            'admin': 'مدير النظام',
            'teacher': 'معلم',
            'student': 'طالب'
        }
        return role_map.get(self.role, self.role)

    def get_role_icon(self):
        """Get appropriate icon for user role"""
        icon_map = {
            'admin': 'fas fa-crown',
            'teacher': 'fas fa-chalkboard-teacher',
            'student': 'fas fa-user-graduate'
        }
        return icon_map.get(self.role, 'fas fa-user')

    def is_suspended(self):
        """Check if user is currently suspended"""
        if self.status != 'suspended':
            return False
        if self.suspension_end_date and self.suspension_end_date <= datetime.utcnow():
            # Suspension has expired, auto-approve user
            self.status = 'approved'
            self.suspension_end_date = None
            db.session.commit()
            return False
        return True

    def is_active_user(self):
        """Check if user can login (approved and not suspended/banned/deleted)"""
        if self.status == 'approved':
            return True
        elif self.status == 'suspended':
            return not self.is_suspended()
        return False

    def get_status_display(self):
        """Get human-readable status in Arabic"""
        status_map = {
            'pending': 'معلق',
            'approved': 'مقبول',
            'rejected': 'مرفوض',
            'suspended': 'محظور مؤقتاً',
            'banned': 'محظور نهائياً',
            'inactive': 'غير نشط',
            'deleted': 'محذوف'
        }
        return status_map.get(self.status, self.status)

    def get_login_message(self):
        """Get appropriate login message based on user status"""
        if self.status == 'pending':
            return 'حسابك قيد المراجعة. يرجى انتظار موافقة الإدارة.'
        elif self.status == 'rejected':
            return 'تم رفض حسابك. يرجى التواصل مع الإدارة.'
        elif self.status == 'suspended':
            if self.suspension_end_date:
                return f'حسابك محظور مؤقتاً حتى {self.suspension_end_date.strftime("%Y-%m-%d %H:%M")}. السبب: {self.ban_reason or "غير محدد"}'
            return f'حسابك محظور مؤقتاً. السبب: {self.ban_reason or "غير محدد"}'
        elif self.status == 'banned':
            return f'حسابك محظور نهائياً. السبب: {self.ban_reason or "غير محدد"}. يرجى التواصل مع الإدارة.'
        elif self.status == 'inactive':
            return 'حسابك غير نشط. يرجى التواصل مع الإدارة لإعادة تفعيله.'
        elif self.status == 'deleted':
            return 'هذا الحساب محذوف. يرجى التواصل مع الإدارة إذا كنت تعتقد أن هذا خطأ.'
        return 'حدث خطأ في حالة حسابك. يرجى التواصل مع الإدارة.'

    # Relationships
    notifications = db.relationship('Notification', back_populates='user', lazy=True)

    def __repr__(self):
        return f'<User {self.email}>'


class UserActionLog(db.Model):
    """Log all admin actions on user accounts"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    admin_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # suspended, banned, deleted, restored, etc.
    old_status = db.Column(db.String(20))
    new_status = db.Column(db.String(20))
    reason = db.Column(db.Text)
    suspension_end_date = db.Column(db.DateTime)  # For temporary suspensions
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='action_logs')
    admin = db.relationship('User', foreign_keys=[admin_id])

    def __repr__(self):
        return f'<UserActionLog {self.action} on {self.user.email}>'

class Package(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    duration_days = db.Column(db.Integer, nullable=False)  # Duration in days
    sessions_count = db.Column(db.Integer, nullable=False)
    session_duration = db.Column(db.Integer, default=60)  # Session duration in minutes
    features = db.Column(db.Text)  # JSON string of features
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    subscriptions = db.relationship('Subscription', backref='package', lazy=True)
    
    def __repr__(self):
        return f'<Package {self.name}>'

class Subscription(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    package_id = db.Column(db.Integer, db.ForeignKey('package.id'), nullable=False)
    status = db.Column(db.String(30), nullable=False, default='pending')  # pending, paid_pending_approval, active, expired, cancelled
    purchase_date = db.Column(db.DateTime, default=datetime.utcnow)
    approval_date = db.Column(db.DateTime)
    start_date = db.Column(db.DateTime)
    end_date = db.Column(db.DateTime)
    sessions_used = db.Column(db.Integer, default=0)
    sessions_remaining = db.Column(db.Integer)
    
    # Relationships
    payments = db.relationship('Payment', backref='subscription', lazy=True)
    sessions = db.relationship('Session', backref='subscription', lazy=True)
    
    def activate_subscription(self):
        self.status = 'active'
        self.approval_date = datetime.utcnow()
        self.start_date = datetime.utcnow()
        self.end_date = self.start_date + timedelta(days=self.package.duration_days)
        self.sessions_remaining = self.package.sessions_count

    def mark_as_paid_pending(self):
        """Mark subscription as paid but pending admin approval"""
        self.status = 'paid_pending_approval'

    @property
    def is_paid_pending(self):
        """Check if subscription is paid but pending approval"""
        return self.status == 'paid_pending_approval'
    
    @property
    def is_active(self):
        return self.status == 'active' and self.end_date > datetime.utcnow()
    
    def __repr__(self):
        return f'<Subscription {self.user.email} - {self.package.name}>'

class Session(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    session_type = db.Column(db.String(20), nullable=False)  # trial, makeup, scheduled
    scheduled_datetime = db.Column(db.DateTime, nullable=False)
    duration_minutes = db.Column(db.Integer, default=60)
    status = db.Column(db.String(20), nullable=False, default='scheduled')  # scheduled, completed, cancelled, missed
    teacher_attended = db.Column(db.Boolean, default=False)
    student_attended = db.Column(db.Boolean, default=False)
    notes = db.Column(db.Text)
    completion_notified = db.Column(db.Boolean, default=False)  # Track if completion notification was sent

    # Session Link Settings
    meeting_provider = db.Column(db.String(50), default='google_meet')  # google_meet, jitsi, zoom
    meeting_link = db.Column(db.String(500))  # Generated meeting link
    meeting_id = db.Column(db.String(100))  # Meeting ID for reference
    meeting_password = db.Column(db.String(50))  # Meeting password if required

    # Google Calendar Integration
    calendar_event_id = db.Column(db.String(255))  # Google Calendar Event ID

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # Relationships
    ratings = db.relationship('SessionRating', backref='session', lazy=True)
    
    @property
    def is_completed(self):
        return self.status == 'completed'

    def get_status_display(self):
        """Get human-readable status in Arabic"""
        status_map = {
            'scheduled': 'مجدولة',
            'completed': 'مكتملة',
            'cancelled': 'ملغية',
            'missed': 'فائتة'
        }
        return status_map.get(self.status, self.status)

    def get_session_type_display(self):
        """Get human-readable session type in Arabic"""
        type_map = {
            'trial': 'حصة تجريبية',
            'scheduled': 'حصة مجدولة',
            'makeup': 'حصة تعويضية'
        }
        return type_map.get(self.session_type, self.session_type)

    def get_meeting_provider_name(self):
        """Get human-readable meeting provider name"""
        provider_map = {
            'google_meet': 'Google Meet',
            'jitsi': 'Jitsi Meet',
            'zoom': 'Zoom'
        }
        return provider_map.get(self.meeting_provider, self.meeting_provider)
    
    def mark_completed(self):
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
        if self.subscription and self.session_type == 'scheduled':
            self.subscription.sessions_used += 1
            self.subscription.sessions_remaining -= 1

        # Send completion notification if not already sent
        if not self.completion_notified:
            self.send_completion_notification()
            self.completion_notified = True

    def send_completion_notification(self):
        """Send session completion notification"""
        try:
            from utils.session_email_notifications import send_session_completed_email
            success, message = send_session_completed_email(self)
            if success:
                print(f"✅ تم إرسال إشعار انتهاء الحصة {self.id}")
            else:
                print(f"❌ فشل إرسال إشعار انتهاء الحصة {self.id}: {message}")
        except Exception as e:
            print(f"❌ خطأ في إرسال إشعار انتهاء الحصة {self.id}: {str(e)}")

    def generate_meeting_link(self):
        """Generate meeting link based on provider settings"""
        if not self.meeting_link:
            if self.meeting_provider == 'google_meet':
                # Create Google Calendar event with Google Meet
                print(f"🔄 Creating Google Calendar event with Google Meet for session {self.id}")
                calendar_created = self.create_calendar_event()

                if calendar_created and self.calendar_event_id:
                    print(f"✅ Google Calendar event created: {self.calendar_event_id}")
                    if self.meeting_link:
                        print(f"✅ Meeting link generated and stored: {self.meeting_link}")
                    else:
                        print(f"⚠️ No meeting link generated, using fallback")
                        # Generate Jitsi link as fallback
                        from utils.session_link_generator import SessionLinkGenerator
                        self.meeting_link = SessionLinkGenerator.generate_jitsi_link(
                            self.id,
                            self.scheduled_datetime
                        )
                        print(f"✅ Generated Jitsi fallback: {self.meeting_link}")
                    print(f"📅 Calendar event ID: {self.calendar_event_id}")
                else:
                    # Calendar creation failed, use Jitsi
                    print("❌ Calendar creation failed, using Jitsi")
                    self.meeting_provider = 'jitsi'
                    from utils.session_link_generator import SessionLinkGenerator
                    self.meeting_link = SessionLinkGenerator.generate_jitsi_link(
                        self.id,
                        self.scheduled_datetime
                    )
            elif self.meeting_provider == 'jitsi':
                # Use traditional generator for Jitsi
                from utils.session_link_generator import SessionLinkGenerator
                self.meeting_link = SessionLinkGenerator.generate_jitsi_link(
                    self.id,
                    self.scheduled_datetime
                )
        return self.meeting_link

    def _get_calendar_meeting_link(self):
        """Get meeting link from Google Calendar event"""
        try:
            from models import CalendarSettings
            from utils.calendar_service import calendar_service

            calendar_settings = CalendarSettings.get_settings()
            if not calendar_settings.calendar_enabled:
                return None

            calendar_service.set_calendar_id(calendar_settings.calendar_id)
            return calendar_service.get_event_meeting_link(self.calendar_event_id)

        except Exception as e:
            print(f"Error getting calendar meeting link: {str(e)}")
            return None

    def _generate_google_meet_link_via_calendar(self):
        """Generate Google Meet link via Google Calendar"""
        try:
            from models import SessionSettings, CalendarSettings

            # Check if Google Calendar is enabled
            session_settings = SessionSettings.get_settings()
            calendar_settings = CalendarSettings.get_settings()

            if not session_settings.use_google_calendar or not calendar_settings.calendar_enabled:
                # Fallback: return placeholder that will be manually set
                return 'https://meet.google.com/new'

            # If calendar event exists, get link from it
            if self.calendar_event_id:
                from utils.calendar_service import calendar_service
                calendar_service.set_calendar_id(calendar_settings.calendar_id)
                meeting_link = calendar_service.get_event_meeting_link(self.calendar_event_id)
                if meeting_link:
                    return meeting_link

            # Return placeholder for manual setup
            return 'https://meet.google.com/new'

        except Exception as e:
            print(f"Error generating Google Meet link via calendar: {str(e)}")
            return 'https://meet.google.com/new'

    def get_meeting_provider_name(self):
        """Get display name for meeting provider"""
        from utils.session_link_generator import SessionLinkGenerator
        return SessionLinkGenerator.get_provider_display_name(self.meeting_provider)

    def create_calendar_event(self):
        """Create Google Calendar event for this session"""
        try:
            from utils.calendar_service import calendar_service
            from models import CalendarSettings

            print(f"🔍 بدء إنشاء Calendar event للحصة {self.id}")

            # Get calendar settings
            settings = CalendarSettings.get_settings()
            print(f"📋 Calendar enabled: {settings.calendar_enabled}")
            print(f"📋 Auto create events: {settings.auto_create_events}")
            print(f"📋 Calendar ID: {settings.calendar_id}")

            # Force enable if not enabled (for testing)
            if not settings.calendar_enabled:
                print("⚠️ Calendar disabled, but forcing creation for Google Meet")

            if not settings.auto_create_events:
                print("⚠️ Auto create disabled, but forcing creation for Google Meet")

            # Check calendar service availability
            if not calendar_service.is_available():
                print("❌ Calendar service not available")
                return False

            # Set calendar ID
            calendar_service.set_calendar_id(settings.calendar_id)
            print(f"✅ Calendar ID set: {settings.calendar_id}")

            # Create event
            print(f"🔄 إنشاء Calendar event...")
            event_id = calendar_service.create_event(self)
            print(f"📅 Event ID returned: {event_id}")

            if event_id:
                self.calendar_event_id = event_id
                print(f"✅ Calendar event created successfully: {event_id}")

                # Update meeting link from calendar event if Google Meet
                if self.meeting_provider == 'google_meet':
                    print(f"🔗 تحديث meeting link من Calendar...")
                    self._update_meeting_link_from_calendar()

                return True
            else:
                print("❌ لم يتم إرجاع event ID")
                return False

        except Exception as e:
            print(f"❌ Error creating calendar event: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def update_calendar_event(self):
        """Update Google Calendar event for this session"""
        try:
            from utils.calendar_service import calendar_service
            from models import CalendarSettings

            # Check if calendar integration is enabled
            settings = CalendarSettings.get_settings()
            if not settings.calendar_enabled or not settings.auto_update_events:
                return False

            # Check if event exists
            if not self.calendar_event_id:
                return self.create_calendar_event()

            # Set calendar ID
            calendar_service.set_calendar_id(settings.calendar_id)

            # Update event
            success = calendar_service.update_event(self.calendar_event_id, self)

            # Update meeting link from calendar event if Google Meet
            if success and self.meeting_provider == 'google_meet':
                self._update_meeting_link_from_calendar()

            return success

        except Exception as e:
            print(f"Error updating calendar event: {str(e)}")
            return False

    def delete_calendar_event(self):
        """Delete Google Calendar event for this session"""
        try:
            from utils.calendar_service import calendar_service
            from models import CalendarSettings

            # Check if calendar integration is enabled
            settings = CalendarSettings.get_settings()
            if not settings.calendar_enabled or not settings.auto_delete_events:
                return False

            # Check if event exists
            if not self.calendar_event_id:
                return True

            # Set calendar ID
            calendar_service.set_calendar_id(settings.calendar_id)

            # Delete event
            success = calendar_service.delete_event(self.calendar_event_id)
            if success:
                self.calendar_event_id = None
            return success

        except Exception as e:
            print(f"Error deleting calendar event: {str(e)}")
            return False

    def _update_meeting_link_from_calendar(self):
        """Update meeting link from Google Calendar event"""
        try:
            if not self.calendar_event_id:
                print("No calendar_event_id found")
                return False

            # Use the existing method
            calendar_link = self._get_calendar_meeting_link()
            if calendar_link and calendar_link != self.meeting_link:
                old_link = self.meeting_link
                self.meeting_link = calendar_link
                print(f"✅ Updated meeting link from calendar:")
                print(f"   Old: {old_link}")
                print(f"   New: {calendar_link}")
                return True

            print(f"⚠️ No meeting link found in calendar event {self.calendar_event_id}")
            return False

        except Exception as e:
            print(f"❌ Error updating meeting link from calendar: {str(e)}")
            return False

    @staticmethod
    def get_sessions_count_by_type_and_status(user_id, user_role, session_type=None, status=None):
        """Get count of sessions by type and status for a user"""
        query = Session.query

        if user_role == 'teacher':
            query = query.filter_by(teacher_id=user_id)
        elif user_role == 'student':
            query = query.filter_by(student_id=user_id)

        if session_type:
            query = query.filter_by(session_type=session_type)

        if status:
            query = query.filter_by(status=status)

        return query.count()

    @staticmethod
    def get_trial_sessions_count(user_id, user_role):
        """Get count of trial sessions"""
        return Session.get_sessions_count_by_type_and_status(user_id, user_role, 'trial')

    @staticmethod
    def get_makeup_sessions_count(user_id, user_role):
        """Get count of makeup sessions"""
        return Session.get_sessions_count_by_type_and_status(user_id, user_role, 'makeup')

    @staticmethod
    def get_cancelled_sessions_count(user_id, user_role):
        """Get count of cancelled sessions"""
        return Session.get_sessions_count_by_type_and_status(user_id, user_role, None, 'cancelled')

    @staticmethod
    def get_admin_sessions_count_by_type(session_type=None, status=None):
        """Get count of all sessions by type and status for admin"""
        query = Session.query

        if session_type:
            query = query.filter_by(session_type=session_type)

        if status:
            query = query.filter_by(status=status)

        return query.count()

    def __repr__(self):
        return f'<Session {self.session_type} - {self.scheduled_datetime}>'

class Payment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='SAR')
    payment_method = db.Column(db.String(50))  # stripe, paypal, etc.
    transaction_id = db.Column(db.String(255))
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, completed, failed, refunded
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    gateway_response = db.Column(db.Text)  # JSON response from payment gateway
    gateway = db.Column(db.String(50))  # Payment gateway used
    notes = db.Column(db.Text)  # Additional notes

    # Relationships
    user = db.relationship('User', backref='payments', lazy=True)

    @staticmethod
    def get_total_revenue():
        """Calculate total revenue from completed payments and active subscriptions"""
        from sqlalchemy import func

        # Try to get revenue from completed payments first
        revenue_from_payments = db.session.query(func.sum(Payment.amount)).filter_by(status='completed').scalar() or 0

        # If payments don't have amounts, calculate from active subscriptions
        if revenue_from_payments == 0:
            revenue_from_subscriptions = db.session.query(
                func.sum(Package.price)
            ).join(Subscription).filter(
                Subscription.status.in_(['active', 'paid_pending_approval'])
            ).scalar() or 0
            return revenue_from_subscriptions

        return revenue_from_payments

    @staticmethod
    def get_monthly_revenue(month_start=None):
        """Calculate monthly revenue from completed payments and active subscriptions"""
        from sqlalchemy import func
        from datetime import datetime

        if month_start is None:
            month_start = datetime.now().replace(day=1)

        # Try to get revenue from completed payments first
        revenue_from_payments = db.session.query(func.sum(Payment.amount)).filter(
            Payment.status == 'completed',
            Payment.payment_date >= month_start
        ).scalar() or 0

        # If payments don't have amounts, calculate from subscriptions activated this month
        if revenue_from_payments == 0:
            revenue_from_subscriptions = db.session.query(
                func.sum(Package.price)
            ).join(Subscription).filter(
                Subscription.status.in_(['active', 'paid_pending_approval']),
                Subscription.approval_date >= month_start
            ).scalar() or 0
            return revenue_from_subscriptions

        return revenue_from_payments

    def __repr__(self):
        return f'<Payment {self.amount} {self.currency} - {self.status}>'

class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50))  # session, payment, system, user_management, etc.
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    is_read = db.Column(db.Boolean, default=False)
    action_url = db.Column(db.String(500))  # URL to redirect when clicked
    icon = db.Column(db.String(50))  # FontAwesome icon class
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)

    # Relationships
    user = db.relationship('User', back_populates='notifications')

    def __repr__(self):
        return f'<Notification {self.title}>'

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = datetime.utcnow()
            db.session.commit()

    def to_dict(self):
        """Convert notification to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'notification_type': self.notification_type,
            'priority': self.priority,
            'is_read': self.is_read,
            'action_url': self.action_url,
            'icon': self.icon,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'time_ago': self.get_time_ago()
        }

    def get_time_ago(self):
        """Get human readable time ago"""
        if not self.created_at:
            return 'غير محدد'

        now = datetime.utcnow()
        diff = now - self.created_at

        if diff.days > 0:
            return f'منذ {diff.days} يوم'
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f'منذ {hours} ساعة'
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f'منذ {minutes} دقيقة'
        else:
            return 'منذ لحظات'

    def get_priority_class(self):
        """Get CSS class for priority"""
        priority_classes = {
            'low': 'text-muted',
            'normal': 'text-primary',
            'high': 'text-warning',
            'urgent': 'text-danger'
        }
        return priority_classes.get(self.priority, 'text-primary')

    def get_icon_class(self):
        """Get icon class with fallback"""
        if self.icon:
            return self.icon

        # Default icons based on notification type
        type_icons = {
            'session': 'fas fa-calendar',
            'payment': 'fas fa-credit-card',
            'system': 'fas fa-cog',
            'user_management': 'fas fa-users',
            'subscription': 'fas fa-star',
            'security': 'fas fa-shield-alt'
        }
        return type_icons.get(self.notification_type, 'fas fa-bell')

    @classmethod
    def get_unread_count_for_user(cls, user_id):
        """Get count of unread notifications for user"""
        return cls.query.filter_by(user_id=user_id, is_read=False).count()

    @classmethod
    def get_recent_for_user(cls, user_id, limit=10):
        """Get recent notifications for user"""
        return cls.query.filter_by(user_id=user_id).order_by(
            cls.created_at.desc()
        ).limit(limit).all()

    @classmethod
    def create_notification(cls, user_id, title, message, notification_type='system',
                          priority='normal', action_url=None, icon=None):
        """Create a new notification"""
        notification = cls(
            user_id=user_id,
            title=title,
            message=message,
            notification_type=notification_type,
            priority=priority,
            action_url=action_url,
            icon=icon
        )
        db.session.add(notification)
        db.session.commit()
        return notification

class SessionRating(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('session.id'), nullable=False)
    rater_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # Who gave the rating
    rating = db.Column(db.Integer, nullable=False)  # 1-5 stars
    comment = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<SessionRating {self.rating}/5>'

class AcademySettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    academy_name = db.Column(db.String(200), nullable=False, default='أكاديمية القرآن الكريم')
    academy_slogan = db.Column(db.String(500), default='نحو تعلم أفضل للقرآن الكريم')
    academy_logo = db.Column(db.String(255))
    academy_favicon = db.Column(db.String(255))
    academy_description = db.Column(db.Text)
    contact_email = db.Column(db.String(120))
    contact_phone = db.Column(db.String(20))
    contact_whatsapp = db.Column(db.String(20))
    address = db.Column(db.Text)
    website_url = db.Column(db.String(255))
    facebook_url = db.Column(db.String(255))
    twitter_url = db.Column(db.String(255))
    instagram_url = db.Column(db.String(255))
    youtube_url = db.Column(db.String(255))
    primary_color = db.Column(db.String(7), default='#007bff')
    secondary_color = db.Column(db.String(7), default='#6c757d')
    success_color = db.Column(db.String(7), default='#28a745')
    danger_color = db.Column(db.String(7), default='#dc3545')
    warning_color = db.Column(db.String(7), default='#ffc107')
    info_color = db.Column(db.String(7), default='#17a2b8')
    timezone = db.Column(db.String(50), default='Asia/Riyadh')
    currency = db.Column(db.String(3), default='SAR')
    language = db.Column(db.String(5), default='ar')
    date_format = db.Column(db.String(20), default='%Y-%m-%d')
    time_format = db.Column(db.String(20), default='%H:%M')
    security_settings = db.Column(db.Text)  # JSON string for security configurations
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_logo_url(self):
        """Get academy logo URL with fallback"""
        return self.academy_logo or '/static/images/logo.png'

    def get_favicon_url(self):
        """Get academy favicon URL with fallback"""
        return self.academy_favicon or '/static/images/favicon.ico'

    def get_social_links(self):
        """Get all social media links"""
        return {
            'facebook': self.facebook_url,
            'twitter': self.twitter_url,
            'instagram': self.instagram_url,
            'youtube': self.youtube_url
        }

    def get_contact_info(self):
        """Get all contact information"""
        return {
            'email': self.contact_email,
            'phone': self.contact_phone,
            'whatsapp': self.contact_whatsapp,
            'address': self.address,
            'website': self.website_url
        }

    def get_theme_colors(self):
        """Get all theme colors"""
        return {
            'primary': self.primary_color,
            'secondary': self.secondary_color,
            'success': self.success_color,
            'danger': self.danger_color,
            'warning': self.warning_color,
            'info': self.info_color
        }

    def to_dict(self):
        """Convert settings to dictionary for templates"""
        return {
            'academy_name': self.academy_name,
            'academy_slogan': self.academy_slogan,
            'academy_description': self.academy_description,
            'academy_logo': self.get_logo_url(),
            'academy_favicon': self.get_favicon_url(),
            'contact_email': self.contact_email,
            'contact_phone': self.contact_phone,
            'contact_whatsapp': self.contact_whatsapp,
            'address': self.address,
            'website_url': self.website_url,
            'social_links': self.get_social_links(),
            'contact_info': self.get_contact_info(),
            'theme_colors': self.get_theme_colors(),
            'timezone': self.timezone,
            'currency': self.currency,
            'language': self.language,
            'date_format': self.date_format,
            'time_format': self.time_format
        }

    def __repr__(self):
        return f'<AcademySettings {self.academy_name}>'

class PaymentGateway(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)  # stripe, paypal, etc.
    is_active = db.Column(db.Boolean, default=False)
    api_key = db.Column(db.String(255))
    secret_key = db.Column(db.String(255))
    webhook_url = db.Column(db.String(255))
    configuration = db.Column(db.Text)  # JSON configuration
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<PaymentGateway {self.name}>'

class EmailSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    enabled = db.Column(db.Boolean, default=False)  # Email system enabled/disabled
    smtp_server = db.Column(db.String(255))
    smtp_port = db.Column(db.Integer, default=587)
    smtp_username = db.Column(db.String(255))
    smtp_password = db.Column(db.String(255))
    use_tls = db.Column(db.Boolean, default=True)
    use_ssl = db.Column(db.Boolean, default=False)  # SSL support
    default_sender = db.Column(db.String(255))

    # Email templates
    welcome_email_enabled = db.Column(db.Boolean, default=True)
    session_reminder_enabled = db.Column(db.Boolean, default=True)
    payment_confirmation_enabled = db.Column(db.Boolean, default=True)
    subscription_approval_enabled = db.Column(db.Boolean, default=True)
    user_management_notifications_enabled = db.Column(db.Boolean, default=True)
    subscription_purchase_enabled = db.Column(db.Boolean, default=True)
    subscription_expiry_enabled = db.Column(db.Boolean, default=True)
    password_reset_enabled = db.Column(db.Boolean, default=True)

    # Session Reminder Settings
    reminder_enabled = db.Column(db.Boolean, default=True)
    reminder_hours_before = db.Column(db.Integer, default=24)
    reminder_minutes_before = db.Column(db.Integer, default=30)
    reminder_auto_send = db.Column(db.Boolean, default=True)
    reminder_include_teacher_info = db.Column(db.Boolean, default=True)
    reminder_include_session_notes = db.Column(db.Boolean, default=False)
    preferred_reminder_time = db.Column(db.Integer, default=9)  # Hour of day (0-23)

    # User Management Notification Settings
    new_user_registration_notification = db.Column(db.Boolean, default=True)
    user_profile_update_notification = db.Column(db.Boolean, default=False)
    user_status_change_notification = db.Column(db.Boolean, default=True)
    subscription_status_change_notification = db.Column(db.Boolean, default=True)
    payment_received_notification = db.Column(db.Boolean, default=True)
    session_completion_notification = db.Column(db.Boolean, default=False)
    admin_daily_summary = db.Column(db.Boolean, default=False)
    admin_weekly_report = db.Column(db.Boolean, default=False)

    # Admin Notification Settings (New)
    admin_payment_notification_enabled = db.Column(db.Boolean, default=True)
    admin_new_user_notification_enabled = db.Column(db.Boolean, default=True)
    admin_session_booking_notification = db.Column(db.Boolean, default=False)
    admin_subscription_expiry_notification = db.Column(db.Boolean, default=True)

    # Session Notification Settings (New)
    trial_session_notification_enabled = db.Column(db.Boolean, default=True)
    makeup_session_notification_enabled = db.Column(db.Boolean, default=True)
    session_deletion_notification_enabled = db.Column(db.Boolean, default=True)

    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class EmailTemplate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    display_name = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.String(500), nullable=False)
    body_html = db.Column(db.Text, nullable=False)
    body_text = db.Column(db.Text)
    template_type = db.Column(db.String(50), nullable=False)  # welcome, reminder, payment, etc.
    is_active = db.Column(db.Boolean, default=True)
    is_system = db.Column(db.Boolean, default=False)  # System templates can't be deleted
    variables = db.Column(db.Text)  # JSON string of available variables
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<EmailTemplate {self.name}>'

    def get_variables_list(self):
        """Get list of available variables for this template"""
        import json
        if self.variables:
            try:
                return json.loads(self.variables)
            except:
                return []
        return []

    def set_variables_list(self, variables_list):
        """Set list of available variables for this template"""
        import json
        self.variables = json.dumps(variables_list)


class EmailLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    recipient_email = db.Column(db.String(255), nullable=False)
    subject = db.Column(db.String(500), nullable=False)
    template_name = db.Column(db.String(100))
    status = db.Column(db.String(20), default='pending')  # pending, sent, failed
    error_message = db.Column(db.Text)
    sent_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<EmailLog {self.recipient_email} - {self.status}>'


class SessionReminder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('session.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    reminder_type = db.Column(db.String(20), nullable=False)  # creation, 1_day_before, 5_minutes_before
    sent_at = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='pending')  # pending, sent, failed
    error_message = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    session = db.relationship('Session', backref='reminders')
    user = db.relationship('User', backref='session_reminders')

    def __repr__(self):
        return f'<SessionReminder {self.reminder_type} for Session {self.session_id}>'

    def __repr__(self):
        return f'<EmailSettings {self.smtp_server}>'

class FinancialReport(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    report_date = db.Column(db.Date, nullable=False)
    total_revenue = db.Column(db.Float, default=0.0)
    total_payments = db.Column(db.Integer, default=0)
    pending_payments = db.Column(db.Float, default=0.0)
    refunded_amount = db.Column(db.Float, default=0.0)
    new_subscriptions = db.Column(db.Integer, default=0)
    active_subscriptions = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<FinancialReport {self.report_date}>'

class UserNotificationSettings(db.Model):
    """User notification preferences"""
    __tablename__ = 'user_notification_settings'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)

    # Email notifications
    email_notifications = db.Column(db.Boolean, default=True)
    session_reminders = db.Column(db.Boolean, default=True)
    payment_notifications = db.Column(db.Boolean, default=True)

    # Admin specific notifications
    system_alerts = db.Column(db.Boolean, default=True)
    user_activity_alerts = db.Column(db.Boolean, default=True)

    # Teacher specific notifications
    student_notifications = db.Column(db.Boolean, default=True)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('notification_settings', uselist=False))

    def __repr__(self):
        return f'<UserNotificationSettings {self.user_id}>'

    @classmethod
    def get_or_create_for_user(cls, user_id):
        """Get existing settings or create default ones for user"""
        settings = cls.query.filter_by(user_id=user_id).first()
        if not settings:
            settings = cls(user_id=user_id)
            db.session.add(settings)
            db.session.commit()
        return settings

class SessionSettings(db.Model):
    __tablename__ = 'session_settings'

    id = db.Column(db.Integer, primary_key=True)

    # Provider Settings
    default_provider = db.Column(db.String(50), default='google_meet')  # google_meet, jitsi
    auto_generate_links = db.Column(db.Boolean, default=True)

    # Google Meet Settings (via Google Calendar)
    use_google_calendar = db.Column(db.Boolean, default=True)

    # Jitsi Meet Settings
    jitsi_enabled = db.Column(db.Boolean, default=True)
    jitsi_domain = db.Column(db.String(100), default='meet.jit.si')
    jitsi_prefix = db.Column(db.String(50), default='academy')
    jitsi_password_required = db.Column(db.Boolean, default=False)

    # Link Generation Settings
    link_format = db.Column(db.String(20), default='short')  # short, long, custom
    include_session_id = db.Column(db.Boolean, default=True)
    include_date = db.Column(db.Boolean, default=True)
    include_time = db.Column(db.Boolean, default=False)

    # Security Settings
    require_password = db.Column(db.Boolean, default=False)
    waiting_room = db.Column(db.Boolean, default=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<SessionSettings {self.default_provider}>'

    @staticmethod
    def get_settings():
        """Get session settings, create default if not exists"""
        settings = SessionSettings.query.first()
        if not settings:
            settings = SessionSettings()
            db.session.add(settings)
            db.session.commit()
        return settings


class CalendarSettings(db.Model):
    __tablename__ = 'calendar_settings'

    id = db.Column(db.Integer, primary_key=True)

    # Google Calendar Settings
    calendar_enabled = db.Column(db.Boolean, default=False)
    calendar_id = db.Column(db.String(255))  # Google Calendar ID
    service_account_email = db.Column(db.String(255))
    # service_account_file = db.Column(db.String(255), default='quranlms-calendar-service-account.json')

    # Event Settings
    auto_create_events = db.Column(db.Boolean, default=True)
    auto_update_events = db.Column(db.Boolean, default=True)
    auto_delete_events = db.Column(db.Boolean, default=True)

    # Event Details
    event_title_template = db.Column(db.String(255), default='حصة قرآن - {student_name} مع {teacher_name}')
    event_description_template = db.Column(db.Text, default='حصة تحفيظ القرآن الكريم\nالطالب: {student_name}\nالمعلم: {teacher_name}\nنوع الحصة: {session_type}')

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @staticmethod
    def get_settings():
        """Get calendar settings, create default if not exists"""
        settings = CalendarSettings.query.first()
        if not settings:
            settings = CalendarSettings()
            db.session.add(settings)
            db.session.commit()
        return settings

    def __repr__(self):
        return f'<CalendarSettings {self.calendar_id}>'
