"""
Context processors for making academy settings available globally in templates
"""

from models import AcademySettings
from flask import g


def get_academy_settings():
    """Get academy settings with caching"""
    if not hasattr(g, 'academy_settings'):
        try:
            g.academy_settings = AcademySettings.query.first()
            if not g.academy_settings:
                # Create default settings if none exist
                g.academy_settings = AcademySettings()
        except Exception as e:
            # Handle database schema issues gracefully
            print(f"Error loading academy settings: {e}")
            g.academy_settings = None
    return g.academy_settings


def academy_context():
    """Context processor to inject academy settings into all templates"""
    settings = get_academy_settings()
    
    # Base academy variables
    context = {
        'academy': settings.to_dict() if settings else {},
        'academy_name': settings.academy_name if settings else 'أكاديمية القرآن الكريم',
        'academy_slogan': settings.academy_slogan if settings else 'نحو تعلم أفضل للقرآن الكريم',
        'academy_logo': settings.get_logo_url() if settings else '/static/images/logo.png',
        'academy_favicon': settings.get_favicon_url() if settings else '/static/images/favicon.ico',
    }
    
    # Add individual settings for backward compatibility
    if settings:
        context.update({
            'academy_description': settings.academy_description,
            'academy_email': settings.contact_email,
            'academy_phone': settings.contact_phone,
            'academy_whatsapp': settings.contact_whatsapp,
            'academy_address': settings.address,
            'academy_website': settings.website_url,
            'academy_facebook': settings.facebook_url,
            'academy_twitter': settings.twitter_url,
            'academy_instagram': settings.instagram_url,
            'academy_youtube': settings.youtube_url,
            'primary_color': settings.primary_color,
            'secondary_color': settings.secondary_color,
            'success_color': settings.success_color,
            'danger_color': settings.danger_color,
            'warning_color': settings.warning_color,
            'info_color': settings.info_color,
            'academy_timezone': settings.timezone,
            'academy_currency': settings.currency,
            'academy_language': settings.language,
            'academy_date_format': settings.date_format,
            'academy_time_format': settings.time_format,
        })
    
    return context


def theme_context():
    """Context processor for theme-related variables"""
    settings = get_academy_settings()
    
    if not settings:
        return {
            'theme_colors': {
                'primary': '#007bff',
                'secondary': '#6c757d',
                'success': '#28a745',
                'danger': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8'
            }
        }
    
    return {
        'theme_colors': settings.get_theme_colors()
    }


def contact_context():
    """Context processor for contact information"""
    settings = get_academy_settings()
    
    if not settings:
        return {
            'contact_info': {
                'email': None,
                'phone': None,
                'whatsapp': None,
                'address': None,
                'website': None
            },
            'social_links': {
                'facebook': None,
                'twitter': None,
                'instagram': None,
                'youtube': None
            }
        }
    
    return {
        'contact_info': settings.get_contact_info(),
        'social_links': settings.get_social_links()
    }


def format_helpers():
    """Context processor for formatting helpers"""
    settings = get_academy_settings()
    
    def format_currency(amount, show_decimals=True):
        """Format currency based on academy settings"""
        if amount is None:
            amount = 0

        currency = settings.currency if settings else 'SAR'

        # تحديد عدد الخانات العشرية
        decimal_places = 2 if show_decimals else 0

        # رموز العملات
        currency_symbols = {
            'SAR': 'ريال',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'EGP': 'ج.م',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'OMR': 'ر.ع',
            'BHD': 'د.ب',
            'JOD': 'د.أ'
        }

        symbol = currency_symbols.get(currency, currency)

        # تنسيق المبلغ
        if currency in ['USD', 'EUR']:
            # العملات الأجنبية - الرمز قبل المبلغ
            return f"{symbol}{amount:.{decimal_places}f}"
        else:
            # العملات العربية - الرمز بعد المبلغ
            return f"{amount:.{decimal_places}f} {symbol}"

    def get_currency_code():
        """Get current currency code"""
        return settings.currency if settings else 'SAR'

    def get_currency_symbol():
        """Get current currency symbol"""
        currency = settings.currency if settings else 'SAR'
        currency_symbols = {
            'SAR': 'ريال',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'EGP': 'ج.م',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'OMR': 'ر.ع',
            'BHD': 'د.ب',
            'JOD': 'د.أ'
        }
        return currency_symbols.get(currency, currency)
    
    def format_date(date_obj):
        """Format date based on academy settings"""
        if not date_obj:
            return ''
        date_format = settings.date_format if settings else '%Y-%m-%d'
        return date_obj.strftime(date_format)
    
    def format_time(time_obj):
        """Format time based on academy settings"""
        if not time_obj:
            return ''
        time_format = settings.time_format if settings else '%H:%M'
        return time_obj.strftime(time_format)
    
    def format_datetime(datetime_obj):
        """Format datetime based on academy settings"""
        if not datetime_obj:
            return ''
        date_format = settings.date_format if settings else '%Y-%m-%d'
        time_format = settings.time_format if settings else '%H:%M'
        return datetime_obj.strftime(f"{date_format} {time_format}")
    
    return {
        'format_currency': format_currency,
        'get_currency_code': get_currency_code,
        'get_currency_symbol': get_currency_symbol,
        'format_date': format_date,
        'format_time': format_time,
        'format_datetime': format_datetime
    }


def csrf_context():
    """Context processor for CSRF token"""
    from flask_wtf.csrf import generate_csrf
    try:
        return {
            'csrf_token': generate_csrf
        }
    except Exception:
        return {
            'csrf_token': lambda: ''
        }


def register_context_processors(app):
    """Register all context processors with the Flask app"""
    app.context_processor(academy_context)
    app.context_processor(theme_context)
    app.context_processor(contact_context)
    app.context_processor(format_helpers)
    app.context_processor(csrf_context)
