<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - أكاديمية القرآن الكريم</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome - Multiple Sources for Better Loading -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Fallback Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css">
    <!-- Bootstrap Icons as Additional Fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        /* Font Loading and Icon Fix */
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

        /* Ensure Font Awesome Icons Load Properly */
        .fas, .far, .fab, .fal, .fad, .fa {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro" !important;
            font-weight: 900 !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Fallback for Icons */
        i[class*="fa-"]:before {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
        }

        /* Tajawal Font Application */
        * {
            font-family: 'Tajawal', sans-serif !important;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .reset-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            position: relative;
        }

        .reset-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
        }

        .reset-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }

        .icon-container {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            box-shadow: 0 10px 20px rgba(0,123,255,0.3);
        }

        .icon-container i {
            font-size: 2rem;
            color: white;
        }

        .reset-form {
            padding: 2.5rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.25rem rgba(0,123,255,0.15);
            background-color: white;
        }

        .form-floating > label {
            padding: 1rem;
            font-weight: 500;
            color: #6c757d;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,123,255,0.4);
        }

        .btn-outline-primary {
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: #007bff;
            transform: translateY(-1px);
        }

        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .reset-footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }

        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }

        .security-note {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #004085;
        }

        @media (max-width: 576px) {
            .reset-container {
                margin: 10px;
                border-radius: 15px;
            }

            .reset-header, .reset-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <div class="icon-container">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h2 class="fw-bold text-primary mb-2">إعادة تعيين كلمة المرور</h2>
            <p class="text-muted mb-0">أدخل كلمة المرور الجديدة لحسابك</p>
        </div>

        <div class="reset-form">
            <!-- Security Note -->
            <div class="security-note">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة أمنية:</strong> تأكد من اختيار كلمة مرور قوية تحتوي على أحرف وأرقام ورموز.
            </div>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'danger' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" id="resetForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password"
                           placeholder="كلمة المرور الجديدة" required minlength="6">
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>كلمة المرور الجديدة
                    </label>
                    <div class="password-strength" id="passwordStrength"></div>
                </div>

                <div class="form-floating">
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                           placeholder="تأكيد كلمة المرور" required minlength="6">
                    <label for="confirm_password">
                        <i class="fas fa-lock me-2"></i>تأكيد كلمة المرور الجديدة
                    </label>
                    <div class="invalid-feedback" id="confirmFeedback"></div>
                </div>

                <div class="d-grid mb-3">
                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                        <i class="fas fa-save me-2"></i>حفظ كلمة المرور الجديدة
                    </button>
                </div>
            </form>
        </div>

        <div class="reset-footer">
            <p class="mb-2 text-muted">تذكرت كلمة المرور؟</p>
            <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right me-2"></i>العودة لتسجيل الدخول
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Ensure Font Awesome Icons Load Properly
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Font Awesome is loaded
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-test';
            testIcon.style.position = 'absolute';
            testIcon.style.left = '-9999px';
            document.body.appendChild(testIcon);

            const computedStyle = window.getComputedStyle(testIcon, ':before');
            const fontFamily = computedStyle.getPropertyValue('font-family');

            if (!fontFamily.includes('Font Awesome')) {
                console.warn('Font Awesome not loaded properly, using fallback');
                // Add fallback CSS
                const fallbackCSS = document.createElement('style');
                fallbackCSS.textContent = `
                    .fas, .far, .fab, .fal, .fad, .fa {
                        font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome" !important;
                        font-weight: 900 !important;
                    }
                `;
                document.head.appendChild(fallbackCSS);
            }

            document.body.removeChild(testIcon);
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm_password');
            const passwordStrength = document.getElementById('passwordStrength');
            const confirmFeedback = document.getElementById('confirmFeedback');
            const submitBtn = document.getElementById('submitBtn');

            // Password strength checker
            function checkPasswordStrength(password) {
                let strength = 0;
                let feedback = '';

                if (password.length >= 8) strength++;
                if (/[a-z]/.test(password)) strength++;
                if (/[A-Z]/.test(password)) strength++;
                if (/[0-9]/.test(password)) strength++;
                if (/[^A-Za-z0-9]/.test(password)) strength++;

                switch (strength) {
                    case 0:
                    case 1:
                        feedback = '<span class="strength-weak"><i class="fas fa-times-circle me-1"></i>ضعيفة جداً</span>';
                        break;
                    case 2:
                        feedback = '<span class="strength-weak"><i class="fas fa-exclamation-circle me-1"></i>ضعيفة</span>';
                        break;
                    case 3:
                        feedback = '<span class="strength-medium"><i class="fas fa-minus-circle me-1"></i>متوسطة</span>';
                        break;
                    case 4:
                        feedback = '<span class="strength-strong"><i class="fas fa-check-circle me-1"></i>قوية</span>';
                        break;
                    case 5:
                        feedback = '<span class="strength-strong"><i class="fas fa-shield-alt me-1"></i>قوية جداً</span>';
                        break;
                }

                return feedback;
            }

            // Validate passwords match
            function validatePasswords() {
                if (password.value && confirmPassword.value) {
                    if (password.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('كلمات المرور غير متطابقة');
                        confirmFeedback.textContent = 'كلمات المرور غير متطابقة';
                        confirmFeedback.style.display = 'block';
                        submitBtn.disabled = true;
                    } else {
                        confirmPassword.setCustomValidity('');
                        confirmFeedback.style.display = 'none';
                        submitBtn.disabled = false;
                    }
                }
            }

            // Event listeners
            password.addEventListener('input', function() {
                passwordStrength.innerHTML = checkPasswordStrength(this.value);
                validatePasswords();
            });

            confirmPassword.addEventListener('input', validatePasswords);

            // Auto-hide alerts
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 8000);
        });
    </script>
</body>
</html>
