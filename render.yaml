services:
  - type: web
    name: quranlms
    env: python
    buildCommand: "chmod +x build.sh && ./build.sh"
    startCommand: "gunicorn app:app"
    envVars:
      - key: FLASK_APP
        value: app.py
      - key: FLASK_ENV
        value: production
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: quranlms-db
          property: connectionString
    
databases:
  - name: quranlms-db
    databaseName: quranlms
    user: quranlms_user
