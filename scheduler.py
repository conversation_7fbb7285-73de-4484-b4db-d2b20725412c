#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مجدول المهام التلقائي للنظام
يتم تشغيله كـ cron job في الإنتاج
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from utils.session_reminder_scheduler import run_all_reminder_jobs, run_immediate_reminder_job
# from utils.subscription_checker import check_expiring_subscriptions
import schedule
import time
from datetime import datetime

def daily_tasks():
    """المهام اليومية"""
    with app.app_context():
        print(f"🌅 بدء المهام اليومية - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # تذكيرات الحصص اليومية
            reminder_results = run_all_reminder_jobs()
            
            # فحص الاشتراكات المنتهية (يمكن إضافته لاحقاً)
            # subscription_results = check_expiring_subscriptions()

            print(f"✅ انتهت المهام اليومية بنجاح")
            print(f"   📧 تذكيرات: {reminder_results}")
            # print(f"   📋 اشتراكات: {subscription_results}")
            
        except Exception as e:
            print(f"❌ خطأ في المهام اليومية: {str(e)}")

def immediate_tasks():
    """المهام الفورية (كل دقيقة)"""
    with app.app_context():
        try:
            # تذكيرات الحصص الفورية (قبل 5 دقائق)
            count = run_immediate_reminder_job()
            if count > 0:
                print(f"⚡ تم إرسال {count} تذكير فوري - {datetime.now().strftime('%H:%M:%S')}")
                
        except Exception as e:
            print(f"❌ خطأ في المهام الفورية: {str(e)}")

def setup_scheduler():
    """إعداد المجدول"""
    print("⚙️ إعداد مجدول المهام...")
    
    # المهام اليومية (كل يوم في الساعة 9:00 صباحاً)
    schedule.every().day.at("09:00").do(daily_tasks)
    
    # المهام الفورية (كل دقيقة)
    schedule.every().minute.do(immediate_tasks)
    
    print("✅ تم إعداد المجدول:")
    print("   📅 المهام اليومية: كل يوم في 9:00 صباحاً")
    print("   ⚡ المهام الفورية: كل دقيقة")

def run_scheduler():
    """تشغيل المجدول"""
    setup_scheduler()
    
    print("🚀 بدء تشغيل المجدول...")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف المجدول")

def run_once():
    """تشغيل المهام مرة واحدة للاختبار"""
    print("🧪 تشغيل المهام مرة واحدة للاختبار...")
    
    with app.app_context():
        # تشغيل المهام اليومية
        daily_tasks()
        
        # تشغيل المهام الفورية
        immediate_tasks()
    
    print("✅ انتهى الاختبار")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='مجدول المهام التلقائي')
    parser.add_argument('--once', action='store_true', help='تشغيل المهام مرة واحدة للاختبار')
    parser.add_argument('--daily', action='store_true', help='تشغيل المهام اليومية فقط')
    parser.add_argument('--immediate', action='store_true', help='تشغيل المهام الفورية فقط')
    
    args = parser.parse_args()
    
    if args.once:
        run_once()
    elif args.daily:
        with app.app_context():
            daily_tasks()
    elif args.immediate:
        with app.app_context():
            immediate_tasks()
    else:
        run_scheduler()
